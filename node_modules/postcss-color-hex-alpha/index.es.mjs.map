{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport valueParser from 'postcss-values-parser';\n\nexport default postcss.plugin('postcss-color-hex-alpha', opts => {\n\t// whether to preserve the original hexa\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : false;\n\n\treturn root => {\n\t\t// for each declaration with a hexa\n\t\troot.walkDecls(decl => {\n\t\t\tif (hasAlphaHex(decl)) {\n\t\t\t\t// replace instances of hexa with rgba()\n\t\t\t\tconst ast = valueParser(decl.value).parse();\n\n\t\t\t\twalk(ast, node => {\n\t\t\t\t\tif (isAlphaHex(node)) {\n\t\t\t\t\t\tnode.replaceWith(hexa2rgba(node));\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// conditionally update the declaration\n\t\t\t\tconst modifiedValue = String(ast);\n\n\t\t\t\tif (decl.value !== modifiedValue) {\n\t\t\t\t\tif (preserve) {\n\t\t\t\t\t\tdecl.cloneBefore({ value: modifiedValue });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdecl.value = modifiedValue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n\n// match any hexa\nconst alphaHexRegExp = /#([0-9A-Fa-f]{4}(?:[0-9A-Fa-f]{4})?)\\b/;\n\n// whether a node has a hexa\nconst hasAlphaHex = node => alphaHexRegExp.test(node.value);\n\n// match an exact hexa\nconst alphaHexValueRegExp = /^#([0-9A-Fa-f]{4}(?:[0-9A-Fa-f]{4})?)$/;\n\n// walk all nodes in a value\nconst walk = (node, fn) => {\n\tif (Object(node.nodes).length) {\n\t\tnode.nodes.slice().forEach(child => {\n\t\t\tfn(child);\n\n\t\t\twalk(child, fn);\n\t\t});\n\t}\n};\n\n// decimal precision\nconst alphaDecimalPrecision = 100000;\n\n// match a hexa node\nconst isAlphaHex = node => node.type === 'word' && alphaHexValueRegExp.test(node.value);\n\nconst hexa2rgba = node => {\n\t// hex is the node value\n\tconst hex = node.value;\n\n\t// conditionally expand a hex\n\tconst hex8 = `0x${hex.length === 5 ? hex.slice(1).replace(/[0-9A-f]/g, '$&$&') : hex.slice(1)}`;\n\n\t// extract the red, blue, green, and alpha values from the hex\n\tconst [r, g, b, a] = [\n\t\tparseInt(hex8.slice(2, 4), 16),\n\t\tparseInt(hex8.slice(4, 6), 16),\n\t\tparseInt(hex8.slice(6, 8), 16),\n\t\tMath.round(parseInt(hex8.slice(8, 10), 16) / 255 * alphaDecimalPrecision) / alphaDecimalPrecision\n\t];\n\n\t// return a new rgba function, preserving the whitespace of the original node\n\tconst rgbaFunc = valueParser.func({ value: 'rgba', raws: Object.assign({}, node.raws) });\n\n\trgbaFunc.append(valueParser.paren({ value: '(' }));\n\trgbaFunc.append(valueParser.number({ value: r }));\n\trgbaFunc.append(valueParser.comma({ value: ',' }));\n\trgbaFunc.append(valueParser.number({ value: g }));\n\trgbaFunc.append(valueParser.comma({ value: ',' }));\n\trgbaFunc.append(valueParser.number({ value: b }));\n\trgbaFunc.append(valueParser.comma({ value: ',' }));\n\trgbaFunc.append(valueParser.number({ value: a }));\n\trgbaFunc.append(valueParser.paren({ value: ')' }));\n\n\treturn rgbaFunc;\n};\n"], "names": ["postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "hasAlphaHex", "ast", "valueParser", "value", "parse", "walk", "node", "isAlphaHex", "replaceWith", "hexa2rgba", "modifiedValue", "String", "cloneBefore", "alphaHexRegExp", "test", "alphaHexValueRegExp", "fn", "nodes", "length", "slice", "for<PERSON>ach", "child", "alphaDecimalPrecision", "type", "hex", "hex8", "replace", "parseInt", "Math", "round", "r", "g", "b", "a", "rgbaFunc", "func", "raws", "assign", "append", "paren", "number", "comma"], "mappings": ";;;AAGA,YAAeA,OAAO,CAACC,MAAR,CAAe,yBAAf,EAA0CC,IAAI,IAAI;;QAE1DC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,KAAvE;SAEOG,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeC,IAAI,IAAI;UAClBC,WAAW,CAACD,IAAD,CAAf,EAAuB;;cAEhBE,GAAG,GAAGC,WAAW,CAACH,IAAI,CAACI,KAAN,CAAX,CAAwBC,KAAxB,EAAZ;QAEAC,IAAI,CAACJ,GAAD,EAAMK,IAAI,IAAI;cACbC,UAAU,CAACD,IAAD,CAAd,EAAsB;YACrBA,IAAI,CAACE,WAAL,CAAiBC,SAAS,CAACH,IAAD,CAA1B;;SAFE,CAAJ,CAJsB;;cAWhBI,aAAa,GAAGC,MAAM,CAACV,GAAD,CAA5B;;YAEIF,IAAI,CAACI,KAAL,KAAeO,aAAnB,EAAkC;cAC7BhB,QAAJ,EAAc;YACbK,IAAI,CAACa,WAAL,CAAiB;cAAET,KAAK,EAAEO;aAA1B;WADD,MAEO;YACNX,IAAI,CAACI,KAAL,GAAaO,aAAb;;;;KAlBJ;GAFD;CAJc,CAAf;;AAiCA,MAAMG,cAAc,GAAG,wCAAvB;;AAGA,MAAMb,WAAW,GAAGM,IAAI,IAAIO,cAAc,CAACC,IAAf,CAAoBR,IAAI,CAACH,KAAzB,CAA5B;;;AAGA,MAAMY,mBAAmB,GAAG,wCAA5B;;AAGA,MAAMV,IAAI,GAAG,CAACC,IAAD,EAAOU,EAAP,KAAc;MACtBrB,MAAM,CAACW,IAAI,CAACW,KAAN,CAAN,CAAmBC,MAAvB,EAA+B;IAC9BZ,IAAI,CAACW,KAAL,CAAWE,KAAX,GAAmBC,OAAnB,CAA2BC,KAAK,IAAI;MACnCL,EAAE,CAACK,KAAD,CAAF;MAEAhB,IAAI,CAACgB,KAAD,EAAQL,EAAR,CAAJ;KAHD;;CAFF;;;AAWA,MAAMM,qBAAqB,GAAG,MAA9B;;AAGA,MAAMf,UAAU,GAAGD,IAAI,IAAIA,IAAI,CAACiB,IAAL,KAAc,MAAd,IAAwBR,mBAAmB,CAACD,IAApB,CAAyBR,IAAI,CAACH,KAA9B,CAAnD;;AAEA,MAAMM,SAAS,GAAGH,IAAI,IAAI;;QAEnBkB,GAAG,GAAGlB,IAAI,CAACH,KAAjB,CAFyB;;QAKnBsB,IAAI,GAAI,KAAID,GAAG,CAACN,MAAJ,KAAe,CAAf,GAAmBM,GAAG,CAACL,KAAJ,CAAU,CAAV,EAAaO,OAAb,CAAqB,WAArB,EAAkC,MAAlC,CAAnB,GAA+DF,GAAG,CAACL,KAAJ,CAAU,CAAV,CAAa,EAA9F,CALyB;;eAQJ,CACpBQ,QAAQ,CAACF,IAAI,CAACN,KAAL,CAAW,CAAX,EAAc,CAAd,CAAD,EAAmB,EAAnB,CADY,EAEpBQ,QAAQ,CAACF,IAAI,CAACN,KAAL,CAAW,CAAX,EAAc,CAAd,CAAD,EAAmB,EAAnB,CAFY,EAGpBQ,QAAQ,CAACF,IAAI,CAACN,KAAL,CAAW,CAAX,EAAc,CAAd,CAAD,EAAmB,EAAnB,CAHY,EAIpBS,IAAI,CAACC,KAAL,CAAWF,QAAQ,CAACF,IAAI,CAACN,KAAL,CAAW,CAAX,EAAc,EAAd,CAAD,EAAoB,EAApB,CAAR,GAAkC,GAAlC,GAAwCG,qBAAnD,IAA4EA,qBAJxD,CARI;QAQlBQ,CARkB;QAQfC,CARe;QAQZC,CARY;QAQTC,CARS;;QAgBnBC,QAAQ,GAAGhC,WAAW,CAACiC,IAAZ,CAAiB;IAAEhC,KAAK,EAAE,MAAT;IAAiBiC,IAAI,EAAEzC,MAAM,CAAC0C,MAAP,CAAc,EAAd,EAAkB/B,IAAI,CAAC8B,IAAvB;GAAxC,CAAjB;EAEAF,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACqC,KAAZ,CAAkB;IAAEpC,KAAK,EAAE;GAA3B,CAAhB;EACA+B,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACsC,MAAZ,CAAmB;IAAErC,KAAK,EAAE2B;GAA5B,CAAhB;EACAI,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACuC,KAAZ,CAAkB;IAAEtC,KAAK,EAAE;GAA3B,CAAhB;EACA+B,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACsC,MAAZ,CAAmB;IAAErC,KAAK,EAAE4B;GAA5B,CAAhB;EACAG,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACuC,KAAZ,CAAkB;IAAEtC,KAAK,EAAE;GAA3B,CAAhB;EACA+B,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACsC,MAAZ,CAAmB;IAAErC,KAAK,EAAE6B;GAA5B,CAAhB;EACAE,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACuC,KAAZ,CAAkB;IAAEtC,KAAK,EAAE;GAA3B,CAAhB;EACA+B,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACsC,MAAZ,CAAmB;IAAErC,KAAK,EAAE8B;GAA5B,CAAhB;EACAC,QAAQ,CAACI,MAAT,CAAgBpC,WAAW,CAACqC,KAAZ,CAAkB;IAAEpC,KAAK,EAAE;GAA3B,CAAhB;SAEO+B,QAAP;CA5BD;;;;"}