{"version": 3, "file": "index.cjs.js", "sources": ["src/lib/postcss-values-parser.js", "src/lib/is-ignored.js", "src/lib/get-custom-properties-from-root.js", "src/lib/get-custom-properties-from-imports.js", "src/lib/transform-value-ast.js", "src/lib/transform-properties.js", "src/lib/write-custom-properties-to-exports.js", "src/index.js"], "sourcesContent": ["import valueParser from 'postcss-values-parser';\n\nexport function parse (string) {\n\treturn valueParser(string).parse();\n}\n", "function isBlockIgnored(ruleOrDeclaration) {\n\tvar rule = ruleOrDeclaration.selector ?\n\t\truleOrDeclaration : ruleOrDeclaration.parent;\n\n\treturn /(!\\s*)?postcss-custom-properties:\\s*off\\b/i.test(rule.toString())\n}\n\nfunction isRuleIgnored(rule) {\n\tvar previous = rule.prev();\n\n\treturn Boolean(isBlockIgnored(rule) ||\n\t\tprevious &&\n\t\tprevious.type === 'comment' &&\n\t\t/(!\\s*)?postcss-custom-properties:\\s*ignore\\s+next\\b/i.test(previous.text));\n}\n\nexport {\n\tisBlockIgnored,\n\tisRuleIgnored\n}\n", "import { parse } from './postcss-values-parser';\nimport { isBlockIgnored } from './is-ignored';\n\n// return custom selectors from the css root, conditionally removing them\nexport default function getCustomPropertiesFromRoot(root, opts) {\n\t// initialize custom selectors\n\tconst customPropertiesFromHtmlElement = {};\n\tconst customPropertiesFromRootPseudo = {};\n\n\t// for each html or :root rule\n\troot.nodes.slice().forEach(rule => {\n\t\tconst customPropertiesObject = isHtmlRule(rule)\n\t\t\t? customPropertiesFromHtmlElement\n\t\t: isRootRule(rule)\n\t\t\t? customPropertiesFromRootPseudo\n\t\t: null;\n\n\t\t// for each custom property\n\t\tif (customPropertiesObject) {\n\t\t\trule.nodes.slice().forEach(decl => {\n\t\t\t\tif (isCustomDecl(decl) && !isBlockIgnored(decl)) {\n\t\t\t\t\tconst { prop } = decl;\n\n\t\t\t\t\t// write the parsed value to the custom property\n\t\t\t\t\tcustomPropertiesObject[prop] = parse(decl.value).nodes;\n\n\t\t\t\t\t// conditionally remove the custom property declaration\n\t\t\t\t\tif (!opts.preserve) {\n\t\t\t\t\t\tdecl.remove();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// conditionally remove the empty html or :root rule\n\t\t\tif (!opts.preserve && isEmptyParent(rule) && !isBlockIgnored(rule)) {\n\t\t\t\trule.remove();\n\t\t\t}\n\t\t}\n\t});\n\n\t// return all custom properties, preferring :root properties over html properties\n\treturn { ...customPropertiesFromHtmlElement, ...customPropertiesFromRootPseudo };\n}\n\n// match html and :root rules\nconst htmlSelectorRegExp = /^html$/i;\nconst rootSelectorRegExp = /^:root$/i;\nconst customPropertyRegExp = /^--[A-z][\\w-]*$/;\n\n// whether the node is an html or :root rule\nconst isHtmlRule = node => node.type === 'rule' && htmlSelectorRegExp.test(node.selector) && Object(node.nodes).length;\nconst isRootRule = node => node.type === 'rule' && rootSelectorRegExp.test(node.selector) && Object(node.nodes).length;\n\n// whether the node is an custom property\nconst isCustomDecl = node => node.type === 'decl' && customPropertyRegExp.test(node.prop);\n\n// whether the node is a parent without children\nconst isEmptyParent = node => Object(node.nodes).length === 0;\n", "import fs from 'fs';\nimport path from 'path';\nimport postcss from 'postcss';\nimport { parse } from './postcss-values-parser';\nimport getCustomPropertiesFromRoot from './get-custom-properties-from-root';\n\n/* Get Custom Properties from CSS File\n/* ========================================================================== */\n\nasync function getCustomPropertiesFromCSSFile(from) {\n\tconst css = await readFile(from);\n\tconst root = postcss.parse(css, { from });\n\n\treturn getCustomPropertiesFromRoot(root, { preserve: true });\n}\n\n/* Get Custom Properties from Object\n/* ========================================================================== */\n\nfunction getCustomPropertiesFromObject(object) {\n\tconst customProperties = Object.assign(\n\t\t{},\n\t\tObject(object).customProperties,\n\t\tObject(object)['custom-properties']\n\t);\n\n\tfor (const key in customProperties) {\n\t\tcustomProperties[key] = parse(String(customProperties[key])).nodes;\n\t}\n\n\treturn customProperties;\n}\n\n/* Get Custom Properties from JSON file\n/* ========================================================================== */\n\nasync function getCustomPropertiesFromJSONFile(from) {\n\tconst object = await readJSON(from);\n\n\treturn getCustomPropertiesFromObject(object);\n}\n\n/* Get Custom Properties from JS file\n/* ========================================================================== */\n\nasync function getCustomPropertiesFromJSFile(from) {\n\tconst object = await import(from);\n\n\treturn getCustomPropertiesFromObject(object);\n}\n\n/* Get Custom Properties from Imports\n/* ========================================================================== */\n\nexport default function getCustomPropertiesFromImports(sources) {\n\treturn sources.map(source => {\n\t\tif (source instanceof Promise) {\n\t\t\treturn source;\n\t\t} else if (source instanceof Function) {\n\t\t\treturn source();\n\t\t}\n\n\t\t// read the source as an object\n\t\tconst opts = source === Object(source) ? source : { from: String(source) };\n\n\t\t// skip objects with Custom Properties\n\t\tif (opts.customProperties || opts['custom-properties']) {\n\t\t\treturn opts\n\t\t}\n\n\t\t// source pathname\n\t\tconst from = path.resolve(String(opts.from || ''));\n\n\t\t// type of file being read from\n\t\tconst type = (opts.type || path.extname(from).slice(1)).toLowerCase();\n\n\t\treturn { type, from };\n\t}).reduce(async (customProperties, source) => {\n\t\tconst { type, from } = await source;\n\n\t\tif (type === 'css') {\n\t\t\treturn Object.assign(await customProperties, await getCustomPropertiesFromCSSFile(from));\n\t\t}\n\n\t\tif (type === 'js') {\n\t\t\treturn Object.assign(await customProperties, await getCustomPropertiesFromJSFile(from));\n\t\t}\n\n\t\tif (type === 'json') {\n\t\t\treturn Object.assign(await customProperties, await getCustomPropertiesFromJSONFile(from));\n\t\t}\n\n\t\treturn Object.assign(await customProperties, await getCustomPropertiesFromObject(await source));\n\t}, {});\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst readFile = from => new Promise((resolve, reject) => {\n\tfs.readFile(from, 'utf8', (error, result) => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve(result);\n\t\t}\n\t});\n});\n\nconst readJSON = async from => JSON.parse(await readFile(from));\n", "export default function transformValueAST(root, customProperties) {\n\tif (root.nodes && root.nodes.length) {\n\t\troot.nodes.slice().forEach(child => {\n\t\t\tif (isVarFunction(child)) {\n\t\t\t\t// eslint-disable-next-line no-unused-vars\n\t\t\t\tconst [propertyNode, comma, ...fallbacks] = child.nodes.slice(1, -1);\n\t\t\t\tconst { value: name } = propertyNode;\n\n\t\t\t\tif (name in Object(customProperties)) {\n\t\t\t\t\t// conditionally replace a known custom property\n\t\t\t\t\tconst nodes = asClonedArrayWithBeforeSpacing(customProperties[name], child.raws.before);\n\n\t\t\t\t\tchild.replaceWith(...nodes);\n\n\t\t\t\t\tretransformValueAST({ nodes }, customProperties, name);\n\t\t\t\t} else if (fallbacks.length) {\n\t\t\t\t\t// conditionally replace a custom property with a fallback\n\t\t\t\t\tconst index = root.nodes.indexOf(child);\n\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\troot.nodes.splice(index, 1, ...asClonedArrayWithBeforeSpacing(fallbacks, child.raws.before));\n\t\t\t\t\t}\n\n\t\t\t\t\ttransformValueAST(root, customProperties);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\ttransformValueAST(child, customProperties);\n\t\t\t}\n\t\t});\n\t}\n\n\treturn root;\n}\n\n// retransform the current ast without a custom property (to prevent recursion)\nfunction retransformValueAST(root, customProperties, withoutProperty) {\n\tconst nextCustomProperties = Object.assign({}, customProperties);\n\n\tdelete nextCustomProperties[withoutProperty];\n\n\treturn transformValueAST(root, nextCustomProperties);\n}\n\n// match var() functions\nconst varRegExp = /^var$/i;\n\n// whether the node is a var() function\nconst isVarFunction = node => node.type === 'func' && varRegExp.test(node.value) && Object(node.nodes).length > 0;\n\n// return an array with its nodes cloned, preserving the raw\nconst asClonedArrayWithBeforeSpacing = (array, beforeSpacing) => {\n\tconst clonedArray = asClonedArray(array, null);\n\n\tif (clonedArray[0]) {\n\t\tclonedArray[0].raws.before = beforeSpacing;\n\t}\n\n\treturn clonedArray;\n};\n\n// return an array with its nodes cloned\nconst asClonedArray = (array, parent) => array.map(node => asClonedNode(node, parent));\n\n// return a cloned node\nconst asClonedNode = (node, parent) => {\n\tconst cloneNode = new node.constructor(node);\n\n\tfor (const key in node) {\n\t\tif (key === 'parent') {\n\t\t\tcloneNode.parent = parent;\n\t\t} else if (Object(node[key]).constructor === Array) {\n\t\t\tcloneNode[key] = asClonedArray(node.nodes, cloneNode);\n\t\t} else if (Object(node[key]).constructor === Object) {\n\t\t\tcloneNode[key] = Object.assign({}, node[key]);\n\t\t}\n\t}\n\n\treturn cloneNode;\n};\n", "import { parse } from './postcss-values-parser';\nimport transformValueAST from './transform-value-ast';\nimport { isRuleIgnored } from './is-ignored';\n\n// transform custom pseudo selectors with custom selectors\nexport default (root, customProperties, opts) => {\n\t// walk decls that can be transformed\n\troot.walkDecls(decl => {\n\t\tif (isTransformableDecl(decl) && !isRuleIgnored(decl)) {\n\t\t\tconst originalValue = decl.value;\n\t\t\tconst valueAST = parse(originalValue);\n\t\t\tconst value = String(transformValueAST(valueAST, customProperties));\n\n\t\t\t// conditionally transform values that have changed\n\t\t\tif (value !== originalValue) {\n\t\t\t\tif (opts.preserve) {\n\t\t\t\t\tdecl.cloneBefore({ value });\n\t\t\t\t} else {\n\t\t\t\t\tdecl.value = value;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n};\n\n// match custom properties\nconst customPropertyRegExp = /^--[A-z][\\w-]*$/;\n\n// match custom property inclusions\nconst customPropertiesRegExp = /(^|[^\\w-])var\\([\\W\\w]+\\)/;\n\n// whether the declaration should be potentially transformed\nconst isTransformableDecl = decl => !customPropertyRegExp.test(decl.prop) && customPropertiesRegExp.test(decl.value);\n", "import fs from 'fs';\nimport path from 'path';\n\n/* Write Custom Properties to CSS File\n/* ========================================================================== */\n\nasync function writeCustomPropertiesToCssFile(to, customProperties) {\n\tconst cssContent = Object.keys(customProperties).reduce((cssLines, name) => {\n\t\tcssLines.push(`\\t${name}: ${customProperties[name]};`);\n\n\t\treturn cssLines;\n\t}, []).join('\\n');\n\tconst css = `:root {\\n${cssContent}\\n}\\n`;\n\n\tawait writeFile(to, css);\n}\n\n/* Write Custom Properties to JSON file\n/* ========================================================================== */\n\nasync function writeCustomPropertiesToJsonFile(to, customProperties) {\n\tconst jsonContent = JSON.stringify({\n\t\t'custom-properties': customProperties\n\t}, null, '  ');\n\tconst json = `${jsonContent}\\n`;\n\n\tawait writeFile(to, json);\n}\n\n/* Write Custom Properties to Common JS file\n/* ========================================================================== */\n\nasync function writeCustomPropertiesToCjsFile(to, customProperties) {\n\tconst jsContents = Object.keys(customProperties).reduce((jsLines, name) => {\n\t\tjsLines.push(`\\t\\t'${escapeForJS(name)}': '${escapeForJS(customProperties[name])}'`);\n\n\t\treturn jsLines;\n\t}, []).join(',\\n');\n\tconst js = `module.exports = {\\n\\tcustomProperties: {\\n${jsContents}\\n\\t}\\n};\\n`;\n\n\tawait writeFile(to, js);\n}\n\n/* Write Custom Properties to Module JS file\n/* ========================================================================== */\n\nasync function writeCustomPropertiesToMjsFile(to, customProperties) {\n\tconst mjsContents = Object.keys(customProperties).reduce((mjsLines, name) => {\n\t\tmjsLines.push(`\\t'${escapeForJS(name)}': '${escapeForJS(customProperties[name])}'`);\n\n\t\treturn mjsLines;\n\t}, []).join(',\\n');\n\tconst mjs = `export const customProperties = {\\n${mjsContents}\\n};\\n`;\n\n\tawait writeFile(to, mjs);\n}\n\n/* Write Custom Properties to Exports\n/* ========================================================================== */\n\nexport default function writeCustomPropertiesToExports(customProperties, destinations) {\n\treturn Promise.all(destinations.map(async destination => {\n\t\tif (destination instanceof Function) {\n\t\t\tawait destination(defaultCustomPropertiesToJSON(customProperties));\n\t\t} else {\n\t\t\t// read the destination as an object\n\t\t\tconst opts = destination === Object(destination) ? destination : { to: String(destination) };\n\n\t\t\t// transformer for Custom Properties into a JSON-compatible object\n\t\t\tconst toJSON = opts.toJSON || defaultCustomPropertiesToJSON;\n\n\t\t\tif ('customProperties' in opts) {\n\t\t\t\t// write directly to an object as customProperties\n\t\t\t\topts.customProperties = toJSON(customProperties);\n\t\t\t} else if ('custom-properties' in opts) {\n\t\t\t\t// write directly to an object as custom-properties\n\t\t\t\topts['custom-properties'] = toJSON(customProperties);\n\t\t\t} else {\n\t\t\t\t// destination pathname\n\t\t\t\tconst to = String(opts.to || '');\n\n\t\t\t\t// type of file being written to\n\t\t\t\tconst type = (opts.type || path.extname(opts.to).slice(1)).toLowerCase();\n\n\t\t\t\t// transformed Custom Properties\n\t\t\t\tconst customPropertiesJSON = toJSON(customProperties);\n\n\t\t\t\tif (type === 'css') {\n\t\t\t\t\tawait writeCustomPropertiesToCssFile(to, customPropertiesJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'js') {\n\t\t\t\t\tawait writeCustomPropertiesToCjsFile(to, customPropertiesJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'json') {\n\t\t\t\t\tawait writeCustomPropertiesToJsonFile(to, customPropertiesJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'mjs') {\n\t\t\t\t\tawait writeCustomPropertiesToMjsFile(to, customPropertiesJSON);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}));\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst defaultCustomPropertiesToJSON = customProperties => {\n\treturn Object.keys(customProperties).reduce((customPropertiesJSON, key) => {\n\t\tcustomPropertiesJSON[key] = String(customProperties[key]);\n\n\t\treturn customPropertiesJSON;\n\t}, {});\n};\n\nconst writeFile = (to, text) => new Promise((resolve, reject) => {\n\tfs.writeFile(to, text, error => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve();\n\t\t}\n\t});\n});\n\nconst escapeForJS = string => string.replace(/\\\\([\\s\\S])|(')/g, '\\\\$1$2').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n", "import postcss from 'postcss';\nimport getCustomPropertiesFromRoot from './lib/get-custom-properties-from-root';\nimport getCustomPropertiesFromImports from './lib/get-custom-properties-from-imports';\nimport transformProperties from './lib/transform-properties';\nimport writeCustomPropertiesToExports from './lib/write-custom-properties-to-exports';\n\nexport default postcss.plugin('postcss-custom-properties', opts => {\n\t// whether to preserve custom selectors and rules using them\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : true;\n\n\t// sources to import custom selectors from\n\tconst importFrom = [].concat(Object(opts).importFrom || []);\n\n\t// destinations to export custom selectors to\n\tconst exportTo = [].concat(Object(opts).exportTo || []);\n\n\t// promise any custom selectors are imported\n\tconst customPropertiesPromise = getCustomPropertiesFromImports(importFrom);\n\n\t// synchronous transform\n\tconst syncTransform = root => {\n\t\tconst customProperties = getCustomPropertiesFromRoot(root, { preserve });\n\n\t\ttransformProperties(root, customProperties, { preserve });\n\t};\n\n\t// asynchronous transform\n\tconst asyncTransform = async root => {\n\t\tconst customProperties = Object.assign(\n\t\t\t{},\n\t\t\tawait customPropertiesPromise,\n\t\t\tgetCustomPropertiesFromRoot(root, { preserve })\n\t\t);\n\n\t\tawait writeCustomPropertiesToExports(customProperties, exportTo);\n\n\t\ttransformProperties(root, customProperties, { preserve });\n\t};\n\n\t// whether to return synchronous function if no asynchronous operations are requested\n\tconst canReturnSyncFunction = importFrom.length === 0 && exportTo.length === 0;\n\n\treturn canReturnSyncFunction ? syncTransform : asyncTransform;\n});\n"], "names": ["parse", "string", "valueParser", "isBlockIgnored", "ruleOrDeclaration", "rule", "selector", "parent", "test", "toString", "isRuleIgnored", "previous", "prev", "Boolean", "type", "text", "getCustomPropertiesFromRoot", "root", "opts", "customPropertiesFromHtmlElement", "customPropertiesFromRootPseudo", "nodes", "slice", "for<PERSON>ach", "customPropertiesObject", "isHtmlRule", "isRootRule", "decl", "isCustomDecl", "prop", "value", "preserve", "remove", "isEmptyParent", "htmlSelectorRegExp", "rootSelectorRegExp", "customPropertyRegExp", "node", "Object", "length", "getCustomPropertiesFromCSSFile", "from", "css", "readFile", "postcss", "getCustomPropertiesFromObject", "object", "customProperties", "assign", "key", "String", "getCustomPropertiesFromJSONFile", "readJSON", "getCustomPropertiesFromJSFile", "getCustomPropertiesFromImports", "sources", "map", "source", "Promise", "Function", "path", "resolve", "extname", "toLowerCase", "reduce", "reject", "fs", "error", "result", "JSON", "transformValueAST", "child", "isVarFunction", "propertyNode", "comma", "fallbacks", "name", "asClonedArrayWithBeforeSpacing", "raws", "before", "replaceWith", "retransformValueAST", "index", "indexOf", "splice", "withoutProperty", "nextCustomProperties", "varRegExp", "array", "beforeSpacing", "cloned<PERSON><PERSON><PERSON>", "asClonedArray", "asClonedNode", "cloneNode", "constructor", "Array", "walkDecls", "isTransformableDecl", "originalValue", "valueAST", "cloneBefore", "customPropertiesRegExp", "writeCustomPropertiesToCssFile", "to", "cssContent", "keys", "cssLines", "push", "join", "writeFile", "writeCustomPropertiesToJsonFile", "json<PERSON><PERSON><PERSON>", "stringify", "json", "writeCustomPropertiesToCjsFile", "js<PERSON><PERSON>nts", "jsLines", "escapeForJS", "js", "writeCustomPropertiesToMjsFile", "mjs<PERSON><PERSON><PERSON>", "mjsLines", "mjs", "writeCustomPropertiesToExports", "destinations", "all", "destination", "defaultCustomPropertiesToJSON", "toJSON", "customPropertiesJSON", "replace", "plugin", "importFrom", "concat", "exportTo", "customPropertiesPromise", "syncTransform", "transformProperties", "asyncTransform", "canReturnSyncFunction"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAASA,KAAT,CAAgBC,MAAhB,EAAwB;SACvBC,WAAW,CAACD,MAAD,CAAX,CAAoBD,KAApB,EAAP;;;ACHD,SAASG,cAAT,CAAwBC,iBAAxB,EAA2C;MACtCC,IAAI,GAAGD,iBAAiB,CAACE,QAAlB,GACVF,iBADU,GACUA,iBAAiB,CAACG,MADvC;SAGO,6CAA6CC,IAA7C,CAAkDH,IAAI,CAACI,QAAL,EAAlD,CAAP;;;AAGD,SAASC,aAAT,CAAuBL,IAAvB,EAA6B;MACxBM,QAAQ,GAAGN,IAAI,CAACO,IAAL,EAAf;SAEOC,OAAO,CAACV,cAAc,CAACE,IAAD,CAAd,IACdM,QAAQ,IACRA,QAAQ,CAACG,IAAT,KAAkB,SADlB,IAEA,uDAAuDN,IAAvD,CAA4DG,QAAQ,CAACI,IAArE,CAHa,CAAd;;;ACNc,SAASC,2BAAT,CAAqCC,IAArC,EAA2CC,IAA3C,EAAiD;;QAEzDC,+BAA+B,GAAG,EAAxC;QACMC,8BAA8B,GAAG,EAAvC,CAH+D;;EAM/DH,IAAI,CAACI,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BlB,IAAI,IAAI;UAC5BmB,sBAAsB,GAAGC,UAAU,CAACpB,IAAD,CAAV,GAC5Bc,+BAD4B,GAE7BO,UAAU,CAACrB,IAAD,CAAV,GACCe,8BADD,GAEA,IAJF,CADkC;;QAQ9BI,sBAAJ,EAA4B;MAC3BnB,IAAI,CAACgB,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BI,IAAI,IAAI;YAC9BC,YAAY,CAACD,IAAD,CAAZ,IAAsB,CAACxB,cAAc,CAACwB,IAAD,CAAzC,EAAiD;gBACxCE,IADwC,GAC/BF,IAD+B,CACxCE,IADwC;;UAIhDL,sBAAsB,CAACK,IAAD,CAAtB,GAA+B7B,KAAK,CAAC2B,IAAI,CAACG,KAAN,CAAL,CAAkBT,KAAjD,CAJgD;;cAO5C,CAACH,IAAI,CAACa,QAAV,EAAoB;YACnBJ,IAAI,CAACK,MAAL;;;OATH,EAD2B;;UAgBvB,CAACd,IAAI,CAACa,QAAN,IAAkBE,aAAa,CAAC5B,IAAD,CAA/B,IAAyC,CAACF,cAAc,CAACE,IAAD,CAA5D,EAAoE;QACnEA,IAAI,CAAC2B,MAAL;;;GAzBH,EAN+D;;2BAqCnDb,+BAAZ,EAAgDC,8BAAhD;;;AAID,MAAMc,kBAAkB,GAAG,SAA3B;AACA,MAAMC,kBAAkB,GAAG,UAA3B;AACA,MAAMC,oBAAoB,GAAG,iBAA7B;;AAGA,MAAMX,UAAU,GAAGY,IAAI,IAAIA,IAAI,CAACvB,IAAL,KAAc,MAAd,IAAwBoB,kBAAkB,CAAC1B,IAAnB,CAAwB6B,IAAI,CAAC/B,QAA7B,CAAxB,IAAkEgC,MAAM,CAACD,IAAI,CAAChB,KAAN,CAAN,CAAmBkB,MAAhH;;AACA,MAAMb,UAAU,GAAGW,IAAI,IAAIA,IAAI,CAACvB,IAAL,KAAc,MAAd,IAAwBqB,kBAAkB,CAAC3B,IAAnB,CAAwB6B,IAAI,CAAC/B,QAA7B,CAAxB,IAAkEgC,MAAM,CAACD,IAAI,CAAChB,KAAN,CAAN,CAAmBkB,MAAhH;;;AAGA,MAAMX,YAAY,GAAGS,IAAI,IAAIA,IAAI,CAACvB,IAAL,KAAc,MAAd,IAAwBsB,oBAAoB,CAAC5B,IAArB,CAA0B6B,IAAI,CAACR,IAA/B,CAArD;;;AAGA,MAAMI,aAAa,GAAGI,IAAI,IAAIC,MAAM,CAACD,IAAI,CAAChB,KAAN,CAAN,CAAmBkB,MAAnB,KAA8B,CAA5D;;ACnDA;;;SAGeC;;;;;;;;sDAAf,WAA8CC,IAA9C,EAAoD;UAC7CC,GAAG,SAASC,QAAQ,CAACF,IAAD,CAA1B;UACMxB,IAAI,GAAG2B,OAAO,CAAC5C,KAAR,CAAc0C,GAAd,EAAmB;MAAED;KAArB,CAAb;WAEOzB,2BAA2B,CAACC,IAAD,EAAO;MAAEc,QAAQ,EAAE;KAAnB,CAAlC;;;;;AAMD,SAASc,6BAAT,CAAuCC,MAAvC,EAA+C;QACxCC,gBAAgB,GAAGT,MAAM,CAACU,MAAP,CACxB,EADwB,EAExBV,MAAM,CAACQ,MAAD,CAAN,CAAeC,gBAFS,EAGxBT,MAAM,CAACQ,MAAD,CAAN,CAAe,mBAAf,CAHwB,CAAzB;;OAMK,MAAMG,GAAX,IAAkBF,gBAAlB,EAAoC;IACnCA,gBAAgB,CAACE,GAAD,CAAhB,GAAwBjD,KAAK,CAACkD,MAAM,CAACH,gBAAgB,CAACE,GAAD,CAAjB,CAAP,CAAL,CAAqC5B,KAA7D;;;SAGM0B,gBAAP;;;;;;SAMcI;;;;;;;;uDAAf,WAA+CV,IAA/C,EAAqD;UAC9CK,MAAM,SAASM,QAAQ,CAACX,IAAD,CAA7B;WAEOI,6BAA6B,CAACC,MAAD,CAApC;;;;;SAMcO;;;;;;;;qDAAf,WAA6CZ,IAA7C,EAAmD;UAC5CK,MAAM,SAAS,wBAAOL,IAAP,EAArB;WAEOI,6BAA6B,CAACC,MAAD,CAApC;;;;;AAMD,AAAe,SAASQ,8BAAT,CAAwCC,OAAxC,EAAiD;SACxDA,OAAO,CAACC,GAAR,CAAYC,MAAM,IAAI;QACxBA,MAAM,YAAYC,OAAtB,EAA+B;aACvBD,MAAP;KADD,MAEO,IAAIA,MAAM,YAAYE,QAAtB,EAAgC;aAC/BF,MAAM,EAAb;KAJ2B;;;UAQtBvC,IAAI,GAAGuC,MAAM,KAAKnB,MAAM,CAACmB,MAAD,CAAjB,GAA4BA,MAA5B,GAAqC;MAAEhB,IAAI,EAAES,MAAM,CAACO,MAAD;KAAhE,CAR4B;;QAWxBvC,IAAI,CAAC6B,gBAAL,IAAyB7B,IAAI,CAAC,mBAAD,CAAjC,EAAwD;aAChDA,IAAP;KAZ2B;;;UAgBtBuB,IAAI,GAAGmB,IAAI,CAACC,OAAL,CAAaX,MAAM,CAAChC,IAAI,CAACuB,IAAL,IAAa,EAAd,CAAnB,CAAb,CAhB4B;;UAmBtB3B,IAAI,GAAG,CAACI,IAAI,CAACJ,IAAL,IAAa8C,IAAI,CAACE,OAAL,CAAarB,IAAb,EAAmBnB,KAAnB,CAAyB,CAAzB,CAAd,EAA2CyC,WAA3C,EAAb;WAEO;MAAEjD,IAAF;MAAQ2B;KAAf;GArBM,EAsBJuB,MAtBI;;;iCAsBG,WAAOjB,gBAAP,EAAyBU,MAAzB,EAAoC;0BAChBA,MADgB;YACrC3C,IADqC,SACrCA,IADqC;YAC/B2B,IAD+B,SAC/BA,IAD+B;;UAGzC3B,IAAI,KAAK,KAAb,EAAoB;eACZwB,MAAM,CAACU,MAAP,QAAoBD,gBAApB,UAA4CP,8BAA8B,CAACC,IAAD,CAA1E,EAAP;;;UAGG3B,IAAI,KAAK,IAAb,EAAmB;eACXwB,MAAM,CAACU,MAAP,QAAoBD,gBAApB,UAA4CM,6BAA6B,CAACZ,IAAD,CAAzE,EAAP;;;UAGG3B,IAAI,KAAK,MAAb,EAAqB;eACbwB,MAAM,CAACU,MAAP,QAAoBD,gBAApB,UAA4CI,+BAA+B,CAACV,IAAD,CAA3E,EAAP;;;aAGMH,MAAM,CAACU,MAAP,QAAoBD,gBAApB,UAA4CF,6BAA6B,QAAOY,MAAP,EAAzE,EAAP;KArCM;;;;;OAsCJ,EAtCI,CAAP;;;;;AA4CD,MAAMd,QAAQ,GAAGF,IAAI,IAAI,IAAIiB,OAAJ,CAAY,CAACG,OAAD,EAAUI,MAAV,KAAqB;EACzDC,EAAE,CAACvB,QAAH,CAAYF,IAAZ,EAAkB,MAAlB,EAA0B,CAAC0B,KAAD,EAAQC,MAAR,KAAmB;QACxCD,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNN,OAAO,CAACO,MAAD,CAAP;;GAJF;CADwB,CAAzB;;AAUA,MAAMhB,QAAQ;;AAAA;gCAAG,WAAMX,IAAN;WAAc4B,IAAI,CAACrE,KAAL,QAAiB2C,QAAQ,CAACF,IAAD,CAAzB,EAAd;GAAH;;kBAARW,QAAQ;;;GAAd;;AC7Ge,SAASkB,iBAAT,CAA2BrD,IAA3B,EAAiC8B,gBAAjC,EAAmD;MAC7D9B,IAAI,CAACI,KAAL,IAAcJ,IAAI,CAACI,KAAL,CAAWkB,MAA7B,EAAqC;IACpCtB,IAAI,CAACI,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BgD,KAAK,IAAI;UAC/BC,aAAa,CAACD,KAAD,CAAjB,EAA0B;;mCAEmBA,KAAK,CAAClD,KAAN,CAAYC,KAAZ,CAAkB,CAAlB,EAAqB,CAAC,CAAtB,CAFnB;cAElBmD,YAFkB;cAEJC,KAFI;cAEMC,SAFN;;cAGVC,IAHU,GAGDH,YAHC,CAGjB3C,KAHiB;;YAKrB8C,IAAI,IAAItC,MAAM,CAACS,gBAAD,CAAlB,EAAsC;;gBAE/B1B,KAAK,GAAGwD,8BAA8B,CAAC9B,gBAAgB,CAAC6B,IAAD,CAAjB,EAAyBL,KAAK,CAACO,IAAN,CAAWC,MAApC,CAA5C;UAEAR,KAAK,CAACS,WAAN,CAAkB,GAAG3D,KAArB;UAEA4D,mBAAmB,CAAC;YAAE5D;WAAH,EAAY0B,gBAAZ,EAA8B6B,IAA9B,CAAnB;SAND,MAOO,IAAID,SAAS,CAACpC,MAAd,EAAsB;;gBAEtB2C,KAAK,GAAGjE,IAAI,CAACI,KAAL,CAAW8D,OAAX,CAAmBZ,KAAnB,CAAd;;cAEIW,KAAK,KAAK,CAAC,CAAf,EAAkB;YACjBjE,IAAI,CAACI,KAAL,CAAW+D,MAAX,CAAkBF,KAAlB,EAAyB,CAAzB,EAA4B,GAAGL,8BAA8B,CAACF,SAAD,EAAYJ,KAAK,CAACO,IAAN,CAAWC,MAAvB,CAA7D;;;UAGDT,iBAAiB,CAACrD,IAAD,EAAO8B,gBAAP,CAAjB;;OApBF,MAsBO;QACNuB,iBAAiB,CAACC,KAAD,EAAQxB,gBAAR,CAAjB;;KAxBF;;;SA6BM9B,IAAP;;;AAID,SAASgE,mBAAT,CAA6BhE,IAA7B,EAAmC8B,gBAAnC,EAAqDsC,eAArD,EAAsE;QAC/DC,oBAAoB,GAAGhD,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBD,gBAAlB,CAA7B;SAEOuC,oBAAoB,CAACD,eAAD,CAA3B;SAEOf,iBAAiB,CAACrD,IAAD,EAAOqE,oBAAP,CAAxB;;;;AAID,MAAMC,SAAS,GAAG,QAAlB;;AAGA,MAAMf,aAAa,GAAGnC,IAAI,IAAIA,IAAI,CAACvB,IAAL,KAAc,MAAd,IAAwByE,SAAS,CAAC/E,IAAV,CAAe6B,IAAI,CAACP,KAApB,CAAxB,IAAsDQ,MAAM,CAACD,IAAI,CAAChB,KAAN,CAAN,CAAmBkB,MAAnB,GAA4B,CAAhH;;;AAGA,MAAMsC,8BAA8B,GAAG,CAACW,KAAD,EAAQC,aAAR,KAA0B;QAC1DC,WAAW,GAAGC,aAAa,CAACH,KAAD,EAAQ,IAAR,CAAjC;;MAEIE,WAAW,CAAC,CAAD,CAAf,EAAoB;IACnBA,WAAW,CAAC,CAAD,CAAX,CAAeZ,IAAf,CAAoBC,MAApB,GAA6BU,aAA7B;;;SAGMC,WAAP;CAPD;;;AAWA,MAAMC,aAAa,GAAG,CAACH,KAAD,EAAQjF,MAAR,KAAmBiF,KAAK,CAAChC,GAAN,CAAUnB,IAAI,IAAIuD,YAAY,CAACvD,IAAD,EAAO9B,MAAP,CAA9B,CAAzC;;;AAGA,MAAMqF,YAAY,GAAG,CAACvD,IAAD,EAAO9B,MAAP,KAAkB;QAChCsF,SAAS,GAAG,IAAIxD,IAAI,CAACyD,WAAT,CAAqBzD,IAArB,CAAlB;;OAEK,MAAMY,GAAX,IAAkBZ,IAAlB,EAAwB;QACnBY,GAAG,KAAK,QAAZ,EAAsB;MACrB4C,SAAS,CAACtF,MAAV,GAAmBA,MAAnB;KADD,MAEO,IAAI+B,MAAM,CAACD,IAAI,CAACY,GAAD,CAAL,CAAN,CAAkB6C,WAAlB,KAAkCC,KAAtC,EAA6C;MACnDF,SAAS,CAAC5C,GAAD,CAAT,GAAiB0C,aAAa,CAACtD,IAAI,CAAChB,KAAN,EAAawE,SAAb,CAA9B;KADM,MAEA,IAAIvD,MAAM,CAACD,IAAI,CAACY,GAAD,CAAL,CAAN,CAAkB6C,WAAlB,KAAkCxD,MAAtC,EAA8C;MACpDuD,SAAS,CAAC5C,GAAD,CAAT,GAAiBX,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBX,IAAI,CAACY,GAAD,CAAtB,CAAjB;;;;SAIK4C,SAAP;CAbD;;AC3DA,2BAAe,CAAC5E,IAAD,EAAO8B,gBAAP,EAAyB7B,IAAzB,KAAkC;;EAEhDD,IAAI,CAAC+E,SAAL,CAAerE,IAAI,IAAI;QAClBsE,mBAAmB,CAACtE,IAAD,CAAnB,IAA6B,CAACjB,aAAa,CAACiB,IAAD,CAA/C,EAAuD;YAChDuE,aAAa,GAAGvE,IAAI,CAACG,KAA3B;YACMqE,QAAQ,GAAGnG,KAAK,CAACkG,aAAD,CAAtB;YACMpE,KAAK,GAAGoB,MAAM,CAACoB,iBAAiB,CAAC6B,QAAD,EAAWpD,gBAAX,CAAlB,CAApB,CAHsD;;UAMlDjB,KAAK,KAAKoE,aAAd,EAA6B;YACxBhF,IAAI,CAACa,QAAT,EAAmB;UAClBJ,IAAI,CAACyE,WAAL,CAAiB;YAAEtE;WAAnB;SADD,MAEO;UACNH,IAAI,CAACG,KAAL,GAAaA,KAAb;;;;GAXJ;CAFD;;AAqBA,MAAMM,sBAAoB,GAAG,iBAA7B;;AAGA,MAAMiE,sBAAsB,GAAG,0BAA/B;;AAGA,MAAMJ,mBAAmB,GAAGtE,IAAI,IAAI,CAACS,sBAAoB,CAAC5B,IAArB,CAA0BmB,IAAI,CAACE,IAA/B,CAAD,IAAyCwE,sBAAsB,CAAC7F,IAAvB,CAA4BmB,IAAI,CAACG,KAAjC,CAA7E;;AC7BA;;;SAGewE;;;;;;;;sDAAf,WAA8CC,EAA9C,EAAkDxD,gBAAlD,EAAoE;UAC7DyD,UAAU,GAAGlE,MAAM,CAACmE,IAAP,CAAY1D,gBAAZ,EAA8BiB,MAA9B,CAAqC,CAAC0C,QAAD,EAAW9B,IAAX,KAAoB;MAC3E8B,QAAQ,CAACC,IAAT,CAAe,KAAI/B,IAAK,KAAI7B,gBAAgB,CAAC6B,IAAD,CAAO,GAAnD;aAEO8B,QAAP;KAHkB,EAIhB,EAJgB,EAIZE,IAJY,CAIP,IAJO,CAAnB;UAKMlE,GAAG,GAAI,YAAW8D,UAAW,OAAnC;UAEMK,SAAS,CAACN,EAAD,EAAK7D,GAAL,CAAf;;;;;SAMcoE;;;;;;;;uDAAf,WAA+CP,EAA/C,EAAmDxD,gBAAnD,EAAqE;UAC9DgE,WAAW,GAAG1C,IAAI,CAAC2C,SAAL,CAAe;2BACbjE;KADF,EAEjB,IAFiB,EAEX,IAFW,CAApB;UAGMkE,IAAI,GAAI,GAAEF,WAAY,IAA5B;UAEMF,SAAS,CAACN,EAAD,EAAKU,IAAL,CAAf;;;;;SAMcC;;;;;;;;sDAAf,WAA8CX,EAA9C,EAAkDxD,gBAAlD,EAAoE;UAC7DoE,UAAU,GAAG7E,MAAM,CAACmE,IAAP,CAAY1D,gBAAZ,EAA8BiB,MAA9B,CAAqC,CAACoD,OAAD,EAAUxC,IAAV,KAAmB;MAC1EwC,OAAO,CAACT,IAAR,CAAc,QAAOU,WAAW,CAACzC,IAAD,CAAO,OAAMyC,WAAW,CAACtE,gBAAgB,CAAC6B,IAAD,CAAjB,CAAyB,GAAjF;aAEOwC,OAAP;KAHkB,EAIhB,EAJgB,EAIZR,IAJY,CAIP,KAJO,CAAnB;UAKMU,EAAE,GAAI,8CAA6CH,UAAW,aAApE;UAEMN,SAAS,CAACN,EAAD,EAAKe,EAAL,CAAf;;;;;SAMcC;;;;;;;;sDAAf,WAA8ChB,EAA9C,EAAkDxD,gBAAlD,EAAoE;UAC7DyE,WAAW,GAAGlF,MAAM,CAACmE,IAAP,CAAY1D,gBAAZ,EAA8BiB,MAA9B,CAAqC,CAACyD,QAAD,EAAW7C,IAAX,KAAoB;MAC5E6C,QAAQ,CAACd,IAAT,CAAe,MAAKU,WAAW,CAACzC,IAAD,CAAO,OAAMyC,WAAW,CAACtE,gBAAgB,CAAC6B,IAAD,CAAjB,CAAyB,GAAhF;aAEO6C,QAAP;KAHmB,EAIjB,EAJiB,EAIbb,IAJa,CAIR,KAJQ,CAApB;UAKMc,GAAG,GAAI,sCAAqCF,WAAY,QAA9D;UAEMX,SAAS,CAACN,EAAD,EAAKmB,GAAL,CAAf;;;;;AAMD,AAAe,SAASC,8BAAT,CAAwC5E,gBAAxC,EAA0D6E,YAA1D,EAAwE;SAC/ElE,OAAO,CAACmE,GAAR,CAAYD,YAAY,CAACpE,GAAb;;;iCAAiB,WAAMsE,WAAN,EAAqB;UACpDA,WAAW,YAAYnE,QAA3B,EAAqC;cAC9BmE,WAAW,CAACC,6BAA6B,CAAChF,gBAAD,CAA9B,CAAjB;OADD,MAEO;;cAEA7B,IAAI,GAAG4G,WAAW,KAAKxF,MAAM,CAACwF,WAAD,CAAtB,GAAsCA,WAAtC,GAAoD;UAAEvB,EAAE,EAAErD,MAAM,CAAC4E,WAAD;SAA7E,CAFM;;cAKAE,MAAM,GAAG9G,IAAI,CAAC8G,MAAL,IAAeD,6BAA9B;;YAEI,sBAAsB7G,IAA1B,EAAgC;;UAE/BA,IAAI,CAAC6B,gBAAL,GAAwBiF,MAAM,CAACjF,gBAAD,CAA9B;SAFD,MAGO,IAAI,uBAAuB7B,IAA3B,EAAiC;;UAEvCA,IAAI,CAAC,mBAAD,CAAJ,GAA4B8G,MAAM,CAACjF,gBAAD,CAAlC;SAFM,MAGA;;gBAEAwD,EAAE,GAAGrD,MAAM,CAAChC,IAAI,CAACqF,EAAL,IAAW,EAAZ,CAAjB,CAFM;;gBAKAzF,IAAI,GAAG,CAACI,IAAI,CAACJ,IAAL,IAAa8C,IAAI,CAACE,OAAL,CAAa5C,IAAI,CAACqF,EAAlB,EAAsBjF,KAAtB,CAA4B,CAA5B,CAAd,EAA8CyC,WAA9C,EAAb,CALM;;gBAQAkE,oBAAoB,GAAGD,MAAM,CAACjF,gBAAD,CAAnC;;cAEIjC,IAAI,KAAK,KAAb,EAAoB;kBACbwF,8BAA8B,CAACC,EAAD,EAAK0B,oBAAL,CAApC;;;cAGGnH,IAAI,KAAK,IAAb,EAAmB;kBACZoG,8BAA8B,CAACX,EAAD,EAAK0B,oBAAL,CAApC;;;cAGGnH,IAAI,KAAK,MAAb,EAAqB;kBACdgG,+BAA+B,CAACP,EAAD,EAAK0B,oBAAL,CAArC;;;cAGGnH,IAAI,KAAK,KAAb,EAAoB;kBACbyG,8BAA8B,CAAChB,EAAD,EAAK0B,oBAAL,CAApC;;;;KAvCe;;;;;MAAZ,CAAP;;;;;AAiDD,MAAMF,6BAA6B,GAAGhF,gBAAgB,IAAI;SAClDT,MAAM,CAACmE,IAAP,CAAY1D,gBAAZ,EAA8BiB,MAA9B,CAAqC,CAACiE,oBAAD,EAAuBhF,GAAvB,KAA+B;IAC1EgF,oBAAoB,CAAChF,GAAD,CAApB,GAA4BC,MAAM,CAACH,gBAAgB,CAACE,GAAD,CAAjB,CAAlC;WAEOgF,oBAAP;GAHM,EAIJ,EAJI,CAAP;CADD;;AAQA,MAAMpB,SAAS,GAAG,CAACN,EAAD,EAAKxF,IAAL,KAAc,IAAI2C,OAAJ,CAAY,CAACG,OAAD,EAAUI,MAAV,KAAqB;EAChEC,EAAE,CAAC2C,SAAH,CAAaN,EAAb,EAAiBxF,IAAjB,EAAuBoD,KAAK,IAAI;QAC3BA,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNN,OAAO;;GAJT;CAD+B,CAAhC;;AAUA,MAAMwD,WAAW,GAAGpH,MAAM,IAAIA,MAAM,CAACiI,OAAP,CAAe,iBAAf,EAAkC,QAAlC,EAA4CA,OAA5C,CAAoD,KAApD,EAA2D,KAA3D,EAAkEA,OAAlE,CAA0E,KAA1E,EAAiF,KAAjF,CAA9B;;AC1HA,YAAetF,OAAO,CAACuF,MAAR,CAAe,2BAAf,EAA4CjH,IAAI,IAAI;;QAE5Da,QAAQ,GAAG,cAAcO,MAAM,CAACpB,IAAD,CAApB,GAA6BL,OAAO,CAACK,IAAI,CAACa,QAAN,CAApC,GAAsD,IAAvE,CAFkE;;QAK5DqG,UAAU,GAAG,GAAGC,MAAH,CAAU/F,MAAM,CAACpB,IAAD,CAAN,CAAakH,UAAb,IAA2B,EAArC,CAAnB,CALkE;;QAQ5DE,QAAQ,GAAG,GAAGD,MAAH,CAAU/F,MAAM,CAACpB,IAAD,CAAN,CAAaoH,QAAb,IAAyB,EAAnC,CAAjB,CARkE;;QAW5DC,uBAAuB,GAAGjF,8BAA8B,CAAC8E,UAAD,CAA9D,CAXkE;;QAc5DI,aAAa,GAAGvH,IAAI,IAAI;UACvB8B,gBAAgB,GAAG/B,2BAA2B,CAACC,IAAD,EAAO;MAAEc;KAAT,CAApD;IAEA0G,mBAAmB,CAACxH,IAAD,EAAO8B,gBAAP,EAAyB;MAAEhB;KAA3B,CAAnB;GAHD,CAdkE;;;QAqB5D2G,cAAc;;;iCAAG,WAAMzH,IAAN,EAAc;YAC9B8B,gBAAgB,GAAGT,MAAM,CAACU,MAAP,CACxB,EADwB,SAElBuF,uBAFkB,GAGxBvH,2BAA2B,CAACC,IAAD,EAAO;QAAEc;OAAT,CAHH,CAAzB;YAMM4F,8BAA8B,CAAC5E,gBAAD,EAAmBuF,QAAnB,CAApC;MAEAG,mBAAmB,CAACxH,IAAD,EAAO8B,gBAAP,EAAyB;QAAEhB;OAA3B,CAAnB;KATmB;;oBAAd2G,cAAc;;;KAApB,CArBkE;;;QAkC5DC,qBAAqB,GAAGP,UAAU,CAAC7F,MAAX,KAAsB,CAAtB,IAA2B+F,QAAQ,CAAC/F,MAAT,KAAoB,CAA7E;SAEOoG,qBAAqB,GAAGH,aAAH,GAAmBE,cAA/C;CApCc,CAAf;;;;"}