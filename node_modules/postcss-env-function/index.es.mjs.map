{"version": 3, "file": "index.es.mjs", "sources": ["lib/get-fn-value.js", "lib/update-env-value.js", "lib/is-env-func.js", "lib/walk-env-funcs.js", "lib/get-replaced-value.js", "lib/is-atrule.js", "lib/is-decl.js", "lib/get-supported-value.js", "lib/set-supported-value.js", "lib/import-from.js", "index.js"], "sourcesContent": ["const dashedMatch = /^--/;\n\n// returns the value of a css function as a string\nexport default (node) => {\n\tconst value = String(node.nodes.slice(1, -1));\n\n\treturn dashedMatch.test(value) ? value : undefined;\n};\n", "import getFnValue from './get-fn-value';\n\n// update a node with an environment value\nexport default (node, variables) => {\n\t// get the value of a css function as a string\n\tconst value = getFnValue(node);\n\n\tif (typeof value === 'string' && value in variables) {\n\t\tnode.replaceWith(\n\t\t\t...asClonedArrayWithBeforeSpacing(variables[value], node.raws.before)\n\t\t);\n\t}\n};\n\n// return an array with its nodes cloned, preserving the raw\nconst asClonedArrayWithBeforeSpacing = (array, beforeSpacing) => {\n\tconst clonedArray = asClonedArray(array, null);\n\n\tif (clonedArray[0]) {\n\t\tclonedArray[0].raws.before = beforeSpacing;\n\t}\n\n\treturn clonedArray;\n};\n\n// return an array with its nodes cloned\nconst asClonedArray = (array, parent) => array.map(node => asClonedNode(node, parent));\n\n// return a cloned node\nconst asClonedNode = (node, parent) => {\n\tconst cloneNode = new node.constructor(node);\n\n\tfor (const key in node) {\n\t\tif (key === 'parent') {\n\t\t\tcloneNode.parent = parent;\n\t\t} else if (Object(node[key]).constructor === Array) {\n\t\t\tcloneNode[key] = asClonedArray(node.nodes, cloneNode);\n\t\t} else if (Object(node[key]).constructor === Object) {\n\t\t\tcloneNode[key] = Object.assign({}, node[key]);\n\t\t}\n\t}\n\n\treturn cloneNode;\n};\n", "// returns whether a node is a css env() function\nexport default (node) => node && node.type === 'func' && node.value === 'env';\n", "import isEnvFunc from './is-env-func';\n\n// walks a node recursively and runs a function using its children\nexport default function walk(node, fn) {\n\tnode.nodes.slice(0).forEach(childNode => {\n\t\tif (childNode.nodes) {\n\t\t\twalk(childNode, fn);\n\t\t}\n\n\t\tif (isEnvFunc(childNode)) {\n\t\t\tfn(childNode);\n\t\t}\n\t});\n}\n", "import parser from 'postcss-values-parser';\nimport updateEnvValue from './update-env-value';\nimport walkEnvFuncs from './walk-env-funcs';\n\n// returns a value replaced with environment variables\nexport default (originalValue, variables) => {\n\t// get the ast of the original value\n\tconst ast = parser(originalValue).parse();\n\n\t// walk all of the css env() functions\n\twalkEnvFuncs(ast, node => {\n\t\t// update the environment value for the css env() function\n\t\tupdateEnvValue(node, variables);\n\t});\n\n\t// return the stringified ast\n\treturn String(ast);\n};\n", "// returns whether a node is an at-rule\nexport default (node) => node && node.type === 'atrule';\n", "// returns whether a node is a declaration\nexport default (node) => node && node.type === 'decl';\n", "import isAtrule from './is-atrule';\nimport isDecl from './is-decl';\n\n// returns a value from an at-rule or declaration\nexport default (node) => isAtrule(node) && node.params || isDecl(node) && node.value;\n", "import isAtrule from './is-atrule';\nimport isDecl from './is-decl';\n\n// assigns a value to an at-rule or declaration\nexport default function (node, value) {\n\tif (isAtrule(node)) {\n\t\tnode.params = value;\n\t}\n\n\tif (isDecl(node)) {\n\t\tnode.value = value;\n\t}\n}\n", "import fs from 'fs';\nimport path from 'path';\nimport valueParser from 'postcss-values-parser';\n\n/* Import Custom Properties from Object\n/* ========================================================================== */\n\nfunction importEnvironmentVariablesFromObject(object) {\n\tconst environmentVariables = Object.assign(\n\t\t{},\n\t\tObject(object).environmentVariables || Object(object)['environment-variables']\n\t);\n\n\tfor (const key in environmentVariables) {\n\t\tenvironmentVariables[key] = valueParser(environmentVariables[key]).parse().nodes;\n\t}\n\n\treturn environmentVariables;\n}\n\n/* Import Custom Properties from JSON file\n/* ========================================================================== */\n\nasync function importEnvironmentVariablesFromJSONFile(from) {\n\tconst object = await readJSON(path.resolve(from));\n\n\treturn importEnvironmentVariablesFromObject(object);\n}\n\n/* Import Custom Properties from JS file\n/* ========================================================================== */\n\nasync function importEnvironmentVariablesFromJSFile(from) {\n\tconst object = await import(path.resolve(from));\n\n\treturn importEnvironmentVariablesFromObject(object);\n}\n\n/* Import Custom Properties from Sources\n/* ========================================================================== */\n\nexport default function importEnvironmentVariablesFromSources(sources) {\n\treturn sources.map(source => {\n\t\tif (source instanceof Promise) {\n\t\t\treturn source;\n\t\t} else if (source instanceof Function) {\n\t\t\treturn source();\n\t\t}\n\n\t\t// read the source as an object\n\t\tconst opts = source === Object(source) ? source : { from: String(source) };\n\n\t\t// skip objects with Custom Properties\n\t\tif (opts.environmentVariables || opts['environment-variables']) {\n\t\t\treturn opts\n\t\t}\n\n\t\t// source pathname\n\t\tconst from = String(opts.from || '');\n\n\t\t// type of file being read from\n\t\tconst type = (opts.type || path.extname(from).slice(1)).toLowerCase();\n\n\t\treturn { type, from };\n\t}).reduce(async (environmentVariables, source) => {\n\t\tconst { type, from } = await source;\n\n\t\tif (type === 'js') {\n\t\t\treturn Object.assign(environmentVariables, await importEnvironmentVariablesFromJSFile(from));\n\t\t}\n\n\t\tif (type === 'json') {\n\t\t\treturn Object.assign(environmentVariables, await importEnvironmentVariablesFromJSONFile(from));\n\t\t}\n\n\t\treturn Object.assign(environmentVariables, importEnvironmentVariablesFromObject(await source));\n\t}, {});\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst readFile = from => new Promise((resolve, reject) => {\n\tfs.readFile(from, 'utf8', (error, result) => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve(result);\n\t\t}\n\t});\n});\n\nconst readJSON = async from => JSON.parse(await readFile(from));\n", "import postcss from 'postcss';\nimport getReplacedValue from './lib/get-replaced-value';\nimport getSupportedValue from './lib/get-supported-value';\nimport setSupportedValue from './lib/set-supported-value';\nimport importEnvironmentVariablesFromSources from './lib/import-from';\n\nexport default postcss.plugin('postcss-env-fn', opts => {\n\t// sources to import environment variables from\n\tconst importFrom = [].concat(Object(opts).importFrom || []);\n\n\t// promise any environment variables are imported\n\tconst environmentVariablesPromise = importEnvironmentVariablesFromSources(importFrom);\n\n\treturn async root => {\n\t\tconst environmentVariables = await environmentVariablesPromise;\n\n\t\troot.walk(node => {\n\t\t\tconst supportedValue = getSupportedValue(node);\n\n\t\t\tif (supportedValue) {\n\t\t\t\tconst replacedValue = getReplacedValue(supportedValue, environmentVariables);\n\n\t\t\t\tif (replacedValue !== supportedValue) {\n\t\t\t\t\tsetSupportedValue(node, replacedValue);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n"], "names": ["dashedMatch", "node", "value", "String", "nodes", "slice", "test", "undefined", "variables", "getFnValue", "replaceWith", "asClonedArrayWithBeforeSpacing", "raws", "before", "array", "beforeSpacing", "cloned<PERSON><PERSON><PERSON>", "asClonedArray", "parent", "map", "asClonedNode", "cloneNode", "constructor", "key", "Object", "Array", "assign", "type", "walk", "fn", "for<PERSON>ach", "childNode", "isEnvFunc", "originalValue", "ast", "parser", "parse", "walkEnvFuncs", "updateEnvValue", "isAtrule", "params", "isDecl", "importEnvironmentVariablesFromObject", "object", "environmentVariables", "valueParser", "importEnvironmentVariablesFromJSONFile", "from", "readJSON", "path", "resolve", "importEnvironmentVariablesFromJSFile", "importEnvironmentVariablesFromSources", "sources", "source", "Promise", "Function", "opts", "extname", "toLowerCase", "reduce", "readFile", "reject", "fs", "error", "result", "JSON", "postcss", "plugin", "importFrom", "concat", "environmentVariablesPromise", "root", "supportedValue", "getSupportedValue", "replacedV<PERSON>ue", "getReplacedValue", "setSupportedValue"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAMA,WAAW,GAAG,KAApB;;AAGA,kBAAgBC,IAAD,IAAU;QAClBC,KAAK,GAAGC,MAAM,CAACF,IAAI,CAACG,KAAL,CAAWC,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,CAAD,CAApB;SAEOL,WAAW,CAACM,IAAZ,CAAiBJ,KAAjB,IAA0BA,KAA1B,GAAkCK,SAAzC;CAHD;;ACAA,sBAAe,CAACN,IAAD,EAAOO,SAAP,KAAqB;;QAE7BN,KAAK,GAAGO,UAAU,CAACR,IAAD,CAAxB;;MAEI,OAAOC,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAIM,SAA1C,EAAqD;IACpDP,IAAI,CAACS,WAAL,CACC,GAAGC,8BAA8B,CAACH,SAAS,CAACN,KAAD,CAAV,EAAmBD,IAAI,CAACW,IAAL,CAAUC,MAA7B,CADlC;;CALF;;AAYA,MAAMF,8BAA8B,GAAG,CAACG,KAAD,EAAQC,aAAR,KAA0B;QAC1DC,WAAW,GAAGC,aAAa,CAACH,KAAD,EAAQ,IAAR,CAAjC;;MAEIE,WAAW,CAAC,CAAD,CAAf,EAAoB;IACnBA,WAAW,CAAC,CAAD,CAAX,CAAeJ,IAAf,CAAoBC,MAApB,GAA6BE,aAA7B;;;SAGMC,WAAP;CAPD;;;AAWA,MAAMC,aAAa,GAAG,CAACH,KAAD,EAAQI,MAAR,KAAmBJ,KAAK,CAACK,GAAN,CAAUlB,IAAI,IAAImB,YAAY,CAACnB,IAAD,EAAOiB,MAAP,CAA9B,CAAzC;;;AAGA,MAAME,YAAY,GAAG,CAACnB,IAAD,EAAOiB,MAAP,KAAkB;QAChCG,SAAS,GAAG,IAAIpB,IAAI,CAACqB,WAAT,CAAqBrB,IAArB,CAAlB;;OAEK,MAAMsB,GAAX,IAAkBtB,IAAlB,EAAwB;QACnBsB,GAAG,KAAK,QAAZ,EAAsB;MACrBF,SAAS,CAACH,MAAV,GAAmBA,MAAnB;KADD,MAEO,IAAIM,MAAM,CAACvB,IAAI,CAACsB,GAAD,CAAL,CAAN,CAAkBD,WAAlB,KAAkCG,KAAtC,EAA6C;MACnDJ,SAAS,CAACE,GAAD,CAAT,GAAiBN,aAAa,CAAChB,IAAI,CAACG,KAAN,EAAaiB,SAAb,CAA9B;KADM,MAEA,IAAIG,MAAM,CAACvB,IAAI,CAACsB,GAAD,CAAL,CAAN,CAAkBD,WAAlB,KAAkCE,MAAtC,EAA8C;MACpDH,SAAS,CAACE,GAAD,CAAT,GAAiBC,MAAM,CAACE,MAAP,CAAc,EAAd,EAAkBzB,IAAI,CAACsB,GAAD,CAAtB,CAAjB;;;;SAIKF,SAAP;CAbD;;AC7BA;AACA,iBAAgBpB,IAAD,IAAUA,IAAI,IAAIA,IAAI,CAAC0B,IAAL,KAAc,MAAtB,IAAgC1B,IAAI,CAACC,KAAL,KAAe,KAAxE;;ACEe,SAAS0B,IAAT,CAAc3B,IAAd,EAAoB4B,EAApB,EAAwB;EACtC5B,IAAI,CAACG,KAAL,CAAWC,KAAX,CAAiB,CAAjB,EAAoByB,OAApB,CAA4BC,SAAS,IAAI;QACpCA,SAAS,CAAC3B,KAAd,EAAqB;MACpBwB,IAAI,CAACG,SAAD,EAAYF,EAAZ,CAAJ;;;QAGGG,SAAS,CAACD,SAAD,CAAb,EAA0B;MACzBF,EAAE,CAACE,SAAD,CAAF;;GANF;;;ACCD,wBAAe,CAACE,aAAD,EAAgBzB,SAAhB,KAA8B;;QAEtC0B,GAAG,GAAGC,MAAM,CAACF,aAAD,CAAN,CAAsBG,KAAtB,EAAZ,CAF4C;;EAK5CC,IAAY,CAACH,GAAD,EAAMjC,IAAI,IAAI;;IAEzBqC,cAAc,CAACrC,IAAD,EAAOO,SAAP,CAAd;GAFW,CAAZ,CAL4C;;SAWrCL,MAAM,CAAC+B,GAAD,CAAb;CAXD;;ACLA;AACA,gBAAgBjC,IAAD,IAAUA,IAAI,IAAIA,IAAI,CAAC0B,IAAL,KAAc,QAA/C;;ACDA;AACA,cAAgB1B,IAAD,IAAUA,IAAI,IAAIA,IAAI,CAAC0B,IAAL,KAAc,MAA/C;;ACGA,yBAAgB1B,IAAD,IAAUsC,QAAQ,CAACtC,IAAD,CAAR,IAAkBA,IAAI,CAACuC,MAAvB,IAAiCC,MAAM,CAACxC,IAAD,CAAN,IAAgBA,IAAI,CAACC,KAA/E;;ACAe,4BAAUD,IAAV,EAAgBC,KAAhB,EAAuB;MACjCqC,QAAQ,CAACtC,IAAD,CAAZ,EAAoB;IACnBA,IAAI,CAACuC,MAAL,GAActC,KAAd;;;MAGGuC,MAAM,CAACxC,IAAD,CAAV,EAAkB;IACjBA,IAAI,CAACC,KAAL,GAAaA,KAAb;;;;ACNF;;;AAGA,SAASwC,oCAAT,CAA8CC,MAA9C,EAAsD;QAC/CC,oBAAoB,GAAGpB,MAAM,CAACE,MAAP,CAC5B,EAD4B,EAE5BF,MAAM,CAACmB,MAAD,CAAN,CAAeC,oBAAf,IAAuCpB,MAAM,CAACmB,MAAD,CAAN,CAAe,uBAAf,CAFX,CAA7B;;OAKK,MAAMpB,GAAX,IAAkBqB,oBAAlB,EAAwC;IACvCA,oBAAoB,CAACrB,GAAD,CAApB,GAA4BsB,MAAW,CAACD,oBAAoB,CAACrB,GAAD,CAArB,CAAX,CAAuCa,KAAvC,GAA+ChC,KAA3E;;;SAGMwC,oBAAP;;;;;;SAMcE;;;;;;;;8DAAf,WAAsDC,IAAtD,EAA4D;UACrDJ,MAAM,SAASK,QAAQ,CAACC,IAAI,CAACC,OAAL,CAAaH,IAAb,CAAD,CAA7B;WAEOL,oCAAoC,CAACC,MAAD,CAA3C;;;;;SAMcQ;;;;;;;;4DAAf,WAAoDJ,IAApD,EAA0D;UACnDJ,MAAM,SAAS,OAAOM,IAAI,CAACC,OAAL,CAAaH,IAAb,CAAP,CAArB;WAEOL,oCAAoC,CAACC,MAAD,CAA3C;;;;;AAMD,AAAe,SAASS,qCAAT,CAA+CC,OAA/C,EAAwD;SAC/DA,OAAO,CAAClC,GAAR,CAAYmC,MAAM,IAAI;QACxBA,MAAM,YAAYC,OAAtB,EAA+B;aACvBD,MAAP;KADD,MAEO,IAAIA,MAAM,YAAYE,QAAtB,EAAgC;aAC/BF,MAAM,EAAb;KAJ2B;;;UAQtBG,IAAI,GAAGH,MAAM,KAAK9B,MAAM,CAAC8B,MAAD,CAAjB,GAA4BA,MAA5B,GAAqC;MAAEP,IAAI,EAAE5C,MAAM,CAACmD,MAAD;KAAhE,CAR4B;;QAWxBG,IAAI,CAACb,oBAAL,IAA6Ba,IAAI,CAAC,uBAAD,CAArC,EAAgE;aACxDA,IAAP;KAZ2B;;;UAgBtBV,IAAI,GAAG5C,MAAM,CAACsD,IAAI,CAACV,IAAL,IAAa,EAAd,CAAnB,CAhB4B;;UAmBtBpB,IAAI,GAAG,CAAC8B,IAAI,CAAC9B,IAAL,IAAasB,IAAI,CAACS,OAAL,CAAaX,IAAb,EAAmB1C,KAAnB,CAAyB,CAAzB,CAAd,EAA2CsD,WAA3C,EAAb;WAEO;MAAEhC,IAAF;MAAQoB;KAAf;GArBM,EAsBJa,MAtBI;;;iCAsBG,WAAOhB,oBAAP,EAA6BU,MAA7B,EAAwC;0BACpBA,MADoB;YACzC3B,IADyC,SACzCA,IADyC;YACnCoB,IADmC,SACnCA,IADmC;;UAG7CpB,IAAI,KAAK,IAAb,EAAmB;eACXH,MAAM,CAACE,MAAP,CAAckB,oBAAd,SAA0CO,oCAAoC,CAACJ,IAAD,CAA9E,EAAP;;;UAGGpB,IAAI,KAAK,MAAb,EAAqB;eACbH,MAAM,CAACE,MAAP,CAAckB,oBAAd,SAA0CE,sCAAsC,CAACC,IAAD,CAAhF,EAAP;;;aAGMvB,MAAM,CAACE,MAAP,CAAckB,oBAAd,EAAoCF,oCAAoC,QAAOY,MAAP,EAAxE,CAAP;KAjCM;;;;;OAkCJ,EAlCI,CAAP;;;;;AAwCD,MAAMO,QAAQ,GAAGd,IAAI,IAAI,IAAIQ,OAAJ,CAAY,CAACL,OAAD,EAAUY,MAAV,KAAqB;EACzDC,EAAE,CAACF,QAAH,CAAYd,IAAZ,EAAkB,MAAlB,EAA0B,CAACiB,KAAD,EAAQC,MAAR,KAAmB;QACxCD,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNd,OAAO,CAACe,MAAD,CAAP;;GAJF;CADwB,CAAzB;;AAUA,MAAMjB,QAAQ;;AAAA;gCAAG,WAAMD,IAAN;WAAcmB,IAAI,CAAC9B,KAAL,QAAiByB,QAAQ,CAACd,IAAD,CAAzB,EAAd;GAAH;;kBAARC,QAAQ;;;GAAd;;ACtFA,YAAemB,OAAO,CAACC,MAAR,CAAe,gBAAf,EAAiCX,IAAI,IAAI;;QAEjDY,UAAU,GAAG,GAAGC,MAAH,CAAU9C,MAAM,CAACiC,IAAD,CAAN,CAAaY,UAAb,IAA2B,EAArC,CAAnB,CAFuD;;QAKjDE,2BAA2B,GAAGnB,qCAAqC,CAACiB,UAAD,CAAzE;;;;mCAEO,WAAMG,IAAN,EAAc;cACd5B,oBAAoB,SAAS2B,2BAAnC;QAEAC,IAAI,CAAC5C,IAAL,CAAU3B,IAAI,IAAI;gBACXwE,cAAc,GAAGC,iBAAiB,CAACzE,IAAD,CAAxC;;cAEIwE,cAAJ,EAAoB;kBACbE,aAAa,GAAGC,gBAAgB,CAACH,cAAD,EAAiB7B,oBAAjB,CAAtC;;gBAEI+B,aAAa,KAAKF,cAAtB,EAAsC;cACrCI,iBAAiB,CAAC5E,IAAD,EAAO0E,aAAP,CAAjB;;;SAPH;OAHD;;;;;;;CAPc,CAAf;;;;"}