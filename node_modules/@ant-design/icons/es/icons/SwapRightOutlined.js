import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwapRightOutlinedSvg from "@ant-design/icons-svg/es/asn/SwapRightOutlined";
import AntdIcon from '../components/AntdIcon';
var SwapRightOutlined = function SwapRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwapRightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SwapRightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SwapRightOutlined';
}
export default RefIcon;