import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SortDescendingOutlinedSvg from "@ant-design/icons-svg/es/asn/SortDescendingOutlined";
import AntdIcon from '../components/AntdIcon';
var SortDescendingOutlined = function SortDescendingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SortDescendingOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SortDescendingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SortDescendingOutlined';
}
export default RefIcon;