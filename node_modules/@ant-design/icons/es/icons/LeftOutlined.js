import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftOutlined";
import AntdIcon from '../components/AntdIcon';
var LeftOutlined = function LeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftOutlined';
}
export default RefIcon;