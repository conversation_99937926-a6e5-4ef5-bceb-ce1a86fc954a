import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TabletOutlinedSvg from "@ant-design/icons-svg/es/asn/TabletOutlined";
import AntdIcon from '../components/AntdIcon';
var TabletOutlined = function TabletOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TabletOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TabletOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TabletOutlined';
}
export default RefIcon;