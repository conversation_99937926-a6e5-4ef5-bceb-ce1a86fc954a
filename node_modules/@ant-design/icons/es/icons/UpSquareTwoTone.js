import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/UpSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var UpSquareTwoTone = function UpSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpSquareTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UpSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareTwoTone';
}
export default RefIcon;