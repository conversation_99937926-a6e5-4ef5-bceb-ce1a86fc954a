import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TikTokOutlinedSvg from "@ant-design/icons-svg/es/asn/TikTokOutlined";
import AntdIcon from '../components/AntdIcon';
var TikTokOutlined = function TikTokOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TikTokOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TikTokOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TikTokOutlined';
}
export default RefIcon;