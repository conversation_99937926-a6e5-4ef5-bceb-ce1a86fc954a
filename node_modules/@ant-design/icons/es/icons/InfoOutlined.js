import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InfoOutlinedSvg from "@ant-design/icons-svg/es/asn/InfoOutlined";
import AntdIcon from '../components/AntdIcon';
var InfoOutlined = function InfoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InfoOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(InfoOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InfoOutlined';
}
export default RefIcon;