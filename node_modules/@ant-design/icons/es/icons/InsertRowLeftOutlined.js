import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsertRowLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var InsertRowLeftOutlined = function InsertRowLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsertRowLeftOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(InsertRowLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsertRowLeftOutlined';
}
export default RefIcon;