import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HourglassOutlinedSvg from "@ant-design/icons-svg/es/asn/HourglassOutlined";
import AntdIcon from '../components/AntdIcon';
var HourglassOutlined = function HourglassOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HourglassOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(HourglassOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HourglassOutlined';
}
export default RefIcon;