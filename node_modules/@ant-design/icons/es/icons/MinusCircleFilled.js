import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusCircleFilledSvg from "@ant-design/icons-svg/es/asn/MinusCircleFilled";
import AntdIcon from '../components/AntdIcon';
var MinusCircleFilled = function MinusCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MinusCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MinusCircleFilled';
}
export default RefIcon;