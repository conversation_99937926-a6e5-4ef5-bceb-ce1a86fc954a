import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LoadingOutlinedSvg from "@ant-design/icons-svg/es/asn/LoadingOutlined";
import AntdIcon from '../components/AntdIcon';
var LoadingOutlined = function LoadingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LoadingOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LoadingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LoadingOutlined';
}
export default RefIcon;