import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HddTwoToneSvg from "@ant-design/icons-svg/es/asn/HddTwoTone";
import AntdIcon from '../components/AntdIcon';
var HddTwoTone = function HddTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HddTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(HddTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HddTwoTone';
}
export default RefIcon;