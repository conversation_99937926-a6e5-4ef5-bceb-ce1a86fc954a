import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MobileFilledSvg from "@ant-design/icons-svg/es/asn/MobileFilled";
import AntdIcon from '../components/AntdIcon';
var MobileFilled = function MobileFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MobileFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MobileFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MobileFilled';
}
export default RefIcon;