import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TwitchFilledSvg from "@ant-design/icons-svg/es/asn/TwitchFilled";
import AntdIcon from '../components/AntdIcon';
var TwitchFilled = function TwitchFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TwitchFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TwitchFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TwitchFilled';
}
export default RefIcon;