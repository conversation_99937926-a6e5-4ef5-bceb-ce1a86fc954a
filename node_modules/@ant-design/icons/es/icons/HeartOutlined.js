import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HeartOutlinedSvg from "@ant-design/icons-svg/es/asn/HeartOutlined";
import AntdIcon from '../components/AntdIcon';
var HeartOutlined = function HeartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HeartOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(HeartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HeartOutlined';
}
export default RefIcon;