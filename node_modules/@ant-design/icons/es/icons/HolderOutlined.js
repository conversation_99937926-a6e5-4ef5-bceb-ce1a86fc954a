import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HolderOutlinedSvg from "@ant-design/icons-svg/es/asn/HolderOutlined";
import AntdIcon from '../components/AntdIcon';
var HolderOutlined = function HolderOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HolderOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(HolderOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HolderOutlined';
}
export default RefIcon;