import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MedicineBoxFilledSvg from "@ant-design/icons-svg/es/asn/MedicineBoxFilled";
import AntdIcon from '../components/AntdIcon';
var MedicineBoxFilled = function MedicineBoxFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MedicineBoxFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MedicineBoxFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MedicineBoxFilled';
}
export default RefIcon;