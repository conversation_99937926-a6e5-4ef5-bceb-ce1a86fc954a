import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MailTwoToneSvg from "@ant-design/icons-svg/es/asn/MailTwoTone";
import AntdIcon from '../components/AntdIcon';
var MailTwoTone = function MailTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MailTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MailTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MailTwoTone';
}
export default RefIcon;