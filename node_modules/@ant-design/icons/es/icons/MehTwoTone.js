import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MehTwoToneSvg from "@ant-design/icons-svg/es/asn/MehTwoTone";
import AntdIcon from '../components/AntdIcon';
var MehTwoTone = function MehTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MehTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MehTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MehTwoTone';
}
export default RefIcon;