import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MergeCellsOutlinedSvg from "@ant-design/icons-svg/es/asn/MergeCellsOutlined";
import AntdIcon from '../components/AntdIcon';
var MergeCellsOutlined = function MergeCellsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MergeCellsOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MergeCellsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MergeCellsOutlined';
}
export default RefIcon;