import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingFilledSvg from "@ant-design/icons-svg/es/asn/ShoppingFilled";
import AntdIcon from '../components/AntdIcon';
var ShoppingFilled = function ShoppingFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ShoppingFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShoppingFilled';
}
export default RefIcon;