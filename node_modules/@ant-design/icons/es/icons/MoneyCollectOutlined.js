import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoneyCollectOutlinedSvg from "@ant-design/icons-svg/es/asn/MoneyCollectOutlined";
import AntdIcon from '../components/AntdIcon';
var MoneyCollectOutlined = function MoneyCollectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoneyCollectOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MoneyCollectOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoneyCollectOutlined';
}
export default RefIcon;