import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TabletFilledSvg from "@ant-design/icons-svg/es/asn/TabletFilled";
import AntdIcon from '../components/AntdIcon';
var TabletFilled = function TabletFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TabletFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TabletFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TabletFilled';
}
export default RefIcon;