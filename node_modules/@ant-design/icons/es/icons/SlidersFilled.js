import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlidersFilledSvg from "@ant-design/icons-svg/es/asn/SlidersFilled";
import AntdIcon from '../components/AntdIcon';
var SlidersFilled = function SlidersFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlidersFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SlidersFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlidersFilled';
}
export default RefIcon;