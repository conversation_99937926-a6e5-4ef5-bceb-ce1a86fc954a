import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Html5FilledSvg from "@ant-design/icons-svg/es/asn/Html5Filled";
import AntdIcon from '../components/AntdIcon';
var Html5Filled = function Html5Filled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: Html5FilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(Html5Filled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'Html5Filled';
}
export default RefIcon;