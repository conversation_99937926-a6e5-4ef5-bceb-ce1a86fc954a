import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Loading3QuartersOutlinedSvg from "@ant-design/icons-svg/es/asn/Loading3QuartersOutlined";
import AntdIcon from '../components/AntdIcon';
var Loading3QuartersOutlined = function Loading3QuartersOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: Loading3QuartersOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(Loading3QuartersOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'Loading3QuartersOutlined';
}
export default RefIcon;