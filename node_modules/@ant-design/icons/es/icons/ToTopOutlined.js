import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ToTopOutlinedSvg from "@ant-design/icons-svg/es/asn/ToTopOutlined";
import AntdIcon from '../components/AntdIcon';
var ToTopOutlined = function ToTopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ToTopOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ToTopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ToTopOutlined';
}
export default RefIcon;