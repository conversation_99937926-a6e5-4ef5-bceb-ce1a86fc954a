import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingOutlinedSvg from "@ant-design/icons-svg/es/asn/ShoppingOutlined";
import AntdIcon from '../components/AntdIcon';
var ShoppingOutlined = function ShoppingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ShoppingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShoppingOutlined';
}
export default RefIcon;