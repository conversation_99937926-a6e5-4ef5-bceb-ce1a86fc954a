import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SunOutlinedSvg from "@ant-design/icons-svg/es/asn/SunOutlined";
import AntdIcon from '../components/AntdIcon';
var SunOutlined = function SunOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SunOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SunOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SunOutlined';
}
export default RefIcon;