import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MobileTwoToneSvg from "@ant-design/icons-svg/es/asn/MobileTwoTone";
import AntdIcon from '../components/AntdIcon';
var MobileTwoTone = function MobileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MobileTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MobileTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MobileTwoTone';
}
export default RefIcon;