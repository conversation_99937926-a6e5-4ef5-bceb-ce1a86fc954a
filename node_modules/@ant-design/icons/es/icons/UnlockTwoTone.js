import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnlockTwoToneSvg from "@ant-design/icons-svg/es/asn/UnlockTwoTone";
import AntdIcon from '../components/AntdIcon';
var UnlockTwoTone = function UnlockTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnlockTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UnlockTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UnlockTwoTone';
}
export default RefIcon;