import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StepForwardFilledSvg from "@ant-design/icons-svg/es/asn/StepForwardFilled";
import AntdIcon from '../components/AntdIcon';
var StepForwardFilled = function StepForwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StepForwardFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(StepForwardFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StepForwardFilled';
}
export default RefIcon;