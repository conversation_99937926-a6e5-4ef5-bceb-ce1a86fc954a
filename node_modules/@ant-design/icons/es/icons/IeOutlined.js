import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IeOutlinedSvg from "@ant-design/icons-svg/es/asn/IeOutlined";
import AntdIcon from '../components/AntdIcon';
var IeOutlined = function IeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(IeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'IeOutlined';
}
export default RefIcon;