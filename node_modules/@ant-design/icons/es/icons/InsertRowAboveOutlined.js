import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsertRowAboveOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowAboveOutlined";
import AntdIcon from '../components/AntdIcon';
var InsertRowAboveOutlined = function InsertRowAboveOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsertRowAboveOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(InsertRowAboveOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsertRowAboveOutlined';
}
export default RefIcon;