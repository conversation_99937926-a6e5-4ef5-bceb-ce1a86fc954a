import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import JavaScriptOutlinedSvg from "@ant-design/icons-svg/es/asn/JavaScriptOutlined";
import AntdIcon from '../components/AntdIcon';
var JavaScriptOutlined = function JavaScriptOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: JavaScriptOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(JavaScriptOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'JavaScriptOutlined';
}
export default RefIcon;