import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SunFilledSvg from "@ant-design/icons-svg/es/asn/SunFilled";
import AntdIcon from '../components/AntdIcon';
var SunFilled = function SunFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SunFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SunFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SunFilled';
}
export default RefIcon;