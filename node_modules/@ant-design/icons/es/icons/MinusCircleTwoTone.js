import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/MinusCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var MinusCircleTwoTone = function MinusCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MinusCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MinusCircleTwoTone';
}
export default RefIcon;