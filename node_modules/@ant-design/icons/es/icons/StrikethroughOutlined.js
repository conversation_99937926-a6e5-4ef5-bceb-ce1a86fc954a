import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StrikethroughOutlinedSvg from "@ant-design/icons-svg/es/asn/StrikethroughOutlined";
import AntdIcon from '../components/AntdIcon';
var StrikethroughOutlined = function StrikethroughOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StrikethroughOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(StrikethroughOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StrikethroughOutlined';
}
export default RefIcon;