import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/SlackSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var SlackSquareOutlined = function SlackSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackSquareOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SlackSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackSquareOutlined';
}
export default RefIcon;