import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ItalicOutlinedSvg from "@ant-design/icons-svg/es/asn/ItalicOutlined";
import AntdIcon from '../components/AntdIcon';
var ItalicOutlined = function ItalicOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ItalicOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ItalicOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ItalicOutlined';
}
export default RefIcon;