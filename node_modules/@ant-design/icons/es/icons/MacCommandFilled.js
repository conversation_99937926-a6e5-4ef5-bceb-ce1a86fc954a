import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MacCommandFilledSvg from "@ant-design/icons-svg/es/asn/MacCommandFilled";
import AntdIcon from '../components/AntdIcon';
var MacCommandFilled = function MacCommandFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MacCommandFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MacCommandFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MacCommandFilled';
}
export default RefIcon;