import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MessageOutlinedSvg from "@ant-design/icons-svg/es/asn/MessageOutlined";
import AntdIcon from '../components/AntdIcon';
var MessageOutlined = function MessageOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MessageOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MessageOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MessageOutlined';
}
export default RefIcon;