import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/MinusSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var MinusSquareOutlined = function MinusSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusSquareOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MinusSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MinusSquareOutlined';
}
export default RefIcon;