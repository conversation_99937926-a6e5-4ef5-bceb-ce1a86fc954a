import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SpotifyFilledSvg from "@ant-design/icons-svg/es/asn/SpotifyFilled";
import AntdIcon from '../components/AntdIcon';
var SpotifyFilled = function SpotifyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SpotifyFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SpotifyFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SpotifyFilled';
}
export default RefIcon;