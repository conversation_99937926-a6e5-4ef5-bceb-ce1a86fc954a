import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IdcardOutlinedSvg from "@ant-design/icons-svg/es/asn/IdcardOutlined";
import AntdIcon from '../components/AntdIcon';
var IdcardOutlined = function IdcardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IdcardOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(IdcardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'IdcardOutlined';
}
export default RefIcon;