import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ThunderboltFilledSvg from "@ant-design/icons-svg/es/asn/ThunderboltFilled";
import AntdIcon from '../components/AntdIcon';
var ThunderboltFilled = function ThunderboltFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ThunderboltFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ThunderboltFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ThunderboltFilled';
}
export default RefIcon;