import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var LeftSquareOutlined = function LeftSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftSquareOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LeftSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftSquareOutlined';
}
export default RefIcon;