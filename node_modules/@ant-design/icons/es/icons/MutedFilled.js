import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MutedFilledSvg from "@ant-design/icons-svg/es/asn/MutedFilled";
import AntdIcon from '../components/AntdIcon';
var MutedFilled = function MutedFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MutedFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MutedFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MutedFilled';
}
export default RefIcon;