import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import JavaOutlinedSvg from "@ant-design/icons-svg/es/asn/JavaOutlined";
import AntdIcon from '../components/AntdIcon';
var JavaOutlined = function JavaOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: JavaOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(JavaOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'JavaOutlined';
}
export default RefIcon;