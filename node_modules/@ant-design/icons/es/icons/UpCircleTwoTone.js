import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/UpCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var UpCircleTwoTone = function UpCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UpCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpCircleTwoTone';
}
export default RefIcon;