import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShakeOutlinedSvg from "@ant-design/icons-svg/es/asn/ShakeOutlined";
import AntdIcon from '../components/AntdIcon';
var ShakeOutlined = function ShakeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShakeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ShakeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShakeOutlined';
}
export default RefIcon;