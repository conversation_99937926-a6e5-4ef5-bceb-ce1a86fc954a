import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LayoutOutlinedSvg from "@ant-design/icons-svg/es/asn/LayoutOutlined";
import AntdIcon from '../components/AntdIcon';
var LayoutOutlined = function LayoutOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LayoutOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LayoutOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LayoutOutlined';
}
export default RefIcon;