import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TruckFilledSvg from "@ant-design/icons-svg/es/asn/TruckFilled";
import AntdIcon from '../components/AntdIcon';
var TruckFilled = function TruckFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TruckFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TruckFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TruckFilled';
}
export default RefIcon;