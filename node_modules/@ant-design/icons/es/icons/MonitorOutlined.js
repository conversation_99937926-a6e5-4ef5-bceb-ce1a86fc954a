import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MonitorOutlinedSvg from "@ant-design/icons-svg/es/asn/MonitorOutlined";
import AntdIcon from '../components/AntdIcon';
var MonitorOutlined = function MonitorOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MonitorOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MonitorOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MonitorOutlined';
}
export default RefIcon;