import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TwitchOutlinedSvg from "@ant-design/icons-svg/es/asn/TwitchOutlined";
import AntdIcon from '../components/AntdIcon';
var TwitchOutlined = function TwitchOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TwitchOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TwitchOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TwitchOutlined';
}
export default RefIcon;