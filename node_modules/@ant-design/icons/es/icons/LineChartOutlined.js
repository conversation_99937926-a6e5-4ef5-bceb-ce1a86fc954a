import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LineChartOutlinedSvg from "@ant-design/icons-svg/es/asn/LineChartOutlined";
import AntdIcon from '../components/AntdIcon';
var LineChartOutlined = function LineChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LineChartOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LineChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LineChartOutlined';
}
export default RefIcon;