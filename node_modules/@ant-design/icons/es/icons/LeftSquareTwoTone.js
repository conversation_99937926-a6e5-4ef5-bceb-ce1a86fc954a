import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/LeftSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var LeftSquareTwoTone = function LeftSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftSquareTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LeftSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftSquareTwoTone';
}
export default RefIcon;