import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MediumWorkmarkOutlinedSvg from "@ant-design/icons-svg/es/asn/MediumWorkmarkOutlined";
import AntdIcon from '../components/AntdIcon';
var MediumWorkmarkOutlined = function MediumWorkmarkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MediumWorkmarkOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MediumWorkmarkOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MediumWorkmarkOutlined';
}
export default RefIcon;