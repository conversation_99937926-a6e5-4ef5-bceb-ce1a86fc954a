import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MedicineBoxOutlinedSvg from "@ant-design/icons-svg/es/asn/MedicineBoxOutlined";
import AntdIcon from '../components/AntdIcon';
var MedicineBoxOutlined = function MedicineBoxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MedicineBoxOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MedicineBoxOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MedicineBoxOutlined';
}
export default RefIcon;