import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoneyCollectFilledSvg from "@ant-design/icons-svg/es/asn/MoneyCollectFilled";
import AntdIcon from '../components/AntdIcon';
var MoneyCollectFilled = function MoneyCollectFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoneyCollectFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MoneyCollectFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoneyCollectFilled';
}
export default RefIcon;