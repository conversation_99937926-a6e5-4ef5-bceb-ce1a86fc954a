import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/LeftCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var LeftCircleTwoTone = function LeftCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LeftCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftCircleTwoTone';
}
export default RefIcon;