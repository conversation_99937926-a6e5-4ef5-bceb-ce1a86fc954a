import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HighlightOutlinedSvg from "@ant-design/icons-svg/es/asn/HighlightOutlined";
import AntdIcon from '../components/AntdIcon';
var HighlightOutlined = function HighlightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HighlightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(HighlightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HighlightOutlined';
}
export default RefIcon;