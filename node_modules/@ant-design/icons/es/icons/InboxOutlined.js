import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InboxOutlinedSvg from "@ant-design/icons-svg/es/asn/InboxOutlined";
import AntdIcon from '../components/AntdIcon';
var InboxOutlined = function InboxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InboxOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(InboxOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InboxOutlined';
}
export default RefIcon;