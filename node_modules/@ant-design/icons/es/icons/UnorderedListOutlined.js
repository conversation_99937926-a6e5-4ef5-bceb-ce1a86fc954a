import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnorderedListOutlinedSvg from "@ant-design/icons-svg/es/asn/UnorderedListOutlined";
import AntdIcon from '../components/AntdIcon';
var UnorderedListOutlined = function UnorderedListOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnorderedListOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UnorderedListOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UnorderedListOutlined';
}
export default RefIcon;