import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LoginOutlinedSvg from "@ant-design/icons-svg/es/asn/LoginOutlined";
import AntdIcon from '../components/AntdIcon';
var LoginOutlined = function LoginOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LoginOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LoginOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LoginOutlined';
}
export default RefIcon;