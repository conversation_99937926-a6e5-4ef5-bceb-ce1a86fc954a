import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnderlineOutlinedSvg from "@ant-design/icons-svg/es/asn/UnderlineOutlined";
import AntdIcon from '../components/AntdIcon';
var UnderlineOutlined = function UnderlineOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnderlineOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UnderlineOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UnderlineOutlined';
}
export default RefIcon;