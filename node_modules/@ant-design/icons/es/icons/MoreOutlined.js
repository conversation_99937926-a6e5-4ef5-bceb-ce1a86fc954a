import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoreOutlinedSvg from "@ant-design/icons-svg/es/asn/MoreOutlined";
import AntdIcon from '../components/AntdIcon';
var MoreOutlined = function MoreOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoreOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MoreOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoreOutlined';
}
export default RefIcon;