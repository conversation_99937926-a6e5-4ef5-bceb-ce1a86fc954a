import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftSquareFilledSvg from "@ant-design/icons-svg/es/asn/LeftSquareFilled";
import AntdIcon from '../components/AntdIcon';
var LeftSquareFilled = function LeftSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftSquareFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(LeftSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftSquareFilled';
}
export default RefIcon;