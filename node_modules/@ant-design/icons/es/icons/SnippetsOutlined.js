import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SnippetsOutlinedSvg from "@ant-design/icons-svg/es/asn/SnippetsOutlined";
import AntdIcon from '../components/AntdIcon';
var SnippetsOutlined = function SnippetsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SnippetsOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SnippetsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SnippetsOutlined';
}
export default RefIcon;