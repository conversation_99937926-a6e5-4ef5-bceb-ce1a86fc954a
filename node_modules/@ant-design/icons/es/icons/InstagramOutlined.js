import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InstagramOutlinedSvg from "@ant-design/icons-svg/es/asn/InstagramOutlined";
import AntdIcon from '../components/AntdIcon';
var InstagramOutlined = function InstagramOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InstagramOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(InstagramOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InstagramOutlined';
}
export default RefIcon;