# Changes to PostCSS Focus Visible

### 4.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

### 3.0.0 (April 7, 2018)

- Changed: default functionality to preserve the original rule
- Added: `preserve` option to preserve the original rule using `:focus-visible`

### 2.0.0 (February 17, 2018)

- Changed `:focus-ring` to `:focus-visible` per the specification
- Removed `assignTo` export option

### 1.0.0 (May 22, 2017)

- Initial version
