{"version": 3, "file": "index.cjs.js", "sources": ["index.js"], "sourcesContent": ["import { lab2rgb, lch2rgb } from '@csstools/convert-colors';\nimport postcss from 'postcss';\nimport parser from 'postcss-values-parser';\n\nexport default postcss.plugin('postcss-lab-function', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : false;\n\n\treturn root => {\n\t\troot.walkDecls(decl => {\n\t\t\tconst { value } = decl;\n\n\t\t\tif (colorAnyRegExp.test(value)) {\n\t\t\t\tconst ast = parser(value).parse();\n\n\t\t\t\tast.walkType('func', node => {\n\t\t\t\t\tif (colorRegExp.test(node.value)) {\n\t\t\t\t\t\tconst children = node.nodes.slice(1, -1);\n\t\t\t\t\t\tconst isLab = labRegExp.test(node.value);\n\t\t\t\t\t\tconst isGray = grayRegExp.test(node.value);\n\t\t\t\t\t\tconst isFunctionalLAB = !isGray && matchFunctionalLAB(children);\n\t\t\t\t\t\tconst isFunctionalLCH = !isGray && matchFunctionalLCH(children);\n\t\t\t\t\t\tconst isFunctionalGray = isGray && matchFunctionalGray(children);\n\n\t\t\t\t\t\tif (isFunctionalLAB || isFunctionalLCH) {\n\t\t\t\t\t\t\tnode.value = 'rgb';\n\n\t\t\t\t\t\t\tconst slashNode = children[3];\n\t\t\t\t\t\t\tconst alphaNode = children[4];\n\n\t\t\t\t\t\t\tif (alphaNode) {\n\t\t\t\t\t\t\t\tif (isPercentage(alphaNode) && !isCalc(alphaNode)) {\n\t\t\t\t\t\t\t\t\talphaNode.unit = '';\n\t\t\t\t\t\t\t\t\talphaNode.value = String(alphaNode.value / 100);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (alphaNode.value === '1') {\n\t\t\t\t\t\t\t\t\tslashNode.remove();\n\t\t\t\t\t\t\t\t\talphaNode.remove();\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tnode.value += 'a';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (slashNode && isSlash(slashNode)) {\n\t\t\t\t\t\t\t\tslashNode.replaceWith( newComma() );\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconst converter = isLab ? lab2rgb : lch2rgb;\n\n\t\t\t\t\t\t\tconst rgbValues = converter(\n\t\t\t\t\t\t\t\t...[\n\t\t\t\t\t\t\t\t\tchildren[0].value,\n\t\t\t\t\t\t\t\t\tchildren[1].value,\n\t\t\t\t\t\t\t\t\tchildren[2].value\n\t\t\t\t\t\t\t\t].map(\n\t\t\t\t\t\t\t\t\tnumber => parseFloat(number)\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t).map(\n\t\t\t\t\t\t\t\tsourceValue => Math.max(Math.min(parseInt(sourceValue * 2.55), 255), 0)\n\t\t\t\t\t\t\t)\n\n\t\t\t\t\t\t\tchildren[0].value = String(rgbValues[0]);\n\t\t\t\t\t\t\tchildren[1].value = String(rgbValues[1]);\n\t\t\t\t\t\t\tchildren[2].value = String(rgbValues[2]);\n\n\t\t\t\t\t\t\tnode.nodes.splice(3, 0, [ newComma() ]);\n\t\t\t\t\t\t\tnode.nodes.splice(2, 0, [ newComma() ]);\n\t\t\t\t\t\t} else if (isFunctionalGray) {\n\t\t\t\t\t\t\tnode.value = 'rgb';\n\n\t\t\t\t\t\t\tconst alphaNode = children[2];\n\n\t\t\t\t\t\t\tconst rgbValues = lab2rgb(\n\t\t\t\t\t\t\t\t...[\n\t\t\t\t\t\t\t\t\tchildren[0].value,\n\t\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\t\t0\n\t\t\t\t\t\t\t\t].map(\n\t\t\t\t\t\t\t\t\tnumber => parseFloat(number)\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t).map(\n\t\t\t\t\t\t\t\tsourceValue => Math.max(Math.min(parseInt(sourceValue * 2.55), 255), 0)\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tnode.removeAll()\n\t\t\t\t\t\t\t.append(newParen('('))\n\t\t\t\t\t\t\t.append(newNumber(rgbValues[0]))\n\t\t\t\t\t\t\t.append(newComma())\n\t\t\t\t\t\t\t.append(newNumber(rgbValues[1]))\n\t\t\t\t\t\t\t.append(newComma())\n\t\t\t\t\t\t\t.append(newNumber(rgbValues[2]))\n\t\t\t\t\t\t\t.append(newParen(')'));\n\n\t\t\t\t\t\t\tif (alphaNode) {\n\t\t\t\t\t\t\t\tif (isPercentage(alphaNode) && !isCalc(alphaNode)) {\n\t\t\t\t\t\t\t\t\talphaNode.unit = '';\n\t\t\t\t\t\t\t\t\talphaNode.value = String(alphaNode.value / 100);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (alphaNode.value !== '1') {\n\t\t\t\t\t\t\t\t\tnode.value += 'a';\n\n\t\t\t\t\t\t\t\t\tnode\n\t\t\t\t\t\t\t\t\t.insertBefore(node.last, newComma())\n\t\t\t\t\t\t\t\t\t.insertBefore(node.last, alphaNode)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tconst newValue = String(ast);\n\n\t\t\t\tif (preserve) {\n\t\t\t\t\tdecl.cloneBefore({ value: newValue });\n\t\t\t\t} else {\n\t\t\t\t\tdecl.value = newValue;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n\nconst colorAnyRegExp = /(^|[^\\w-])(lab|lch|gray)\\(/i;\nconst colorRegExp = /^(lab|lch|gray)$/i;\nconst labRegExp = /^lab$/i;\nconst grayRegExp = /^gray$/i;\nconst alphaUnitMatch = /^%?$/i;\nconst calcFuncMatch = /^calc$/i;\nconst hueUnitMatch = /^(deg|grad|rad|turn)?$/i;\n\nconst isAlphaValue = node => isCalc(node) || node.type === 'number' && alphaUnitMatch.test(node.unit);\nconst isCalc = node => node.type === 'func' && calcFuncMatch.test(node.value);\nconst isHue = node => isCalc(node) || node.type === 'number' && hueUnitMatch.test(node.unit);\nconst isNumber = node => isCalc(node) || node.type === 'number' && node.unit === '';\nconst isPercentage = node => isCalc(node) || node.type === 'number' && node.unit === '%';\nconst isSlash = node => node.type === 'operator' && node.value === '/';\nconst functionalLABMatch = [isNumber, isNumber, isNumber, isSlash, isAlphaValue];\nconst functionalLCHMatch = [isNumber, isNumber, isHue, isSlash, isAlphaValue];\nconst functionalGrayMatch = [isNumber, isSlash, isAlphaValue];\nconst matchFunctionalLAB = children => children.every(\n\t(child, index) => typeof functionalLABMatch[index] === 'function' && functionalLABMatch[index](child)\n);\nconst matchFunctionalLCH = children => children.every(\n\t(child, index) => typeof functionalLCHMatch[index] === 'function' && functionalLCHMatch[index](child)\n);\nconst matchFunctionalGray = children => children.every(\n\t(child, index) => typeof functionalGrayMatch[index] === 'function' && functionalGrayMatch[index](child)\n);\n\nconst newComma = () => parser.comma({ value: ',' });\nconst newNumber = value => parser.number({ value });\nconst newParen = value => parser.paren({ value });\n"], "names": ["postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "value", "colorAnyRegExp", "test", "ast", "parser", "parse", "walkType", "node", "colorRegExp", "children", "nodes", "slice", "isLab", "labRegExp", "<PERSON><PERSON><PERSON>", "grayRegExp", "isFunctionalLAB", "matchFunctionalLAB", "isFunctionalLCH", "matchFunctionalLCH", "isFunctionalGray", "matchFunctionalGray", "slashNode", "alphaNode", "isPercentage", "isCalc", "unit", "String", "remove", "isSlash", "replaceWith", "newComma", "converter", "lab2rgb", "lch2rgb", "rgbValues", "map", "number", "parseFloat", "sourceValue", "Math", "max", "min", "parseInt", "splice", "removeAll", "append", "newParen", "newNumber", "insertBefore", "last", "newValue", "cloneBefore", "alphaUnitMatch", "calcFuncMatch", "hueUnitMatch", "isAlphaValue", "type", "is<PERSON>ue", "isNumber", "functionalLABMatch", "functionalLCHMatch", "functionalGrayMatch", "every", "child", "index", "comma", "paren"], "mappings": ";;;;;;;;AAIA,YAAeA,OAAO,CAACC,MAAR,CAAe,sBAAf,EAAuCC,IAAI,IAAI;QACvDC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,KAAvE;SAEOG,IAAI,IAAI;IACdA,IAAI,CAACC,SAAL,CAAeC,IAAI,IAAI;YACdC,KADc,GACJD,IADI,CACdC,KADc;;UAGlBC,cAAc,CAACC,IAAf,CAAoBF,KAApB,CAAJ,EAAgC;cACzBG,GAAG,GAAGC,MAAM,CAACJ,KAAD,CAAN,CAAcK,KAAd,EAAZ;QAEAF,GAAG,CAACG,QAAJ,CAAa,MAAb,EAAqBC,IAAI,IAAI;cACxBC,WAAW,CAACN,IAAZ,CAAiBK,IAAI,CAACP,KAAtB,CAAJ,EAAkC;kBAC3BS,QAAQ,GAAGF,IAAI,CAACG,KAAL,CAAWC,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,CAAjB;kBACMC,KAAK,GAAGC,SAAS,CAACX,IAAV,CAAeK,IAAI,CAACP,KAApB,CAAd;kBACMc,MAAM,GAAGC,UAAU,CAACb,IAAX,CAAgBK,IAAI,CAACP,KAArB,CAAf;kBACMgB,eAAe,GAAG,CAACF,MAAD,IAAWG,kBAAkB,CAACR,QAAD,CAArD;kBACMS,eAAe,GAAG,CAACJ,MAAD,IAAWK,kBAAkB,CAACV,QAAD,CAArD;kBACMW,gBAAgB,GAAGN,MAAM,IAAIO,mBAAmB,CAACZ,QAAD,CAAtD;;gBAEIO,eAAe,IAAIE,eAAvB,EAAwC;cACvCX,IAAI,CAACP,KAAL,GAAa,KAAb;oBAEMsB,SAAS,GAAGb,QAAQ,CAAC,CAAD,CAA1B;oBACMc,SAAS,GAAGd,QAAQ,CAAC,CAAD,CAA1B;;kBAEIc,SAAJ,EAAe;oBACVC,YAAY,CAACD,SAAD,CAAZ,IAA2B,CAACE,MAAM,CAACF,SAAD,CAAtC,EAAmD;kBAClDA,SAAS,CAACG,IAAV,GAAiB,EAAjB;kBACAH,SAAS,CAACvB,KAAV,GAAkB2B,MAAM,CAACJ,SAAS,CAACvB,KAAV,GAAkB,GAAnB,CAAxB;;;oBAGGuB,SAAS,CAACvB,KAAV,KAAoB,GAAxB,EAA6B;kBAC5BsB,SAAS,CAACM,MAAV;kBACAL,SAAS,CAACK,MAAV;iBAFD,MAGO;kBACNrB,IAAI,CAACP,KAAL,IAAc,GAAd;;;;kBAIEsB,SAAS,IAAIO,OAAO,CAACP,SAAD,CAAxB,EAAqC;gBACpCA,SAAS,CAACQ,WAAV,CAAuBC,QAAQ,EAA/B;;;oBAGKC,SAAS,GAAGpB,KAAK,GAAGqB,qBAAH,GAAaC,qBAApC;oBAEMC,SAAS,GAAGH,SAAS,CAC1B,GAAG,CACFvB,QAAQ,CAAC,CAAD,CAAR,CAAYT,KADV,EAEFS,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAFV,EAGFS,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAHV,EAIDoC,GAJC,CAKFC,MAAM,IAAIC,UAAU,CAACD,MAAD,CALlB,CADuB,CAAT,CAQhBD,GARgB,CASjBG,WAAW,IAAIC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASC,QAAQ,CAACJ,WAAW,GAAG,IAAf,CAAjB,EAAuC,GAAvC,CAAT,EAAsD,CAAtD,CATE,CAAlB;cAYA9B,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB2B,MAAM,CAACQ,SAAS,CAAC,CAAD,CAAV,CAA1B;cACA1B,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB2B,MAAM,CAACQ,SAAS,CAAC,CAAD,CAAV,CAA1B;cACA1B,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB2B,MAAM,CAACQ,SAAS,CAAC,CAAD,CAAV,CAA1B;cAEA5B,IAAI,CAACG,KAAL,CAAWkC,MAAX,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAEb,QAAQ,EAAV,CAAxB;cACAxB,IAAI,CAACG,KAAL,CAAWkC,MAAX,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAEb,QAAQ,EAAV,CAAxB;aA3CD,MA4CO,IAAIX,gBAAJ,EAAsB;cAC5Bb,IAAI,CAACP,KAAL,GAAa,KAAb;oBAEMuB,SAAS,GAAGd,QAAQ,CAAC,CAAD,CAA1B;oBAEM0B,SAAS,GAAGF,qBAAO,CACxB,GAAG,CACFxB,QAAQ,CAAC,CAAD,CAAR,CAAYT,KADV,EAEF,CAFE,EAGF,CAHE,EAIDoC,GAJC,CAKFC,MAAM,IAAIC,UAAU,CAACD,MAAD,CALlB,CADqB,CAAP,CAQhBD,GARgB,CASjBG,WAAW,IAAIC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASC,QAAQ,CAACJ,WAAW,GAAG,IAAf,CAAjB,EAAuC,GAAvC,CAAT,EAAsD,CAAtD,CATE,CAAlB;cAYAhC,IAAI,CAACsC,SAAL,GACCC,MADD,CACQC,QAAQ,CAAC,GAAD,CADhB,EAECD,MAFD,CAEQE,SAAS,CAACb,SAAS,CAAC,CAAD,CAAV,CAFjB,EAGCW,MAHD,CAGQf,QAAQ,EAHhB,EAICe,MAJD,CAIQE,SAAS,CAACb,SAAS,CAAC,CAAD,CAAV,CAJjB,EAKCW,MALD,CAKQf,QAAQ,EALhB,EAMCe,MAND,CAMQE,SAAS,CAACb,SAAS,CAAC,CAAD,CAAV,CANjB,EAOCW,MAPD,CAOQC,QAAQ,CAAC,GAAD,CAPhB;;kBASIxB,SAAJ,EAAe;oBACVC,YAAY,CAACD,SAAD,CAAZ,IAA2B,CAACE,MAAM,CAACF,SAAD,CAAtC,EAAmD;kBAClDA,SAAS,CAACG,IAAV,GAAiB,EAAjB;kBACAH,SAAS,CAACvB,KAAV,GAAkB2B,MAAM,CAACJ,SAAS,CAACvB,KAAV,GAAkB,GAAnB,CAAxB;;;oBAGGuB,SAAS,CAACvB,KAAV,KAAoB,GAAxB,EAA6B;kBAC5BO,IAAI,CAACP,KAAL,IAAc,GAAd;kBAEAO,IAAI,CACH0C,YADD,CACc1C,IAAI,CAAC2C,IADnB,EACyBnB,QAAQ,EADjC,EAECkB,YAFD,CAEc1C,IAAI,CAAC2C,IAFnB,EAEyB3B,SAFzB;;;;;SAxFL;cAiGM4B,QAAQ,GAAGxB,MAAM,CAACxB,GAAD,CAAvB;;YAEIT,QAAJ,EAAc;UACbK,IAAI,CAACqD,WAAL,CAAiB;YAAEpD,KAAK,EAAEmD;WAA1B;SADD,MAEO;UACNpD,IAAI,CAACC,KAAL,GAAamD,QAAb;;;KA5GH;GADD;CAHc,CAAf;AAuHA,MAAMlD,cAAc,GAAG,6BAAvB;AACA,MAAMO,WAAW,GAAG,mBAApB;AACA,MAAMK,SAAS,GAAG,QAAlB;AACA,MAAME,UAAU,GAAG,SAAnB;AACA,MAAMsC,cAAc,GAAG,OAAvB;AACA,MAAMC,aAAa,GAAG,SAAtB;AACA,MAAMC,YAAY,GAAG,yBAArB;;AAEA,MAAMC,YAAY,GAAGjD,IAAI,IAAIkB,MAAM,CAAClB,IAAD,CAAN,IAAgBA,IAAI,CAACkD,IAAL,KAAc,QAAd,IAA0BJ,cAAc,CAACnD,IAAf,CAAoBK,IAAI,CAACmB,IAAzB,CAAvE;;AACA,MAAMD,MAAM,GAAGlB,IAAI,IAAIA,IAAI,CAACkD,IAAL,KAAc,MAAd,IAAwBH,aAAa,CAACpD,IAAd,CAAmBK,IAAI,CAACP,KAAxB,CAA/C;;AACA,MAAM0D,KAAK,GAAGnD,IAAI,IAAIkB,MAAM,CAAClB,IAAD,CAAN,IAAgBA,IAAI,CAACkD,IAAL,KAAc,QAAd,IAA0BF,YAAY,CAACrD,IAAb,CAAkBK,IAAI,CAACmB,IAAvB,CAAhE;;AACA,MAAMiC,QAAQ,GAAGpD,IAAI,IAAIkB,MAAM,CAAClB,IAAD,CAAN,IAAgBA,IAAI,CAACkD,IAAL,KAAc,QAAd,IAA0BlD,IAAI,CAACmB,IAAL,KAAc,EAAjF;;AACA,MAAMF,YAAY,GAAGjB,IAAI,IAAIkB,MAAM,CAAClB,IAAD,CAAN,IAAgBA,IAAI,CAACkD,IAAL,KAAc,QAAd,IAA0BlD,IAAI,CAACmB,IAAL,KAAc,GAArF;;AACA,MAAMG,OAAO,GAAGtB,IAAI,IAAIA,IAAI,CAACkD,IAAL,KAAc,UAAd,IAA4BlD,IAAI,CAACP,KAAL,KAAe,GAAnE;;AACA,MAAM4D,kBAAkB,GAAG,CAACD,QAAD,EAAWA,QAAX,EAAqBA,QAArB,EAA+B9B,OAA/B,EAAwC2B,YAAxC,CAA3B;AACA,MAAMK,kBAAkB,GAAG,CAACF,QAAD,EAAWA,QAAX,EAAqBD,KAArB,EAA4B7B,OAA5B,EAAqC2B,YAArC,CAA3B;AACA,MAAMM,mBAAmB,GAAG,CAACH,QAAD,EAAW9B,OAAX,EAAoB2B,YAApB,CAA5B;;AACA,MAAMvC,kBAAkB,GAAGR,QAAQ,IAAIA,QAAQ,CAACsD,KAAT,CACtC,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOL,kBAAkB,CAACK,KAAD,CAAzB,KAAqC,UAArC,IAAmDL,kBAAkB,CAACK,KAAD,CAAlB,CAA0BD,KAA1B,CAD/B,CAAvC;;AAGA,MAAM7C,kBAAkB,GAAGV,QAAQ,IAAIA,QAAQ,CAACsD,KAAT,CACtC,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOJ,kBAAkB,CAACI,KAAD,CAAzB,KAAqC,UAArC,IAAmDJ,kBAAkB,CAACI,KAAD,CAAlB,CAA0BD,KAA1B,CAD/B,CAAvC;;AAGA,MAAM3C,mBAAmB,GAAGZ,QAAQ,IAAIA,QAAQ,CAACsD,KAAT,CACvC,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOH,mBAAmB,CAACG,KAAD,CAA1B,KAAsC,UAAtC,IAAoDH,mBAAmB,CAACG,KAAD,CAAnB,CAA2BD,KAA3B,CAD/B,CAAxC;;AAIA,MAAMjC,QAAQ,GAAG,MAAM3B,MAAM,CAAC8D,KAAP,CAAa;EAAElE,KAAK,EAAE;CAAtB,CAAvB;;AACA,MAAMgD,SAAS,GAAGhD,KAAK,IAAII,MAAM,CAACiC,MAAP,CAAc;EAAErC;CAAhB,CAA3B;;AACA,MAAM+C,QAAQ,GAAG/C,KAAK,IAAII,MAAM,CAAC+D,KAAP,CAAa;EAAEnE;CAAf,CAA1B;;;;"}