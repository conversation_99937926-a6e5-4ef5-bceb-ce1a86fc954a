{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\n\n// space-separated values splitter\nconst { list: { space } } = postcss\n\n// overflow shorthand property matcher\nconst overflowPropertyRegExp = /^overflow$/i;\n\nexport default postcss.plugin('postcss-overflow-shorthand', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? <PERSON><PERSON>an(opts.preserve) : true;\n\n\treturn root => {\n\t\t// for each overflow declaration\n\t\troot.walkDecls(overflowPropertyRegExp, decl => {\n\t\t\t// split the declaration values\n\t\t\tconst [overflowX, overflowY, ...invalidatingValues] = space(decl.value);\n\n\t\t\t// if there are two values, but no invalidating values\n\t\t\tif (overflowY && !invalidatingValues.length) {\n\t\t\t\t// insert the overflow-* longhand declarations\n\t\t\t\tdecl.cloneBefore({\n\t\t\t\t\tprop: `${decl.prop}-x`,\n\t\t\t\t\tvalue: overflowX\n\t\t\t\t});\n\n\t\t\t\tdecl.cloneBefore({\n\t\t\t\t\tprop: `${decl.prop}-y`,\n\t\t\t\t\tvalue: overflowY\n\t\t\t\t});\n\n\t\t\t\t// conditionally remove the original declaration\n\t\t\t\tif (!preserve) {\n\t\t\t\t\tdecl.remove();\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t};\n});\n"], "names": ["space", "postcss", "list", "overflowPropertyRegExp", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "value", "overflowX", "overflowY", "invalidating<PERSON><PERSON><PERSON>", "length", "cloneBefore", "prop", "remove"], "mappings": ";;;;;;;;;;;;;;;;;;MAGgBA,QAAYC,QAApBC,KAAQF;;AAGhB,MAAMG,sBAAsB,GAAG,aAA/B;AAEA,YAAeF,OAAO,CAACG,MAAR,CAAe,4BAAf,EAA6CC,IAAI,IAAI;QAC7DC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,IAAvE;SAEOG,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeP,sBAAf,EAAuCQ,IAAI,IAAI;;qBAEQX,KAAK,CAACW,IAAI,CAACC,KAAN,CAFb;;YAEvCC,SAFuC;YAE5BC,SAF4B;YAEdC,kBAFc;;;UAK1CD,SAAS,IAAI,CAACC,kBAAkB,CAACC,MAArC,EAA6C;;QAE5CL,IAAI,CAACM,WAAL,CAAiB;UAChBC,IAAI,EAAG,GAAEP,IAAI,CAACO,IAAK,IADH;UAEhBN,KAAK,EAAEC;SAFR;QAKAF,IAAI,CAACM,WAAL,CAAiB;UAChBC,IAAI,EAAG,GAAEP,IAAI,CAACO,IAAK,IADH;UAEhBN,KAAK,EAAEE;SAFR,EAP4C;;YAaxC,CAACR,QAAL,EAAe;UACdK,IAAI,CAACQ,MAAL;;;KAnBH;GAFD;CAHc,CAAf;;;;"}