{"version": 3, "file": "index.cjs.js", "sources": ["lib/get-custom-properties.js", "lib/import-from.js", "lib/conversions.js", "lib/color.js", "lib/manage-unresolved.js", "lib/transform.js", "index.js"], "sourcesContent": ["import valueParser from 'postcss-values-parser';\n\n// return custom selectors from the css root, conditionally removing them\nexport default function getCustomProperties(root, opts) {\n\t// initialize custom selectors\n\tconst customPropertiesFromHtmlElement = {};\n\tconst customPropertiesFromRootPsuedo = {};\n\n\t// for each html or :root rule\n\troot.nodes.slice().forEach(rule => {\n\t\tconst customPropertiesObject = isHtmlRule(rule)\n\t\t\t? customPropertiesFromHtmlElement\n\t\t: isRootRule(rule)\n\t\t\t? customPropertiesFromRootPsuedo\n\t\t: null;\n\n\t\t// for each custom property\n\t\tif (customPropertiesObject) {\n\t\t\trule.nodes.slice().forEach(decl => {\n\t\t\t\tif (isCustomDecl(decl)) {\n\t\t\t\t\tconst { prop } = decl;\n\n\t\t\t\t\t// write the parsed value to the custom property\n\t\t\t\t\tcustomPropertiesObject[prop] = valueParser(decl.value).parse();\n\n\t\t\t\t\t// conditionally remove the custom property declaration\n\t\t\t\t\tif (!opts.preserve) {\n\t\t\t\t\t\tdecl.remove();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// conditionally remove the empty html or :root rule\n\t\t\tif (!opts.preserve && isEmptyParent(rule)) {\n\t\t\t\trule.remove();\n\t\t\t}\n\t\t}\n\t});\n\n\t// return all custom properties, preferring :root properties over html properties\n\treturn { ...customPropertiesFromHtmlElement, ...customPropertiesFromRootPsuedo };\n}\n\n// match html and :root rules\nconst htmlSelectorRegExp = /^html$/i;\nconst rootSelectorRegExp = /^:root$/i;\nconst customPropertyRegExp = /^--[A-z][\\w-]*$/;\n\n// whether the node is an html or :root rule\nconst isHtmlRule = node => node.type === 'rule' && htmlSelectorRegExp.test(node.selector) && Object(node.nodes).length;\nconst isRootRule = node => node.type === 'rule' && rootSelectorRegExp.test(node.selector) && Object(node.nodes).length;\n\n// whether the node is an custom property\nconst isCustomDecl = node => node.type === 'decl' && customPropertyRegExp.test(node.prop);\n\n// whether the node is a parent without children\nconst isEmptyParent = node => Object(node.nodes).length === 0;\n", "import fs from 'fs';\nimport path from 'path';\nimport postcss from 'postcss';\nimport getCustomProperties from './get-custom-properties';\nimport valueParser from 'postcss-values-parser';\n\n/* Import Custom Properties from CSS AST\n/* ========================================================================== */\n\nfunction importCustomPropertiesFromCSSAST(root) {\n\treturn getCustomProperties(root, { preserve: true });\n}\n\n/* Import Custom Properties from CSS File\n/* ========================================================================== */\n\nasync function importCustomPropertiesFromCSSFile(from) {\n\tconst css = await readFile(from);\n\tconst root = postcss.parse(css, { from });\n\n\treturn importCustomPropertiesFromCSSAST(root);\n}\n\n/* Import Custom Properties from Object\n/* ========================================================================== */\n\nfunction importCustomPropertiesFromObject(object) {\n\tconst customProperties = Object.assign(\n\t\t{},\n\t\tObject(object).customProperties || Object(object)['custom-properties']\n\t);\n\n\tfor (const prop in customProperties) {\n\t\tcustomProperties[prop] = valueParser(customProperties[prop]).parse();\n\t}\n\n\treturn customProperties;\n}\n\n/* Import Custom Properties from JSON file\n/* ========================================================================== */\n\nasync function importCustomPropertiesFromJSONFile(from) {\n\tconst object = await readJSON(from);\n\n\treturn importCustomPropertiesFromObject(object);\n}\n\n/* Import Custom Properties from JS file\n/* ========================================================================== */\n\nasync function importCustomPropertiesFromJSFile(from) {\n\tconst object = await import(from);\n\n\treturn importCustomPropertiesFromObject(object);\n}\n\n/* Import Custom Properties from Sources\n/* ========================================================================== */\n\nexport default function importCustomPropertiesFromSources(sources) {\n\treturn sources.map(source => {\n\t\tif (source instanceof Promise) {\n\t\t\treturn source;\n\t\t} else if (source instanceof Function) {\n\t\t\treturn source();\n\t\t}\n\n\t\t// read the source as an object\n\t\tconst opts = source === Object(source) ? source : { from: String(source) };\n\n\t\t// skip objects with Custom Properties\n\t\tif (opts.customProperties || opts['custom-properties']) {\n\t\t\treturn opts\n\t\t}\n\n\t\t// source pathname\n\t\tconst from = path.resolve(String(opts.from || ''));\n\n\t\t// type of file being read from\n\t\tconst type = (opts.type || path.extname(from).slice(1)).toLowerCase();\n\n\t\treturn { type, from };\n\t}).reduce(async (customProperties, source) => {\n\t\tconst { type, from } = await source;\n\n\t\tif (type === 'ast') {\n\t\t\treturn Object.assign(await customProperties, importCustomPropertiesFromCSSAST(from));\n\t\t}\n\n\t\tif (type === 'css') {\n\t\t\treturn Object.assign(await customProperties, await importCustomPropertiesFromCSSFile(from));\n\t\t}\n\n\t\tif (type === 'js') {\n\t\t\treturn Object.assign(await customProperties, await importCustomPropertiesFromJSFile(from));\n\t\t}\n\n\t\tif (type === 'json') {\n\t\t\treturn Object.assign(await customProperties, await importCustomPropertiesFromJSONFile(from));\n\t\t}\n\n\t\treturn Object.assign(await customProperties, await importCustomPropertiesFromObject(await source));\n\t}, {});\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst readFile = from => new Promise((resolve, reject) => {\n\tfs.readFile(from, 'utf8', (error, result) => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve(result);\n\t\t}\n\t});\n});\n\nconst readJSON = async from => JSON.parse(await readFile(from));\n", "/* Convert Degree to Hue Degree\n/* ========================================================================== */\n\nexport function convertDtoD(deg) {\n\treturn deg % 360;\n}\n\n/* Convert Gradian to Hue Degree\n/* ========================================================================== */\n\nexport function convertGtoD(grad) {\n\treturn grad * 0.9 % 360;\n}\n\n/* Convert Radian to Hue Degree\n/* ========================================================================== */\n\nexport function convertRtoD(rad) {\n\treturn rad * 180 / Math.PI % 360;\n}\n\n/* Convert Turn to Hue Degree\n/* ========================================================================== */\n\nexport function convertTtoD(turn) {\n\treturn turn * 360 % 360;\n}\n\n/* Convert a Name to Red/Green/Blue\n/* ========================================================================== */\n\nexport function convertNtoRGB(name) {\n\tconst names = {\n\t\taliceblue: [240, 248, 255],\n\t\tantiquewhite: [250, 235, 215],\n\t\taqua: [0, 255, 255],\n\t\taquamarine: [127, 255, 212],\n\t\tazure: [240, 255, 255],\n\t\tbeige: [245, 245, 220],\n\t\tbisque: [255, 228, 196],\n\t\tblack: [0, 0, 0],\n\t\tblanchedalmond: [255, 235, 205],\n\t\tblue: [0, 0, 255],\n\t\tblueviolet: [138, 43, 226],\n\t\tbrown: [165, 42, 42],\n\t\tburlywood: [222, 184, 135],\n\t\tcadetblue: [95, 158, 160],\n\t\tchartreuse: [127, 255, 0],\n\t\tchocolate: [210, 105, 30],\n\t\tcoral: [255, 127, 80],\n\t\tcornflowerblue: [100, 149, 237],\n\t\tcornsilk: [255, 248, 220],\n\t\tcrimson: [220, 20, 60],\n\t\tcyan: [0, 255, 255],\n\t\tdarkblue: [0, 0, 139],\n\t\tdarkcyan: [0, 139, 139],\n\t\tdarkgoldenrod: [184, 134, 11],\n\t\tdarkgray: [169, 169, 169],\n\t\tdarkgreen: [0, 100, 0],\n\t\tdarkgrey: [169, 169, 169],\n\t\tdarkkhaki: [189, 183, 107],\n\t\tdarkmagenta: [139, 0, 139],\n\t\tdarkolivegreen: [85, 107, 47],\n\t\tdarkorange: [255, 140, 0],\n\t\tdarkorchid: [153, 50, 204],\n\t\tdarkred: [139, 0, 0],\n\t\tdarksalmon: [233, 150, 122],\n\t\tdarkseagreen: [143, 188, 143],\n\t\tdarkslateblue: [72, 61, 139],\n\t\tdarkslategray: [47, 79, 79],\n\t\tdarkslategrey: [47, 79, 79],\n\t\tdarkturquoise: [0, 206, 209],\n\t\tdarkviolet: [148, 0, 211],\n\t\tdeeppink: [255, 20, 147],\n\t\tdeepskyblue: [0, 191, 255],\n\t\tdimgray: [105, 105, 105],\n\t\tdimgrey: [105, 105, 105],\n\t\tdodgerblue: [30, 144, 255],\n\t\tfirebrick: [178, 34, 34],\n\t\tfloralwhite: [255, 250, 240],\n\t\tforestgreen: [34, 139, 34],\n\t\tfuchsia: [255, 0, 255],\n\t\tgainsboro: [220, 220, 220],\n\t\tghostwhite: [248, 248, 255],\n\t\tgold: [255, 215, 0],\n\t\tgoldenrod: [218, 165, 32],\n\t\tgray: [128, 128, 128],\n\t\tgreen: [0, 128, 0],\n\t\tgreenyellow: [173, 255, 47],\n\t\tgrey: [128, 128, 128],\n\t\thoneydew: [240, 255, 240],\n\t\thotpink: [255, 105, 180],\n\t\tindianred: [205, 92, 92],\n\t\tindigo: [75, 0, 130],\n\t\tivory: [255, 255, 240],\n\t\tkhaki: [240, 230, 140],\n\t\tlavender: [230, 230, 250],\n\t\tlavenderblush: [255, 240, 245],\n\t\tlawngreen: [124, 252, 0],\n\t\tlemonchiffon: [255, 250, 205],\n\t\tlightblue: [173, 216, 230],\n\t\tlightcoral: [240, 128, 128],\n\t\tlightcyan: [224, 255, 255],\n\t\tlightgoldenrodyellow: [250, 250, 210],\n\t\tlightgray: [211, 211, 211],\n\t\tlightgreen: [144, 238, 144],\n\t\tlightgrey: [211, 211, 211],\n\t\tlightpink: [255, 182, 193],\n\t\tlightsalmon: [255, 160, 122],\n\t\tlightseagreen: [32, 178, 170],\n\t\tlightskyblue: [135, 206, 250],\n\t\tlightslategray: [119, 136, 153],\n\t\tlightslategrey: [119, 136, 153],\n\t\tlightsteelblue: [176, 196, 222],\n\t\tlightyellow: [255, 255, 224],\n\t\tlime: [0, 255, 0],\n\t\tlimegreen: [50, 205, 50],\n\t\tlinen: [250, 240, 230],\n\t\tmagenta: [255, 0, 255],\n\t\tmaroon: [128, 0, 0],\n\t\tmediumaquamarine: [102, 205, 170],\n\t\tmediumblue: [0, 0, 205],\n\t\tmediumorchid: [186, 85, 211],\n\t\tmediumpurple: [147, 112, 219],\n\t\tmediumseagreen: [60, 179, 113],\n\t\tmediumslateblue: [123, 104, 238],\n\t\tmediumspringgreen: [0, 250, 154],\n\t\tmediumturquoise: [72, 209, 204],\n\t\tmediumvioletred: [199, 21, 133],\n\t\tmidnightblue: [25, 25, 112],\n\t\tmintcream: [245, 255, 250],\n\t\tmistyrose: [255, 228, 225],\n\t\tmoccasin: [255, 228, 181],\n\t\tnavajowhite: [255, 222, 173],\n\t\tnavy: [0, 0, 128],\n\t\toldlace: [253, 245, 230],\n\t\tolive: [128, 128, 0],\n\t\tolivedrab: [107, 142, 35],\n\t\torange: [255, 165, 0],\n\t\torangered: [255, 69, 0],\n\t\torchid: [218, 112, 214],\n\t\tpalegoldenrod: [238, 232, 170],\n\t\tpalegreen: [152, 251, 152],\n\t\tpaleturquoise: [175, 238, 238],\n\t\tpalevioletred: [219, 112, 147],\n\t\tpapayawhip: [255, 239, 213],\n\t\tpeachpuff: [255, 218, 185],\n\t\tperu: [205, 133, 63],\n\t\tpink: [255, 192, 203],\n\t\tplum: [221, 160, 221],\n\t\tpowderblue: [176, 224, 230],\n\t\tpurple: [128, 0, 128],\n\t\trebeccapurple: [102, 51, 153],\n\t\tred: [255, 0, 0],\n\t\trosybrown: [188, 143, 143],\n\t\troyalblue: [65, 105, 225],\n\t\tsaddlebrown: [139, 69, 19],\n\t\tsalmon: [250, 128, 114],\n\t\tsandybrown: [244, 164, 96],\n\t\tseagreen: [46, 139, 87],\n\t\tseashell: [255, 245, 238],\n\t\tsienna: [160, 82, 45],\n\t\tsilver: [192, 192, 192],\n\t\tskyblue: [135, 206, 235],\n\t\tslateblue: [106, 90, 205],\n\t\tslategray: [112, 128, 144],\n\t\tslategrey: [112, 128, 144],\n\t\tsnow: [255, 250, 250],\n\t\tspringgreen: [0, 255, 127],\n\t\tsteelblue: [70, 130, 180],\n\t\ttan: [210, 180, 140],\n\t\tteal: [0, 128, 128],\n\t\tthistle: [216, 191, 216],\n\t\ttomato: [255, 99, 71],\n\t\ttransparent: [0, 0, 0],\n\t\tturquoise: [64, 224, 208],\n\t\tviolet: [238, 130, 238],\n\t\twheat: [245, 222, 179],\n\t\twhite: [255, 255, 255],\n\t\twhitesmoke: [245, 245, 245],\n\t\tyellow: [255, 255, 0],\n\t\tyellowgreen: [154, 205, 50]\n\t};\n\n\treturn names[name] && names[name].map(c => c / 2.55);\n}\n\n\n/* Convert a Hex to Red/Green/Blue\n/* ========================================================================== */\n\nexport function convertHtoRGB(hex) {\n\t// #<hex-color>{3,4,6,8}\n\tconst [r, g, b, a, rr, gg, bb, aa] = (hex.match(hexColorMatch) || []).slice(1);\n\n\tif (rr !== undefined || r !== undefined) {\n\t\tconst red   = rr !== undefined ? parseInt(rr, 16) : r !== undefined ? parseInt(r + r, 16) : 0;\n\t\tconst green = gg !== undefined ? parseInt(gg, 16) : g !== undefined ? parseInt(g + g, 16) : 0;\n\t\tconst blue  = bb !== undefined ? parseInt(bb, 16) : b !== undefined ? parseInt(b + b, 16) : 0;\n\t\tconst alpha = aa !== undefined ? parseInt(aa, 16) : a !== undefined ? parseInt(a + a, 16) : 255;\n\n\t\treturn [red, green, blue, alpha].map(c => c / 2.55);\n\t}\n\n\treturn undefined;\n}\n\nconst hexColorMatch = /^#(?:([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?|([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?)$/i;\n", "import { rgb2hsl, rgb2hwb, hsl2rgb, hsl2hwb, hwb2rgb, hwb2hsl, rgb2hue } from '@csstools/convert-colors';\n\nexport default class Color {\n\tconstructor(color) {\n\t\tthis.color = Object(Object(color).color || color);\n\n\t\tthis.color.colorspace = this.color.colorspace\n\t\t\t? this.color.colorspace\n\t\t: 'red' in color && 'green' in color && 'blue' in color\n\t\t\t? 'rgb'\n\t\t: 'hue' in color && 'saturation' in color && 'lightness' in color\n\t\t\t? 'hsl'\n\t\t: 'hue' in color && 'whiteness' in color && 'blackness' in color\n\t\t\t? 'hwb'\n\t\t: 'unknown';\n\n\t\tif (color.colorspace === 'rgb') {\n\t\t\tthis.color.hue = rgb2hue(color.red, color.green, color.blue, color.hue || 0);\n\t\t}\n\t}\n\n\talpha(alpha) {\n\t\tconst color = this.color;\n\n\t\treturn alpha === undefined\n\t\t\t? color.alpha\n\t\t: new Color(assign(color, { alpha }));\n\t}\n\n\tblackness(blackness) {\n\t\tconst hwb = color2hwb(this.color);\n\n\t\treturn blackness === undefined\n\t\t\t? hwb.blackness\n\t\t: new Color(assign(hwb, { blackness }));\n\t}\n\n\tblend(color, percentage, colorspace = 'rgb') {\n\t\tconst base = this.color;\n\n\t\treturn new Color(blend(base, color, percentage, colorspace));\n\t}\n\n\tblenda(color, percentage, colorspace = 'rgb') {\n\t\tconst base = this.color;\n\n\t\treturn new Color(blend(base, color, percentage, colorspace, true));\n\t}\n\n\tblue(blue) {\n\t\tconst rgb = color2rgb(this.color);\n\n\t\treturn blue === undefined\n\t\t\t? rgb.blue\n\t\t: new Color(assign(rgb, { blue }));\n\t}\n\n\tcontrast(percentage) {\n\t\tconst base = this.color;\n\n\t\treturn new Color(contrast(base, percentage));\n\t}\n\n\tgreen(green) {\n\t\tconst rgb = color2rgb(this.color);\n\n\t\treturn green === undefined\n\t\t\t? rgb.green\n\t\t: new Color(assign(rgb, { green }));\n\t}\n\n\thue(hue) {\n\t\tconst hsl = color2hsl(this.color);\n\n\t\treturn hue === undefined\n\t\t\t? hsl.hue\n\t\t: new Color(assign(hsl, { hue }));\n\t}\n\n\tlightness(lightness) {\n\t\tconst hsl = color2hsl(this.color);\n\n\t\treturn lightness === undefined\n\t\t\t? hsl.lightness\n\t\t: new Color(assign(hsl, { lightness }))\n\t}\n\n\tred(red) {\n\t\tconst rgb = color2rgb(this.color);\n\n\t\treturn red === undefined\n\t\t\t? rgb.red\n\t\t: new Color(assign(rgb, { red }));\n\t}\n\n\trgb(red, green, blue) {\n\t\tconst rgb = color2rgb(this.color);\n\n\t\treturn new Color(assign(rgb, { red, green, blue }));\n\t}\n\n\tsaturation(saturation) {\n\t\tconst hsl = color2hsl(this.color);\n\n\t\treturn saturation === undefined\n\t\t\t? hsl.saturation\n\t\t: new Color(assign(hsl, { saturation }));\n\t}\n\n\tshade(percentage) {\n\t\tconst hwb = color2hwb(this.color);\n\t\tconst shade = { hue: 0, whiteness: 0, blackness: 100, colorspace: 'hwb' };\n\t\tconst colorspace = 'rgb';\n\n\t\treturn percentage === undefined\n\t\t\t? hwb.blackness\n\t\t: new Color(blend(hwb, shade, percentage, colorspace));\n\t}\n\n\ttint(percentage) {\n\t\tconst hwb = color2hwb(this.color);\n\t\tconst tint = { hue: 0, whiteness: 100, blackness: 0, colorspace: 'hwb' };\n\t\tconst colorspace = 'rgb';\n\n\t\treturn percentage === undefined\n\t\t\t? hwb.blackness\n\t\t: new Color(blend(hwb, tint, percentage, colorspace));\n\t}\n\n\twhiteness(whiteness) {\n\t\tconst hwb = color2hwb(this.color);\n\n\t\treturn whiteness === undefined\n\t\t\t? hwb.whiteness\n\t\t: new Color(assign(hwb, { whiteness }));\n\t}\n\n\ttoHSL() {\n\t\treturn color2hslString(this.color);\n\t}\n\n\ttoHWB() {\n\t\treturn color2hwbString(this.color);\n\t}\n\n\ttoLegacy() {\n\t\treturn color2legacyString(this.color);\n\t}\n\n\ttoRGB() {\n\t\treturn color2rgbString(this.color);\n\t}\n\n\ttoRGBLegacy() {\n\t\treturn color2rgbLegacyString(this.color);\n\t}\n\n\ttoString() {\n\t\treturn color2string(this.color);\n\t}\n}\n\n/* Blending\n/* ========================================================================== */\n\nfunction blend(base, color, percentage, colorspace, isBlendingAlpha) {\n\tconst addition    = percentage / 100;\n\tconst subtraction = 1 - addition;\n\n\tif (colorspace === 'hsl') {\n\t\tconst { hue: h1, saturation: s1, lightness: l1, alpha: a1 } = color2hsl(base);\n\t\tconst { hue: h2, saturation: s2, lightness: l2, alpha: a2 } = color2hsl(color);\n\n\t\tconst [hue, saturation, lightness, alpha] = [\n\t\t\th1 * subtraction + h2 * addition,\n\t\t\ts1 * subtraction + s2 * addition,\n\t\t\tl1 * subtraction + l2 * addition,\n\t\t\tisBlendingAlpha\n\t\t\t\t? a1 * subtraction + a2 * addition\n\t\t\t: a1\n\t\t];\n\n\t\treturn { hue, saturation, lightness, alpha, colorspace: 'hsl' };\n\t} else if (colorspace === 'hwb') {\n\t\tconst { hue: h1, whiteness: w1, blackness: b1, alpha: a1 } = color2hwb(base);\n\t\tconst { hue: h2, whiteness: w2, blackness: b2, alpha: a2 } = color2hwb(color);\n\n\t\tconst [hue, whiteness, blackness, alpha] = [\n\t\t\th1 * subtraction + h2 * addition,\n\t\t\tw1 * subtraction + w2 * addition,\n\t\t\tb1 * subtraction + b2 * addition,\n\t\t\tisBlendingAlpha\n\t\t\t\t? a1 * subtraction + a2 * addition\n\t\t\t: a1\n\t\t];\n\n\t\treturn { hue, whiteness, blackness, alpha, colorspace: 'hwb' };\n\t} else {\n\t\tconst { red: r1, green: g1, blue: b1, alpha: a1 } = color2rgb(base);\n\t\tconst { red: r2, green: g2, blue: b2, alpha: a2 } = color2rgb(color);\n\n\t\tconst [red, green, blue, alpha] = [\n\t\t\tr1 * subtraction + r2 * addition,\n\t\t\tg1 * subtraction + g2 * addition,\n\t\t\tb1 * subtraction + b2 * addition,\n\t\t\tisBlendingAlpha\n\t\t\t\t? a1 * subtraction + a2 * addition\n\t\t\t: a1\n\t\t];\n\n\t\treturn { red, green, blue, alpha, colorspace: 'rgb' };\n\t}\n}\n\n/* Assign channels to a new instance of a base color\n/* ========================================================================== */\n\nfunction assign(base, channels) {\n\tconst color = Object.assign({}, base);\n\n\tObject.keys(channels).forEach(\n\t\tchannel => {\n\t\t\t// detect channel\n\t\t\tconst isHue = channel === 'hue';\n\t\t\tconst isRGB = !isHue && blueGreenRedMatch.test(channel);\n\n\t\t\t// normalized value of the channel\n\t\t\tconst value = normalize(channels[channel], channel);\n\n\t\t\t// assign channel to new object\n\t\t\tcolor[channel] = value;\n\n\t\t\tif (isRGB) {\n\t\t\t\t// conditionally preserve the hue\n\t\t\t\tcolor.hue = rgb2hue(color.red, color.green, color.blue, base.hue || 0);\n\t\t\t}\n\t\t}\n\t);\n\n\treturn color;\n}\n\nfunction normalize(value, channel) {\n\t// detect channel\n\tconst isHue = channel === 'hue';\n\n\t// value limitations\n\tconst min = 0;\n\tconst max = isHue ? 360 : 100;\n\n\tconst normalizedValue = Math.min(Math.max(isHue\n\t\t? value % 360\n\t: value, min), max);\n\n\treturn normalizedValue;\n}\n\n/* Convert colors\n/* ========================================================================== */\n\nfunction color2rgb(color) {\n\tconst [ red, green, blue ] = color.colorspace === 'hsl'\n\t\t? hsl2rgb(color.hue, color.saturation, color.lightness)\n\t: color.colorspace === 'hwb'\n\t\t? hwb2rgb(color.hue, color.whiteness, color.blackness)\n\t: [ color.red, color.green, color.blue ];\n\n\treturn { red, green, blue, hue: color.hue, alpha: color.alpha, colorspace: 'rgb' };\n}\n\nfunction color2hsl(color) {\n\tconst [ hue, saturation, lightness ] = color.colorspace === 'rgb'\n\t\t? rgb2hsl(color.red, color.green, color.blue, color.hue)\n\t: color.colorspace === 'hwb'\n\t\t? hwb2hsl(color.hue, color.whiteness, color.blackness)\n\t: [ color.hue, color.saturation, color.lightness ];\n\n\treturn { hue, saturation, lightness, alpha: color.alpha, colorspace: 'hsl' };\n}\n\nfunction color2hwb(color) {\n\tconst [ hue, whiteness, blackness ] = color.colorspace === 'rgb'\n\t\t? rgb2hwb(color.red, color.green, color.blue, color.hue)\n\t: color.colorspace === 'hsl'\n\t\t? hsl2hwb(color.hue, color.saturation, color.lightness)\n\t: [ color.hue, color.whiteness, color.blackness ];\n\n\treturn { hue, whiteness, blackness, alpha: color.alpha, colorspace: 'hwb' };\n}\n\n/* Contrast functions\n/* ========================================================================== */\n\nfunction contrast(color, percentage) {\n\t// https://drafts.csswg.org/css-color/#contrast-adjuster\n\tconst hwb = color2hwb(color);\n\tconst rgb = color2rgb(color);\n\n\t// compute the luminance of the color.\n\tconst luminance = rgb2luminance(rgb.red, rgb.green, rgb.blue);\n\n\t// the maximum-contrast color, if it is less than .5\n\tconst maxContrastColor = luminance < 0.5\n\t\t// hwb(X, 100%, 0%), where X is the hue angle of the color\n\t\t? { hue: hwb.hue, whiteness: 100, blackness: 0, alpha: hwb.alpha, colorspace: 'hwb' }\n\t// otherwise, hwb(X, 0%, 100%), where X is the hue angle of the color\n\t: { hue: hwb.hue, whiteness: 0, blackness: 100, alpha: hwb.alpha, colorspace: 'hwb' };\n\n\t// contrast ratio\n\tconst contrastRatio = colors2contrast(color, maxContrastColor);\n\n\tconst minContrastColor = contrastRatio > 4.5\n\t\t// the color with the smallest contrast ratio with the base color that is greater than 4.5\n\t\t? colors2contrastRatioColor(hwb, maxContrastColor)\n\t// otherwise, the maximum-contrast color\n\t: maxContrastColor;\n\n\t// color(maximum-contrast blend(minimum-contrast <percentage> hwb)));\n\treturn blend(maxContrastColor, minContrastColor, percentage, 'hwb', false);\n}\n\nfunction colors2contrast(color1, color2) {\n\t// https://drafts.csswg.org/css-color/#contrast-ratio\n\tconst rgb1 = color2rgb(color1);\n\tconst rgb2 = color2rgb(color2);\n\tconst l1 = rgb2luminance(rgb1.red, rgb1.green, rgb1.blue);\n\tconst l2 = rgb2luminance(rgb2.red, rgb2.green, rgb2.blue);\n\n\treturn l1 > l2\n\t\t// if l1 is the relative luminance of the lighter of the colors\n\t\t? (l1 + 0.05) / (l2 + 0.05)\n\t// otherwise, if l2 is the relative luminance of the lighter of the colors\n\t: (l2 + 0.05) / (l1 + 0.05);\n}\n\nfunction rgb2luminance(red, green, blue) {\n\tconst [ redLuminance, greenLuminance, blueLuminance ] = [\n\t\tchannel2luminance(red),\n\t\tchannel2luminance(green),\n\t\tchannel2luminance(blue)\n\t];\n\n\t// https://drafts.csswg.org/css-color/#luminance\n\tconst luminance = 0.2126 * redLuminance + 0.7152 * greenLuminance + 0.0722 * blueLuminance;\n\n\treturn luminance;\n}\n\nfunction channel2luminance(value) {\n\t// https://drafts.csswg.org/css-color/#luminance\n\tconst luminance = value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) /1.055, 2.4);\n\n\treturn luminance;\n}\n\n// return the smallest contrast ratio from a color and a maximum contrast (credit: @thetalecrafter)\nfunction colors2contrastRatioColor(hwb, maxHWB) {\n\tconst modifiedHWB = Object.assign({}, hwb);\n\n\t// values to be used for linear interpolations in HWB space\n\tlet minW = hwb.whiteness;\n\tlet minB = hwb.blackness;\n\tlet maxW = maxHWB.whiteness;\n\tlet maxB = maxHWB.blackness;\n\n\t// find the color with the smallest contrast ratio with the base color that is greater than 4.5\n\twhile (Math.abs(minW - maxW) > 100 || Math.abs(minB - maxB) > 100) {\n\t\tconst midW = Math.round((maxW + minW) / 2);\n\t\tconst midB = Math.round((maxB + minB) / 2);\n\n\t\tmodifiedHWB.whiteness = midW;\n\t\tmodifiedHWB.blackness = midB;\n\n\t\tif (colors2contrast(modifiedHWB, hwb) > 4.5) {\n\t\t\tmaxW = midW;\n\t\t\tmaxB = midB;\n\t\t} else {\n\t\t\tminW = midW;\n\t\t\tminB = midB;\n\t\t}\n\t}\n\n\treturn modifiedHWB;\n}\n\n/* Match\n/* ========================================================================== */\n\nconst blueGreenRedMatch = /^(blue|green|red)$/i;\n\n/* Stringifiers\n/* ========================================================================== */\n\nfunction color2string(color) {\n\treturn color.colorspace === 'hsl'\n\t\t? color2hslString(color)\n\t: color.colorspace === 'hwb'\n\t\t? color2hwbString(color)\n\t: color2rgbString(color);\n}\n\nfunction color2hslString(color) {\n\tconst hsl        = color2hsl(color);\n\tconst isOpaque   = hsl.alpha === 100;\n\tconst hue        = hsl.hue;\n\tconst saturation = Math.round(hsl.saturation * 10000000000) / 10000000000;\n\tconst lightness  = Math.round(hsl.lightness * 10000000000) / 10000000000;\n\tconst alpha      = Math.round(hsl.alpha * 10000000000) / 10000000000;\n\n\treturn `hsl(${hue} ${saturation}% ${lightness}%${isOpaque\n\t\t? ''\n\t: ` / ${alpha}%`})`;\n}\n\nfunction color2hwbString(color) {\n\tconst hwb       = color2hwb(color);\n\tconst isOpaque  = hwb.alpha === 100;\n\tconst hue       = hwb.hue;\n\tconst whiteness = Math.round(hwb.whiteness * 10000000000) / 10000000000;\n\tconst blackness = Math.round(hwb.blackness * 10000000000) / 10000000000;\n\tconst alpha     = Math.round(hwb.alpha * 10000000000) / 10000000000;\n\n\treturn `hwb(${hue} ${whiteness}% ${blackness}%${isOpaque\n\t\t? ''\n\t: ` / ${alpha}%`})`;\n}\n\nfunction color2rgbString(color) {\n\tconst rgb      = color2rgb(color);\n\tconst isOpaque = rgb.alpha === 100;\n\tconst red      = Math.round(rgb.red * 10000000000) / 10000000000;\n\tconst green    = Math.round(rgb.green * 10000000000) / 10000000000;\n\tconst blue     = Math.round(rgb.blue * 10000000000) / 10000000000;\n\tconst alpha    = Math.round(rgb.alpha * 10000000000) / 10000000000;\n\n\treturn `rgb(${red}% ${green}% ${blue}%${isOpaque\n\t\t? ''\n\t: ` / ${alpha}%`})`;\n}\n\nfunction color2legacyString(color) {\n\treturn color.colorspace === 'hsl'\n\t\t? color2hslLegacyString(color)\n\t: color2rgbLegacyString(color);\n}\n\nfunction color2rgbLegacyString(color) {\n\tconst rgb      = color2rgb(color);\n\tconst isOpaque = rgb.alpha === 100;\n\tconst name     = isOpaque ? 'rgb' : 'rgba';\n\tconst red      = Math.round(rgb.red * 255 / 100);\n\tconst green    = Math.round(rgb.green * 255 / 100);\n\tconst blue     = Math.round(rgb.blue * 255 / 100);\n\tconst alpha    = Math.round(rgb.alpha / 100 * 10000000000) / 10000000000;\n\n\treturn `${name}(${red}, ${green}, ${blue}${isOpaque\n\t\t? ''\n\t: `, ${alpha}`})`;\n}\n\nfunction color2hslLegacyString(color) {\n\tconst hsl        = color2hsl(color);\n\tconst isOpaque   = hsl.alpha === 100;\n\tconst name       = isOpaque ? 'hsl' : 'hsla';\n\tconst hue        = hsl.hue;\n\tconst saturation = Math.round(hsl.saturation * 10000000000) / 10000000000;\n\tconst lightness  = Math.round(hsl.lightness * 10000000000) / 10000000000;\n\tconst alpha      = Math.round(hsl.alpha / 100 * 10000000000) / 10000000000;\n\n\treturn `${name}(${hue}, ${saturation}%, ${lightness}%${isOpaque\n\t\t? ''\n\t: `, ${alpha}`})`;\n}\n", "export default function manageUnresolved(node, opts, word, message) {\n\tif ('warn' === opts.unresolved) {\n\t\topts.decl.warn(opts.result, message, { word });\n\t} else if ('ignore' !== opts.unresolved) {\n\t\tthrow opts.decl.error(message, { word });\n\t}\n}\n", "// tooling\nimport { convertDtoD, convertGtoD, convertRtoD, convertTtoD, convertNtoRGB, convertHtoRGB } from './conversions';\nimport Color from './color';\nimport manageUnresolved from './manage-unresolved';\nimport parser from 'postcss-values-parser';\n\n/* Transform AST\n/* ========================================================================== */\n\nexport default function transformAST(node, opts) {\n\tnode.nodes.slice(0).forEach(child => {\n\t\tif (isColorModFunction(child)) {\n\t\t\t// transform any variables within the color-mod() function\n\t\t\tif (opts.transformVars) {\n\t\t\t\ttransformVariables(child, opts);\n\t\t\t}\n\n\t\t\t// transform any color-mod() functions\n\t\t\tconst color = transformColorModFunction(child, opts);\n\n\t\t\tif (color) {\n\t\t\t\t// update the color-mod() function with the transformed value\n\t\t\t\tchild.replaceWith(parser.word({\n\t\t\t\t\traws: child.raws,\n\t\t\t\t\tvalue: opts.stringifier(color)\n\t\t\t\t}));\n\t\t\t}\n\t\t} else if (child.nodes && Object(child.nodes).length) {\n\t\t\ttransformAST(child, opts);\n\t\t}\n\t});\n}\n\n/* Transform <var> functions\n/* ========================================================================== */\n\nfunction transformVariables(node, opts) {\n\twalk(node, child => {\n\t\tif (isVariable(child)) {\n\t\t\t// get the custom property and fallback value from var()\n\t\t\tconst [prop, fallbackNode] = transformArgsByParams(child, [\n\t\t\t\t// <value> , [ <fallback> ]?\n\t\t\t\t[transformWord, isComma, transformNode]\n\t\t\t]);\n\n\t\t\t// if the custom property is known\n\t\t\tif (prop in opts.customProperties) {\n\t\t\t\tlet customPropertyValue = opts.customProperties[prop];\n\n\t\t\t\t// follow custom properties referencing custom properties\n\t\t\t\tif (looseVarMatch.test(customPropertyValue)) {\n\t\t\t\t\tconst rootChildAST = customPropertyValue.clone();\n\n\t\t\t\t\ttransformVariables(rootChildAST, opts);\n\n\t\t\t\t\tcustomPropertyValue = rootChildAST;\n\t\t\t\t}\n\n\t\t\t\t// replace var() with the custom property value\n\t\t\t\tif (customPropertyValue.nodes.length === 1 && customPropertyValue.nodes[0].nodes.length) {\n\t\t\t\t\tcustomPropertyValue.nodes[0].nodes.forEach(customPropertyChild => {\n\t\t\t\t\t\tchild.parent.insertBefore(child, customPropertyChild);\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tchild.remove();\n\t\t\t} else if (fallbackNode && fallbackNode.nodes.length === 1 && fallbackNode.nodes[0].nodes.length) {\n\t\t\t\t// otherwise, replace var() with the fallback value\n\t\t\t\ttransformVariables(fallbackNode, opts);\n\n\t\t\t\tchild.replaceWith(...fallbackNode.nodes[0].nodes[0]);\n\t\t\t}\n\t\t}\n\t});\n}\n\n/* Transform <color> functions\n/* ========================================================================== */\n\nfunction transformColor(node, opts) {\n\tif (isRGBFunction(node)) {\n\t\treturn transformRGBFunction(node, opts);\n\t} else if (isHSLFunction(node)) {\n\t\treturn transformHSLFunction(node, opts);\n\t} else if (isHWBFunction(node)) {\n\t\treturn transformHWBFunction(node, opts);\n\t} else if (isColorModFunction(node)) {\n\t\treturn transformColorModFunction(node, opts);\n\t} else if (isHexColor(node)) {\n\t\treturn transformHexColor(node, opts);\n\t} else if (isNamedColor(node)) {\n\t\treturn transformNamedColor(node, opts);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a color`);\n\t}\n}\n\n// return a transformed rgb/rgba color function\nfunction transformRGBFunction(node, opts) {\n\tconst [red, green, blue, alpha = 100] = transformArgsByParams(node, [\n\t\t// <percentage> <percentage> <percentage> [ , <alpha-value> ]?\n\t\t[transformPercentage, transformPercentage, transformPercentage, isSlash, transformAlpha],\n\t\t// <number> <number> <number> [ , <alpha-value> ]?\n\t\t[transformRGBNumber, transformRGBNumber, transformRGBNumber, isSlash, transformAlpha],\n\t\t// <percentage> , <percentage> , <percentage> [ , <alpha-value> ]?\n\t\t[transformPercentage, isComma, transformPercentage, isComma, transformPercentage, isComma, transformAlpha],\n\t\t// <number> , <number> , <number> [ , <alpha-value> ]?\n\t\t[transformRGBNumber, isComma, transformRGBNumber, isComma, transformRGBNumber, isComma, transformAlpha]\n\t]);\n\n\tif (red !== undefined) {\n\t\tconst color = new Color({ red, green, blue, alpha, colorspace: 'rgb' });\n\n\t\treturn color;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid rgb() function`);\n\t}\n}\n\n// return a transformed hsl/hsla color function\nfunction transformHSLFunction(node, opts) {\n\tconst [hue, saturation, lightness, alpha = 100] = transformArgsByParams(node, [\n\t\t// <hue> <percentage> <percentage> [ / <alpha-value> ]?\n\t\t[transformHue, transformPercentage, transformPercentage, isSlash, transformAlpha],\n\t\t// <hue> , <percentage> , <percentage> [ , <alpha-value> ]?\n\t\t[transformHue, isComma, transformPercentage, isComma, transformPercentage, isComma, transformAlpha]\n\t]);\n\n\tif (lightness !== undefined) {\n\t\tconst color = new Color({ hue, saturation, lightness, alpha, colorspace: 'hsl' });\n\n\t\treturn color;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hsl() function`);\n\t}\n}\n\n// return a transformed hwb color function\nfunction transformHWBFunction(node, opts) {\n\tconst [hue, whiteness, blackness, alpha = 100] = transformArgsByParams(node, [\n\t\t// <hue> <percentage> <percentage> [ / <alpha-value> ]?\n\t\t[transformHue, transformPercentage, transformPercentage, isSlash, transformAlpha]\n\t]);\n\n\tif (blackness !== undefined) {\n\t\tconst color = new Color({ hue, whiteness, blackness, alpha, colorspace: 'hwb' });\n\n\t\treturn color;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hwb() function`);\n\t}\n}\n\n// return a transformed color-mod color function\nfunction transformColorModFunction(node, opts) {\n\t// [ <color> | <hue> ] <color-adjuster>*\n\tconst [colorOrHueNode, ...adjusterNodes] = (node.nodes || []).slice(1, -1) || [];\n\n\tif (colorOrHueNode !== undefined) {\n\t\tconst color = isHue(colorOrHueNode)\n\t\t\t? new Color({\n\t\t\t\thue: transformHue(colorOrHueNode, opts),\n\t\t\t\tsaturation: 100,\n\t\t\t\tlightness: 50,\n\t\t\t\talpha: 100,\n\t\t\t\tcolorspace: 'hsl'\n\t\t\t})\n\t\t: transformColor(colorOrHueNode, opts);\n\n\t\tif (color) {\n\t\t\tconst adjustedColor = transformColorByAdjusters(color, adjusterNodes, opts);\n\n\t\t\treturn adjustedColor;\n\t\t} else {\n\t\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid color`);\n\t\t}\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid color-mod() function`);\n\t}\n}\n\n// return a transformed hex color\nfunction transformHexColor(node, opts) {\n\tif (hexColorMatch.test(node.value)) {\n\t\t// #<hex-color>{3,4,6,8}\n\t\tconst [red, green, blue, alpha] = convertHtoRGB(node.value);\n\n\t\tconst color = new Color({ red, green, blue, alpha });\n\n\t\treturn color;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hex color`);\n\t}\n}\n\n// return a transformed named-color\nfunction transformNamedColor(node, opts) {\n\tif (isNamedColor(node)) {\n\t\t// <named-color>\n\t\tconst [red, green, blue] = convertNtoRGB(node.value);\n\n\t\tconst color = new Color({ red, green, blue, alpha: 100, colorspace: 'rgb' });\n\n\t\treturn color;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid named-color`);\n\t}\n}\n\n/* Transform <color-adjuster> functions\n/* ========================================================================== */\n\n// return a transformed color using adjustments\nfunction transformColorByAdjusters(color, adjusterNodes, opts) {\n\tconst adjustedColor = adjusterNodes.reduce((base, node) => {\n\t\tif (isAlphaBlueGreenRedAdjuster(node)) {\n\t\t\treturn transformAlphaBlueGreenRedAdjuster(base, node, opts);\n\t\t} else if (isRGBAdjuster(node)) {\n\t\t\treturn transformRGBAdjuster(base, node, opts);\n\t\t} else if (isHueAdjuster(node)) {\n\t\t\treturn transformHueAdjuster(base, node, opts);\n\t\t} else if (isBlacknessLightnessSaturationWhitenessAdjuster(node)) {\n\t\t\treturn transformBlacknessLightnessSaturationWhitenessAdjuster(base, node, opts);\n\t\t} else if (isShadeTintAdjuster(node)) {\n\t\t\treturn transformShadeTintAdjuster(base, node, opts);\n\t\t} else if (isBlendAdjuster(node)) {\n\t\t\treturn transformBlendAdjuster(base, node, node.value === 'blenda', opts);\n\t\t} else if (isContrastAdjuster(node)) {\n\t\t\treturn transformContrastAdjuster(base, node, opts);\n\t\t} else {\n\t\t\tmanageUnresolved(node, opts, node.value, `Expected a valid color adjuster`);\n\n\t\t\treturn base;\n\t\t}\n\t}, color);\n\n\treturn adjustedColor;\n}\n\n// return a transformed color using a/alpha/blue/green/red adjustments\nfunction transformAlphaBlueGreenRedAdjuster(base, node, opts) {\n\tconst [operatorOrValue, adjustment] = transformArgsByParams(node, alphaMatch.test(node.value)\n\t\t\t// a/alpha adjustments\n\t\t\t? [\n\t\t\t\t// [ + | - ] <alpha-value>\n\t\t\t\t[transformMinusPlusOperator, transformAlpha],\n\t\t\t\t// * <percentage>\n\t\t\t\t[transformTimesOperator, transformPercentage],\n\t\t\t\t// <alpha-value>\n\t\t\t\t[transformAlpha]\n\t\t\t]\n\t\t// blue/green/red adjustments\n\t\t: [\n\t\t\t// [ + | - ] <percentage>\n\t\t\t[transformMinusPlusOperator, transformPercentage],\n\t\t\t// [ + | - ] <number>\n\t\t\t[transformMinusPlusOperator, transformRGBNumber],\n\t\t\t// * <percentage>\n\t\t\t[transformTimesOperator, transformPercentage],\n\t\t\t// <percentage>\n\t\t\t[transformPercentage],\n\t\t\t// <number>\n\t\t\t[transformRGBNumber]\n\t\t]\n\t);\n\n\tif (operatorOrValue !== undefined) {\n\t\t// normalized channel name\n\t\tconst channel = node.value.toLowerCase().replace(alphaMatch, 'alpha');\n\n\t\tconst existingValue = base[channel]();\n\n\t\tconst modifiedValue = adjustment !== undefined\n\t\t\t? operatorOrValue === '+'\n\t\t\t\t? existingValue + Number(adjustment)\n\t\t\t: operatorOrValue === '-'\n\t\t\t\t? existingValue - Number(adjustment)\n\t\t\t: operatorOrValue === '*'\n\t\t\t\t? existingValue * Number(adjustment)\n\t\t\t: Number(adjustment)\n\t\t: Number(operatorOrValue);\n\n\t\tconst modifiedColor = base[channel](modifiedValue);\n\n\t\treturn modifiedColor;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid modifier()`);\n\t}\n}\n\n// return a transformed color using an rgb adjustment\nfunction transformRGBAdjuster(base, node, opts) {\n\tconst [arg1, arg2, arg3, arg4] = transformArgsByParams(node, [\n\t\t\t// [ + | - ] <percentage> <percentage> <percentage>\n\t\t\t[transformMinusPlusOperator, transformPercentage, transformPercentage, transformPercentage],\n\t\t\t// [ + | - ] <number> <number> <number>\n\t\t\t[transformMinusPlusOperator, transformRGBNumber, transformRGBNumber, transformRGBNumber],\n\t\t\t// [ + | - ] <hash-token>\n\t\t\t[transformMinusPlusOperator, transformHexColor],\n\t\t\t// [ * ] <percentage>\n\t\t\t[transformTimesOperator, transformPercentage]\n\t\t]\n\t);\n\n\tif (arg2 !== undefined && arg2.color) {\n\t\tconst modifiedColor = base.rgb(\n\t\t\targ1 === '+'\n\t\t\t\t? base.red() + arg2.red()\n\t\t\t: base.red() - arg2.red(),\n\t\t\targ1 === '+'\n\t\t\t\t? base.green() + arg2.green()\n\t\t\t: base.green() - arg2.green(),\n\t\t\targ1 === '+'\n\t\t\t\t? base.blue() + arg2.blue()\n\t\t\t: base.blue() - arg2.blue()\n\t\t);\n\n\t\treturn modifiedColor;\n\t} else if (arg1 !== undefined && minusPlusMatch.test(arg1)) {\n\t\tconst modifiedColor = base.rgb(\n\t\t\targ1 === '+'\n\t\t\t\t? base.red() + arg2\n\t\t\t: base.red() - arg2,\n\t\t\targ1 === '+'\n\t\t\t\t? base.green() + arg3\n\t\t\t: base.green() - arg3,\n\t\t\targ1 === '+'\n\t\t\t\t? base.blue() + arg4\n\t\t\t: base.blue() - arg4\n\t\t);\n\n\t\treturn modifiedColor;\n\t} else if (arg1 !== undefined && arg2 !== undefined) {\n\t\tconst modifiedColor = base.rgb(\n\t\t\tbase.red() * arg2,\n\t\t\tbase.green() * arg2,\n\t\t\tbase.blue() * arg2\n\t\t);\n\n\t\treturn modifiedColor;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid rgb() adjuster`);\n\t}\n}\n\n// return a transformed color using a blend/blenda adjustment\nfunction transformBlendAdjuster(base, node, isAlphaBlend, opts) {\n\tconst [color, percentage, colorspace = 'rgb'] = transformArgsByParams(node, [\n\t\t[transformColor, transformPercentage, transformColorSpace]\n\t]);\n\n\tif (percentage !== undefined) {\n\t\tconst modifiedColor = isAlphaBlend\n\t\t\t? base.blenda(color.color, percentage, colorspace)\n\t\t: base.blend(color.color, percentage, colorspace);\n\n\t\treturn modifiedColor;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid blend() adjuster)`);\n\t}\n}\n\n// return a transformed color using a contrast adjustment\nfunction transformContrastAdjuster(base, node, opts) {\n\tconst [percentage] = transformArgsByParams(node, [\n\t\t// <percentage>\n\t\t[transformPercentage]\n\t]);\n\n\tif (percentage !== undefined) {\n\t\tconst modifiedColor = base.contrast(percentage);\n\n\t\treturn modifiedColor;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid contrast() adjuster)`);\n\t}\n}\n\n// return a transformed color using a hue adjustment\nfunction transformHueAdjuster(base, node, opts) {\n\tconst [operatorOrHue, adjustment] = transformArgsByParams(node, [\n\t\t// [ + | - | * ] <angle>\n\t\t[transformMinusPlusTimesOperator, transformHue],\n\t\t// <angle>\n\t\t[transformHue]\n\t]);\n\n\tif (operatorOrHue !== undefined) {\n\t\tconst existingHue = base.hue();\n\n\t\tconst modifiedValue = adjustment !== undefined\n\t\t\t? operatorOrHue === '+'\n\t\t\t\t? existingHue + Number(adjustment)\n\t\t\t: operatorOrHue === '-'\n\t\t\t\t? existingHue - Number(adjustment)\n\t\t\t: operatorOrHue === '*'\n\t\t\t\t? existingHue * Number(adjustment)\n\t\t\t: Number(adjustment)\n\t\t: Number(operatorOrHue);\n\n\t\treturn base.hue(modifiedValue);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hue() function)`);\n\t}\n}\n\n// [ b | blackness | l | lightness | s | saturation | w | whiteness ]( [ + | - | * ]? <percentage> )\nfunction transformBlacknessLightnessSaturationWhitenessAdjuster(base, node, opts) {\n\tconst channel = node.value.toLowerCase().replace(/^b$/, 'blackness').replace(/^l$/, 'lightness').replace(/^s$/, 'saturation').replace(/^w$/, 'whiteness');\n\tconst [operatorOrValue, adjustment] = transformArgsByParams(node, [\n\t\t[transformMinusPlusTimesOperator, transformPercentage],\n\t\t[transformPercentage]\n\t]);\n\n\tif (operatorOrValue !== undefined) {\n\t\tconst existingValue = base[channel]();\n\n\t\tconst modifiedValue = adjustment !== undefined\n\t\t\t? operatorOrValue === '+'\n\t\t\t\t? existingValue + Number(adjustment)\n\t\t\t: operatorOrValue === '-'\n\t\t\t\t? existingValue - Number(adjustment)\n\t\t\t: operatorOrValue === '*'\n\t\t\t\t? existingValue * Number(adjustment)\n\t\t\t: Number(adjustment)\n\t\t: Number(operatorOrValue);\n\n\t\treturn base[channel](modifiedValue);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid ${channel}() function)`);\n\t}\n}\n\n// return a transformed color using shade/tint adjustments\nfunction transformShadeTintAdjuster(base, node, opts) {\n\tconst channel = node.value.toLowerCase();\n\tconst [percentage] = transformArgsByParams(node, [\n\t\t// [ shade | tint ]( <percentage> )\n\t\t[transformPercentage]\n\t]);\n\n\tif (percentage !== undefined) {\n\t\tconst modifiedValue = Number(percentage);\n\n\t\treturn base[channel](modifiedValue);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected valid ${channel}() arguments`);\n\t}\n}\n\n/* Argument Transforms\n/* ========================================================================== */\n\n// return a transformed color space\nfunction transformColorSpace(node, opts) {\n\tif (isColorSpace(node)) {\n\t\t// [ hsl | hwb | rgb ]\n\t\treturn node.value;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid color space)`);\n\t}\n}\n\n// return a transformed alpha value\nfunction transformAlpha(node, opts) {\n\tif (isNumber(node)) {\n\t\t// <number>\n\t\treturn node.value * 100;\n\t} else if (isPercentage(node)) {\n\t\t// <percentage>\n\t\treturn transformPercentage(node, opts);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid alpha value)`);\n\t}\n}\n\n// return a transformed rgb number\nfunction transformRGBNumber(node, opts) {\n\tif (isNumber(node)) {\n\t\t// <number>\n\t\treturn node.value / 2.55;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid RGB value)`);\n\t}\n}\n\n// return a transformed hue\nfunction transformHue(node, opts) {\n\tif (isHue(node)) {\n\t\t// <hue> = <number> | <angle>\n\t\tconst unit = node.unit.toLowerCase();\n\n\t\tif (unit === 'grad') {\n\t\t\t// if <angle> = <gradian> (400 per circle)\n\t\t\treturn convertGtoD(node.value);\n\t\t} else if (unit === 'rad') {\n\t\t\t// if <angle> = <radian> (2π per circle)\n\t\t\treturn convertRtoD(node.value);\n\t\t} else if (unit === 'turn') {\n\t\t\t// if <angle> = <turn> (1 per circle)\n\t\t\treturn convertTtoD(node.value);\n\t\t} else {\n\t\t\t// if <angle> = [ <degree> | <number> ] (360 per circle)\n\t\t\treturn convertDtoD(node.value);\n\t\t}\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hue`);\n\t}\n}\n\n// return a transformed percentage\nfunction transformPercentage(node, opts) {\n\tif (isPercentage(node)) {\n\t\t// <percentage>\n\t\treturn Number(node.value);\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid hue`);\n\t}\n}\n\n// return a transformed minus-plus operator\nfunction transformMinusPlusOperator(node, opts) {\n\tif (isMinusPlusOperator(node)) {\n\t\t// [ - | + ]\n\t\treturn node.value;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a plus or minus operator`);\n\t}\n}\n\n// return a transformed times operator\nfunction transformTimesOperator(node, opts) {\n\tif (isTimesOperator(node)) {\n\t\t// [ * ]\n\t\treturn node.value;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a times operator`);\n\t}\n}\n\n// return a transformed minus-plus-times operator\nfunction transformMinusPlusTimesOperator(node, opts) {\n\tif (isMinusPlusTimesOperator(node)) {\n\t\t// [ - | + | * ]\n\t\treturn node.value;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a plus, minus, or times operator`);\n\t}\n}\n\n/* Additional transforms\n/* ========================================================================== */\n\nfunction transformWord(node, opts) {\n\tif (isWord(node)) {\n\t\treturn node.value;\n\t} else {\n\t\treturn manageUnresolved(node, opts, node.value, `Expected a valid word`);\n\t}\n}\n\nfunction transformNode(node) {\n\treturn Object(node);\n}\n\n/* Transform helper\n/* ========================================================================== */\n\n// return the first set of transformed arguments allowable by the parameters\nfunction transformArgsByParams(node, params) {\n\tconst nodes = (node.nodes || []).slice(1, -1);\n\tconst opts = { unresolved: 'ignore' };\n\n\treturn params.map(param => nodes.map(\n\t\t(childNode, index) => typeof param[index] === 'function' ? param[index](childNode, opts) : undefined\n\t).filter(child => typeof child !== 'boolean')).filter(param => param.every(\n\t\tresult => result !== undefined\n\t))[0] || [];\n}\n\n/* Walk helper (required because the default walker is affected by mutations)\n/* ========================================================================== */\n\n// run a function over each node and hen walk each child node of that node\nfunction walk(node, fn) {\n\tfn(node);\n\n\tif (Object(node.nodes).length) {\n\t\tnode.nodes.slice().forEach(childNode => {\n\t\t\twalk(childNode, fn);\n\t\t});\n\t}\n}\n\n/* Variable validators\n/* ========================================================================== */\n\n// return whether the node is a var function\nfunction isVariable(node) {\n\t// var()\n\treturn Object(node).type === 'func' && varMatch.test(node.value);\n}\n\n/* Adjustment validators\n/* ========================================================================== */\n\n// return whether the node is an a/alpha/blue/green/red adjuster\nfunction isAlphaBlueGreenRedAdjuster(node) {\n\t// [ a(), alpha(), blue(), green(), red() ]\n\treturn Object(node).type === 'func' && alphaBlueGreenRedMatch.test(node.value);\n}\n\n// return whether the node is an rgb adjuster\nfunction isRGBAdjuster(node) {\n\treturn Object(node).type === 'func' && rgbMatch.test(node.value);\n}\n\n// return whether the node is a hue adjuster\nfunction isHueAdjuster(node) {\n\t// [ h() | hue() ]\n\treturn Object(node).type === 'func' && hueMatch.test(node.value);\n}\n\n// return whether the node is a blackness/lightness/saturation/whiteness adjuster\nfunction isBlacknessLightnessSaturationWhitenessAdjuster(node) {\n\t// [ b() | blackness() | l() | lightness() | s() | saturation() | w() | whiteness() ]\n\treturn Object(node).type === 'func' && blacknessLightnessSaturationWhitenessMatch.test(node.value);\n}\n\n// return whether the node is a shade/tint adjuster\nfunction isShadeTintAdjuster(node) {\n\t// [ shade() | tint() ]\n\treturn Object(node).type === 'func' && shadeTintMatch.test(node.value);\n}\n\n// return whether the node is a blend adjuster\nfunction isBlendAdjuster(node) {\n\t// [ blend(), blenda() ]\n\treturn Object(node).type === 'func' && blendMatch.test(node.value);\n}\n\n// return whether the node is a contrast adjuster\nfunction isContrastAdjuster(node) {\n\t// [ contrast() ]\n\treturn Object(node).type === 'func' && contrastMatch.test(node.value);\n}\n\n/* Color validators\n/* ========================================================================== */\n\n// return whether the node is an rgb/rgba color function\nfunction isRGBFunction(node) {\n\t// [ rgb(), rgba() ]\n\treturn Object(node).type === 'func' && rgbaMatch.test(node.value);\n}\n\n// return whether the node is an hsl color function\nfunction isHSLFunction(node) {\n\t// [ hsl(), hsla() ]\n\treturn Object(node).type === 'func' && hslaMatch.test(node.value);\n}\n\n// return whether the node is an hwb color function\nfunction isHWBFunction(node) {\n\t// hwb()\n\treturn Object(node).type === 'func' && hwbMatch.test(node.value);\n}\n\n// return whether the node is a color-mod function\nfunction isColorModFunction(node) {\n\t// color-mod()\n\treturn Object(node).type === 'func' && colorModMatch.test(node.value);\n}\n\n// return whether the node is a valid named-color\nfunction isNamedColor(node) {\n\treturn Object(node).type === 'word' && Boolean(convertNtoRGB(node.value));\n}\n\n// return whether the node is a valid hex color\nfunction isHexColor(node) {\n\t// #<hex-color>{3,4,6,8}\n\treturn Object(node).type === 'word' && hexColorMatch.test(node.value);\n}\n\n// return whether the node is a valid color space\nfunction isColorSpace(node) {\n\t// [ hsl | hwb | rgb ]\n\treturn Object(node).type === 'word' && colorSpaceMatch.test(node.value);\n}\n\n/* Additional validators\n/* ========================================================================== */\n\n// return whether the hue value is valid\nfunction isHue(node) {\n\treturn Object(node).type === 'number' && hueUnitMatch.test(node.unit);\n}\n\n// return whether the comma is valid\nfunction isComma(node) {\n\treturn Object(node).type === 'comma';\n}\n\n// return whether the slash operator is valid\nfunction isSlash(node) {\n\treturn Object(node).type === 'operator' && node.value === '/';\n}\n\n// return whether the number is valid\nfunction isNumber(node) {\n\treturn Object(node).type === 'number' && node.unit === '';\n}\n\n// return whether the mind-plus operator is valid\nfunction isMinusPlusOperator(node) {\n\treturn Object(node).type === 'operator' && minusPlusMatch.test(node.value);\n}\n\n// return whether the minus-plus-times operator is valid\nfunction isMinusPlusTimesOperator(node) {\n\treturn Object(node).type === 'operator' && minusPlusTimesMatch.test(node.value);\n}\n\n// return whether the times operator is valid\nfunction isTimesOperator(node) {\n\treturn Object(node).type === 'operator' && timesMatch.test(node.value);\n}\n\n// return whether the percentage is valid\nfunction isPercentage(node) {\n\treturn Object(node).type === 'number' && (node.unit === '%' || node.value === '0');\n}\n\n// return whether the node is a word\nfunction isWord(node) {\n\t// <word>\n\treturn Object(node).type === 'word';\n}\n\n/* Matchers\n/* ========================================================================== */\n\nconst alphaMatch = /^a(lpha)?$/i;\nconst alphaBlueGreenRedMatch = /^(a(lpha)?|blue|green|red)$/i;\nconst blacknessLightnessSaturationWhitenessMatch = /^(b(lackness)?|l(ightness)?|s(aturation)?|w(hiteness)?)$/i;\nconst blendMatch = /^blenda?$/i;\nconst colorModMatch = /^color-mod$/i;\nconst colorSpaceMatch = /^(hsl|hwb|rgb)$/i;\nconst contrastMatch = /^contrast$/i;\nconst hexColorMatch = /^#(?:([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?|([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?)$/i;\nconst hslaMatch = /^hsla?$/i;\nconst hueUnitMatch = /^(deg|grad|rad|turn)?$/i;\nconst hueMatch = /^h(ue)?$/i;\nconst hwbMatch = /^hwb$/i;\nconst minusPlusMatch = /^[+-]$/;\nconst minusPlusTimesMatch = /^[*+-]$/;\nconst rgbMatch = /^rgb$/i;\nconst rgbaMatch = /^rgba?$/i;\nconst shadeTintMatch = /^(shade|tint)$/i;\nconst varMatch = /^var$/i;\nconst looseVarMatch = /(^|[^\\w-])var\\(/i;\nconst timesMatch = /^[*]$/;\n", "import getCustomProperties from './lib/get-custom-properties';\nimport importCustomPropertiesFromSources from './lib/import-from';\nimport parser from 'postcss-values-parser';\nimport postcss from 'postcss';\nimport transformAST from './lib/transform';\n\nexport default postcss.plugin('postcss-color-mod-function', opts => {\n\t// how unresolved functions and arguments should be handled (default: \"throw\")\n\tconst unresolvedOpt = String(Object(opts).unresolved || 'throw').toLowerCase();\n\n\t// how transformed colors will be produced in CSS\n\tconst stringifierOpt = Object(opts).stringifier || (color => color.toLegacy());\n\n\t// sources to import custom selectors from\n\tconst importFrom = [].concat(Object(opts).importFrom || []);\n\n\t//  whether var() within color-mod() should use Custom Properties or var() fallback\n\tconst transformVarsOpt = 'transformVars' in Object(opts) ? opts.transformVars : true;\n\n\t// promise any custom selectors are imported\n\tconst customPropertiesPromise = importCustomPropertiesFromSources(importFrom);\n\n\treturn async (root, result) => {\n\t\tconst customProperties = Object.assign(\n\t\t\tawait customPropertiesPromise,\n\t\t\tgetCustomProperties(root, { preserve: true })\n\t\t);\n\n\t\troot.walkDecls(decl => {\n\t\t\tconst originalValue = decl.value;\n\n\t\t\tif (colorModFunctionMatch.test(originalValue)) {\n\t\t\t\tconst ast = parser(originalValue, { loose: true }).parse();\n\n\t\t\t\ttransformAST(ast, {\n\t\t\t\t\tunresolved: unresolvedOpt,\n\t\t\t\t\tstringifier: stringifierOpt,\n\t\t\t\t\ttransformVars: transformVarsOpt,\n\t\t\t\t\tdecl,\n\t\t\t\t\tresult,\n\t\t\t\t\tcustomProperties\n\t\t\t\t});\n\n\t\t\t\tconst modifiedValue = ast.toString();\n\n\t\t\t\tif (originalValue !== modifiedValue) {\n\t\t\t\t\tdecl.value = modifiedValue;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n\nconst colorModFunctionMatch = /(^|[^\\w-])color-mod\\(/i;\n"], "names": ["getCustomProperties", "root", "opts", "customPropertiesFromHtmlElement", "customPropertiesFromRootPsuedo", "nodes", "slice", "for<PERSON>ach", "rule", "customPropertiesObject", "isHtmlRule", "isRootRule", "decl", "isCustomDecl", "prop", "valueParser", "value", "parse", "preserve", "remove", "isEmptyParent", "htmlSelectorRegExp", "rootSelectorRegExp", "customPropertyRegExp", "node", "type", "test", "selector", "Object", "length", "importCustomPropertiesFromCSSAST", "importCustomPropertiesFromCSSFile", "from", "css", "readFile", "postcss", "importCustomPropertiesFromObject", "object", "customProperties", "assign", "importCustomPropertiesFromJSONFile", "readJSON", "importCustomPropertiesFromJSFile", "importCustomPropertiesFromSources", "sources", "map", "source", "Promise", "Function", "String", "path", "resolve", "extname", "toLowerCase", "reduce", "reject", "fs", "error", "result", "JSON", "convertDtoD", "deg", "convertGtoD", "grad", "convertRtoD", "rad", "Math", "PI", "convertTtoD", "turn", "convertNtoRGB", "name", "names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "transparent", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "c", "convertHtoRGB", "hex", "match", "hexColorMatch", "r", "g", "b", "a", "rr", "gg", "bb", "aa", "undefined", "parseInt", "alpha", "Color", "constructor", "color", "colorspace", "hue", "rgb2hue", "blackness", "hwb", "color2hwb", "blend", "percentage", "base", "blenda", "rgb", "color2rgb", "contrast", "hsl", "color2hsl", "lightness", "saturation", "shade", "whiteness", "tint", "toHSL", "color2hslString", "toHWB", "color2hwbString", "toLegacy", "color2legacyString", "toRGB", "color2rgbString", "toRGBLegacy", "color2rgbLegacyString", "toString", "color2string", "isBlendingAlpha", "addition", "subtraction", "h1", "s1", "l1", "a1", "h2", "s2", "l2", "a2", "w1", "b1", "w2", "b2", "r1", "g1", "r2", "g2", "channels", "keys", "channel", "is<PERSON>ue", "isRGB", "blueGreenRedMatch", "normalize", "min", "max", "normalizedValue", "hsl2rgb", "hwb2rgb", "rgb2hsl", "hwb2hsl", "rgb2hwb", "hsl2hwb", "luminance", "rgb2luminance", "maxContrastColor", "contrastRatio", "colors2contrast", "minContrastColor", "colors2contrastRatioColor", "color1", "color2", "rgb1", "rgb2", "channel2luminance", "redLuminance", "greenLuminance", "blueLuminance", "pow", "maxHWB", "modifiedHWB", "minW", "minB", "maxW", "maxB", "abs", "midW", "round", "midB", "isOpaque", "color2hslLegacyString", "manageUnresolved", "word", "message", "unresolved", "warn", "transformAST", "child", "isColorModFunction", "transformVars", "transformVariables", "transformColorModFunction", "replaceWith", "parser", "raws", "stringifier", "walk", "isVariable", "transformArgsByParams", "transformWord", "is<PERSON><PERSON><PERSON>", "transformNode", "fallbackNode", "customPropertyValue", "looseVarMatch", "rootChildAST", "clone", "customProperty<PERSON>hild", "parent", "insertBefore", "transformColor", "isRGBFunction", "transformRGBFunction", "isHSLFunction", "transformHSLFunction", "isHWBFunction", "transformHWBFunction", "isHexColor", "transformHexColor", "isNamedColor", "transformNamedColor", "transformPercentage", "isSlash", "transformAlpha", "transformRGBNumber", "transformHue", "colorOrHueNode", "adjusterNodes", "adjustedColor", "transformColorByAdjusters", "isAlphaBlueGreenRedAdjuster", "transformAlphaBlueGreenRedAdjuster", "isRGBAdjuster", "transformRGBAdjuster", "isHueAdjuster", "transformHueAdjuster", "isBlacknessLightnessSaturationWhitenessAdjuster", "transformBlacknessLightnessSaturationWhitenessAdjuster", "isShadeTintAdjuster", "transformShadeTintAdjuster", "isBlendAdjuster", "transformBlendAdjuster", "isContrastAdjuster", "transformContrastAdjuster", "alphaMatch", "transformMinusPlusOperator", "transformTimesOperator", "operatorOrValue", "adjustment", "replace", "existingValue", "modifiedValue", "Number", "modifiedColor", "arg1", "arg2", "arg3", "arg4", "minusPlusMatch", "isAlphaBlend", "transformColorSpace", "transformMinusPlusTimesOperator", "operatorOrHue", "existingHue", "isColorSpace", "isNumber", "isPercentage", "unit", "isMinusPlusOperator", "isTimesOperator", "isMinusPlusTimesOperator", "isWord", "params", "param", "childNode", "index", "filter", "every", "fn", "varMatch", "alphaBlueGreenRedMatch", "rgbMatch", "hueMatch", "blacknessLightnessSaturationWhitenessMatch", "shadeTintMatch", "blendMatch", "contrastMatch", "rgbaMatch", "hslaMatch", "hwbMatch", "colorModMatch", "Boolean", "colorSpaceMatch", "hueUnitMatch", "minusPlusTimesMatch", "timesMatch", "plugin", "unresolvedOpt", "stringifierOpt", "importFrom", "concat", "transformVarsOpt", "customPropertiesPromise", "walkDecls", "originalValue", "colorModFunctionMatch", "ast", "loose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGe,SAASA,mBAAT,CAA6BC,IAA7B,EAAmCC,IAAnC,EAAyC;;QAEjDC,+BAA+B,GAAG,EAAxC;QACMC,8BAA8B,GAAG,EAAvC,CAHuD;;EAMvDH,IAAI,CAACI,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BC,IAAI,IAAI;UAC5BC,sBAAsB,GAAGC,UAAU,CAACF,IAAD,CAAV,GAC5BL,+BAD4B,GAE7BQ,UAAU,CAACH,IAAD,CAAV,GACCJ,8BADD,GAEA,IAJF,CADkC;;QAQ9BK,sBAAJ,EAA4B;MAC3BD,IAAI,CAACH,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BK,IAAI,IAAI;YAC9BC,YAAY,CAACD,IAAD,CAAhB,EAAwB;gBACfE,IADe,GACNF,IADM,CACfE,IADe;;UAIvBL,sBAAsB,CAACK,IAAD,CAAtB,GAA+BC,WAAW,CAACH,IAAI,CAACI,KAAN,CAAX,CAAwBC,KAAxB,EAA/B,CAJuB;;cAOnB,CAACf,IAAI,CAACgB,QAAV,EAAoB;YACnBN,IAAI,CAACO,MAAL;;;OATH,EAD2B;;UAgBvB,CAACjB,IAAI,CAACgB,QAAN,IAAkBE,aAAa,CAACZ,IAAD,CAAnC,EAA2C;QAC1CA,IAAI,CAACW,MAAL;;;GAzBH,EANuD;;2BAqC3ChB,+BAAZ,EAAgDC,8BAAhD;;;AAID,MAAMiB,kBAAkB,GAAG,SAA3B;AACA,MAAMC,kBAAkB,GAAG,UAA3B;AACA,MAAMC,oBAAoB,GAAG,iBAA7B;;AAGA,MAAMb,UAAU,GAAGc,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAc,MAAd,IAAwBJ,kBAAkB,CAACK,IAAnB,CAAwBF,IAAI,CAACG,QAA7B,CAAxB,IAAkEC,MAAM,CAACJ,IAAI,CAACnB,KAAN,CAAN,CAAmBwB,MAAhH;;AACA,MAAMlB,UAAU,GAAGa,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAc,MAAd,IAAwBH,kBAAkB,CAACI,IAAnB,CAAwBF,IAAI,CAACG,QAA7B,CAAxB,IAAkEC,MAAM,CAACJ,IAAI,CAACnB,KAAN,CAAN,CAAmBwB,MAAhH;;;AAGA,MAAMhB,YAAY,GAAGW,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAc,MAAd,IAAwBF,oBAAoB,CAACG,IAArB,CAA0BF,IAAI,CAACV,IAA/B,CAArD;;;AAGA,MAAMM,aAAa,GAAGI,IAAI,IAAII,MAAM,CAACJ,IAAI,CAACnB,KAAN,CAAN,CAAmBwB,MAAnB,KAA8B,CAA5D;;AClDA;;;AAGA,SAASC,gCAAT,CAA0C7B,IAA1C,EAAgD;SACxCD,mBAAmB,CAACC,IAAD,EAAO;IAAEiB,QAAQ,EAAE;GAAnB,CAA1B;;;;;;SAMca;;;;;;;;yDAAf,WAAiDC,IAAjD,EAAuD;UAChDC,GAAG,SAASC,QAAQ,CAACF,IAAD,CAA1B;UACM/B,IAAI,GAAGkC,OAAO,CAAClB,KAAR,CAAcgB,GAAd,EAAmB;MAAED;KAArB,CAAb;WAEOF,gCAAgC,CAAC7B,IAAD,CAAvC;;;;;AAMD,SAASmC,gCAAT,CAA0CC,MAA1C,EAAkD;QAC3CC,gBAAgB,GAAGV,MAAM,CAACW,MAAP,CACxB,EADwB,EAExBX,MAAM,CAACS,MAAD,CAAN,CAAeC,gBAAf,IAAmCV,MAAM,CAACS,MAAD,CAAN,CAAe,mBAAf,CAFX,CAAzB;;OAKK,MAAMvB,IAAX,IAAmBwB,gBAAnB,EAAqC;IACpCA,gBAAgB,CAACxB,IAAD,CAAhB,GAAyBC,WAAW,CAACuB,gBAAgB,CAACxB,IAAD,CAAjB,CAAX,CAAoCG,KAApC,EAAzB;;;SAGMqB,gBAAP;;;;;;SAMcE;;;;;;;;0DAAf,WAAkDR,IAAlD,EAAwD;UACjDK,MAAM,SAASI,QAAQ,CAACT,IAAD,CAA7B;WAEOI,gCAAgC,CAACC,MAAD,CAAvC;;;;;SAMcK;;;;;;;;wDAAf,WAAgDV,IAAhD,EAAsD;UAC/CK,MAAM,SAAS,wBAAOL,IAAP,EAArB;WAEOI,gCAAgC,CAACC,MAAD,CAAvC;;;;;AAMD,AAAe,SAASM,iCAAT,CAA2CC,OAA3C,EAAoD;SAC3DA,OAAO,CAACC,GAAR,CAAYC,MAAM,IAAI;QACxBA,MAAM,YAAYC,OAAtB,EAA+B;aACvBD,MAAP;KADD,MAEO,IAAIA,MAAM,YAAYE,QAAtB,EAAgC;aAC/BF,MAAM,EAAb;KAJ2B;;;UAQtB5C,IAAI,GAAG4C,MAAM,KAAKlB,MAAM,CAACkB,MAAD,CAAjB,GAA4BA,MAA5B,GAAqC;MAAEd,IAAI,EAAEiB,MAAM,CAACH,MAAD;KAAhE,CAR4B;;QAWxB5C,IAAI,CAACoC,gBAAL,IAAyBpC,IAAI,CAAC,mBAAD,CAAjC,EAAwD;aAChDA,IAAP;KAZ2B;;;UAgBtB8B,IAAI,GAAGkB,IAAI,CAACC,OAAL,CAAaF,MAAM,CAAC/C,IAAI,CAAC8B,IAAL,IAAa,EAAd,CAAnB,CAAb,CAhB4B;;UAmBtBP,IAAI,GAAG,CAACvB,IAAI,CAACuB,IAAL,IAAayB,IAAI,CAACE,OAAL,CAAapB,IAAb,EAAmB1B,KAAnB,CAAyB,CAAzB,CAAd,EAA2C+C,WAA3C,EAAb;WAEO;MAAE5B,IAAF;MAAQO;KAAf;GArBM,EAsBJsB,MAtBI;;;iCAsBG,WAAOhB,gBAAP,EAAyBQ,MAAzB,EAAoC;0BAChBA,MADgB;YACrCrB,IADqC,SACrCA,IADqC;YAC/BO,IAD+B,SAC/BA,IAD+B;;UAGzCP,IAAI,KAAK,KAAb,EAAoB;eACZG,MAAM,CAACW,MAAP,QAAoBD,gBAApB,GAAsCR,gCAAgC,CAACE,IAAD,CAAtE,CAAP;;;UAGGP,IAAI,KAAK,KAAb,EAAoB;eACZG,MAAM,CAACW,MAAP,QAAoBD,gBAApB,UAA4CP,iCAAiC,CAACC,IAAD,CAA7E,EAAP;;;UAGGP,IAAI,KAAK,IAAb,EAAmB;eACXG,MAAM,CAACW,MAAP,QAAoBD,gBAApB,UAA4CI,gCAAgC,CAACV,IAAD,CAA5E,EAAP;;;UAGGP,IAAI,KAAK,MAAb,EAAqB;eACbG,MAAM,CAACW,MAAP,QAAoBD,gBAApB,UAA4CE,kCAAkC,CAACR,IAAD,CAA9E,EAAP;;;aAGMJ,MAAM,CAACW,MAAP,QAAoBD,gBAApB,UAA4CF,gCAAgC,QAAOU,MAAP,EAA5E,EAAP;KAzCM;;;;;OA0CJ,EA1CI,CAAP;;;;;AAgDD,MAAMZ,QAAQ,GAAGF,IAAI,IAAI,IAAIe,OAAJ,CAAY,CAACI,OAAD,EAAUI,MAAV,KAAqB;EACzDC,EAAE,CAACtB,QAAH,CAAYF,IAAZ,EAAkB,MAAlB,EAA0B,CAACyB,KAAD,EAAQC,MAAR,KAAmB;QACxCD,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNN,OAAO,CAACO,MAAD,CAAP;;GAJF;CADwB,CAAzB;;AAUA,MAAMjB,QAAQ;;AAAA;gCAAG,WAAMT,IAAN;WAAc2B,IAAI,CAAC1C,KAAL,QAAiBiB,QAAQ,CAACF,IAAD,CAAzB,EAAd;GAAH;;kBAARS,QAAQ;;;GAAd;;ACvHA;;AAGA,AAAO,SAASmB,WAAT,CAAqBC,GAArB,EAA0B;SACzBA,GAAG,GAAG,GAAb;;;;;AAMD,AAAO,SAASC,WAAT,CAAqBC,IAArB,EAA2B;SAC1BA,IAAI,GAAG,GAAP,GAAa,GAApB;;;;;AAMD,AAAO,SAASC,WAAT,CAAqBC,GAArB,EAA0B;SACzBA,GAAG,GAAG,GAAN,GAAYC,IAAI,CAACC,EAAjB,GAAsB,GAA7B;;;;;AAMD,AAAO,SAASC,WAAT,CAAqBC,IAArB,EAA2B;SAC1BA,IAAI,GAAG,GAAP,GAAa,GAApB;;;;;AAMD,AAAO,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;QAC7BC,KAAK,GAAG;IACbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADE;IAEbC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAFD;IAGbC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAHO;IAIbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAJC;IAKbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CALM;IAMbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CANM;IAObC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAPK;IAQbC,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CARM;IASbC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CATH;IAUbC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAVO;IAWbC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAXC;IAYbC,KAAK,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAZM;IAabC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAbE;IAcbC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAdE;IAebC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAfC;IAgBbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAhBE;IAiBbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAjBM;IAkBbC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlBH;IAmBbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnBG;IAoBbC,OAAO,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CApBI;IAqBbC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CArBO;IAsBbC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtBG;IAuBbC,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvBG;IAwBbC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAxBF;IAyBbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzBG;IA0BbC,SAAS,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CA1BE;IA2BbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3BG;IA4BbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5BE;IA6BbC,WAAW,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CA7BA;IA8BbC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9BH;IA+BbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA/BC;IAgCbC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhCC;IAiCbC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAjCI;IAkCbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlCC;IAmCbC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnCD;IAoCbC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CApCF;IAqCbC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CArCF;IAsCbC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CAtCF;IAuCbC,aAAa,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvCF;IAwCbC,UAAU,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAxCC;IAyCbC,QAAQ,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAzCG;IA0CbC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1CA;IA2CbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3CI;IA4CbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5CI;IA6CbC,UAAU,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7CC;IA8CbC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA9CE;IA+CbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/CA;IAgDbC,WAAW,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CAhDA;IAiDbC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAjDI;IAkDbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlDE;IAmDbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnDC;IAoDbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CApDO;IAqDbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CArDE;IAsDbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtDO;IAuDbC,KAAK,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAvDM;IAwDbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAxDA;IAyDbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzDO;IA0DbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1DG;IA2DbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3DI;IA4DbC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5DE;IA6DbC,MAAM,EAAE,CAAC,EAAD,EAAK,CAAL,EAAQ,GAAR,CA7DK;IA8DbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9DM;IA+DbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/DM;IAgEbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhEG;IAiEbC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjEF;IAkEbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlEE;IAmEbC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnED;IAoEbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApEE;IAqEbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArEC;IAsEbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtEE;IAuEbC,oBAAoB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvET;IAwEbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxEE;IAyEbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzEC;IA0EbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1EE;IA2EbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3EE;IA4EbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5EA;IA6EbC,aAAa,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7EF;IA8EbC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9ED;IA+EbC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/EH;IAgFbC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhFH;IAiFbC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjFH;IAkFbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlFA;IAmFbC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAnFO;IAoFbC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CApFE;IAqFbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArFM;IAsFbC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAtFI;IAuFbC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAvFK;IAwFbC,gBAAgB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxFL;IAyFbC,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAzFC;IA0FbC,YAAY,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CA1FD;IA2FbC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3FD;IA4FbC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA5FH;IA6FbC,eAAe,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7FJ;IA8FbC,iBAAiB,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA9FN;IA+FbC,eAAe,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA/FJ;IAgGbC,eAAe,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhGJ;IAiGbC,YAAY,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CAjGD;IAkGbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlGE;IAmGbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnGE;IAoGbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApGG;IAqGbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArGA;IAsGbC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtGO;IAuGbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvGI;IAwGbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAxGM;IAyGbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzGE;IA0GbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA1GK;IA2GbC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,CAAV,CA3GE;IA4GbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5GK;IA6GbC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7GF;IA8GbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9GE;IA+GbC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/GF;IAgHbC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhHF;IAiHbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjHC;IAkHbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlHE;IAmHbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAnHO;IAoHbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApHO;IAqHbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArHO;IAsHbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtHC;IAuHbC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAvHK;IAwHbC,aAAa,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAxHF;IAyHbC,GAAG,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAzHQ;IA0HbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1HE;IA2HbC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA3HE;IA4HbC,WAAW,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5HA;IA6HbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7HK;IA8HbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CA9HC;IA+HbC,QAAQ,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA/HG;IAgIbC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhIG;IAiIbC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAjIK;IAkIbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlIK;IAmIbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnII;IAoIbC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CApIE;IAqIbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArIE;IAsIbC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtIE;IAuIbC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvIO;IAwIbC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAxIA;IAyIbC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAzIE;IA0IbC,GAAG,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1IQ;IA2IbC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA3IO;IA4IbC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5II;IA6IbC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA7IK;IA8IbC,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CA9IA;IA+IbC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA/IE;IAgJbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhJK;IAiJbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjJM;IAkJbC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlJM;IAmJbC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnJC;IAoJbC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CApJK;IAqJbC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX;GArJd;SAwJOrJ,KAAK,CAACD,IAAD,CAAL,IAAeC,KAAK,CAACD,IAAD,CAAL,CAAY1B,GAAZ,CAAgBiL,CAAC,IAAIA,CAAC,GAAG,IAAzB,CAAtB;;;;;AAOD,AAAO,SAASC,aAAT,CAAuBC,GAAvB,EAA4B;;iBAEG,CAACA,GAAG,CAACC,KAAJ,CAAUC,aAAV,KAA4B,EAA7B,EAAiC5N,KAAjC,CAAuC,CAAvC,CAFH;;QAE3B6N,CAF2B;QAExBC,CAFwB;QAErBC,CAFqB;QAElBC,CAFkB;QAEfC,EAFe;QAEXC,EAFW;QAEPC,EAFO;QAEHC,EAFG;;MAI9BH,EAAE,KAAKI,SAAP,IAAoBR,CAAC,KAAKQ,SAA9B,EAAyC;UAClC1C,GAAG,GAAKsC,EAAE,KAAKI,SAAP,GAAmBC,QAAQ,CAACL,EAAD,EAAK,EAAL,CAA3B,GAAsCJ,CAAC,KAAKQ,SAAN,GAAkBC,QAAQ,CAACT,CAAC,GAAGA,CAAL,EAAQ,EAAR,CAA1B,GAAwC,CAA5F;UACMpG,KAAK,GAAGyG,EAAE,KAAKG,SAAP,GAAmBC,QAAQ,CAACJ,EAAD,EAAK,EAAL,CAA3B,GAAsCJ,CAAC,KAAKO,SAAN,GAAkBC,QAAQ,CAACR,CAAC,GAAGA,CAAL,EAAQ,EAAR,CAA1B,GAAwC,CAA5F;UACMlJ,IAAI,GAAIuJ,EAAE,KAAKE,SAAP,GAAmBC,QAAQ,CAACH,EAAD,EAAK,EAAL,CAA3B,GAAsCJ,CAAC,KAAKM,SAAN,GAAkBC,QAAQ,CAACP,CAAC,GAAGA,CAAL,EAAQ,EAAR,CAA1B,GAAwC,CAA5F;UACMQ,KAAK,GAAGH,EAAE,KAAKC,SAAP,GAAmBC,QAAQ,CAACF,EAAD,EAAK,EAAL,CAA3B,GAAsCJ,CAAC,KAAKK,SAAN,GAAkBC,QAAQ,CAACN,CAAC,GAAGA,CAAL,EAAQ,EAAR,CAA1B,GAAwC,GAA5F;WAEO,CAACrC,GAAD,EAAMlE,KAAN,EAAa7C,IAAb,EAAmB2J,KAAnB,EAA0BhM,GAA1B,CAA8BiL,CAAC,IAAIA,CAAC,GAAG,IAAvC,CAAP;;;SAGMa,SAAP;;AAGD,MAAMT,aAAa,GAAG,yGAAtB;;AC7Me,MAAMY,KAAN,CAAY;EAC1BC,WAAW,CAACC,KAAD,EAAQ;SACbA,KAAL,GAAapN,MAAM,CAACA,MAAM,CAACoN,KAAD,CAAN,CAAcA,KAAd,IAAuBA,KAAxB,CAAnB;SAEKA,KAAL,CAAWC,UAAX,GAAwB,KAAKD,KAAL,CAAWC,UAAX,GACrB,KAAKD,KAAL,CAAWC,UADU,GAEtB,SAASD,KAAT,IAAkB,WAAWA,KAA7B,IAAsC,UAAUA,KAAhD,GACC,KADD,GAEA,SAASA,KAAT,IAAkB,gBAAgBA,KAAlC,IAA2C,eAAeA,KAA1D,GACC,KADD,GAEA,SAASA,KAAT,IAAkB,eAAeA,KAAjC,IAA0C,eAAeA,KAAzD,GACC,KADD,GAEA,SARF;;QAUIA,KAAK,CAACC,UAAN,KAAqB,KAAzB,EAAgC;WAC1BD,KAAL,CAAWE,GAAX,GAAiBC,qBAAO,CAACH,KAAK,CAAC/C,GAAP,EAAY+C,KAAK,CAACjH,KAAlB,EAAyBiH,KAAK,CAAC9J,IAA/B,EAAqC8J,KAAK,CAACE,GAAN,IAAa,CAAlD,CAAxB;;;;EAIFL,KAAK,CAACA,KAAD,EAAQ;UACNG,KAAK,GAAG,KAAKA,KAAnB;WAEOH,KAAK,KAAKF,SAAV,GACJK,KAAK,CAACH,KADF,GAEL,IAAIC,KAAJ,CAAUvM,MAAM,CAACyM,KAAD,EAAQ;MAAEH;KAAV,CAAhB,CAFF;;;EAKDO,SAAS,CAACA,SAAD,EAAY;UACdC,GAAG,GAAGC,SAAS,CAAC,KAAKN,KAAN,CAArB;WAEOI,SAAS,KAAKT,SAAd,GACJU,GAAG,CAACD,SADA,GAEL,IAAIN,KAAJ,CAAUvM,MAAM,CAAC8M,GAAD,EAAM;MAAED;KAAR,CAAhB,CAFF;;;EAKDG,KAAK,CAACP,KAAD,EAAQQ,UAAR,EAAoBP,UAAU,GAAG,KAAjC,EAAwC;UACtCQ,IAAI,GAAG,KAAKT,KAAlB;WAEO,IAAIF,KAAJ,CAAUS,KAAK,CAACE,IAAD,EAAOT,KAAP,EAAcQ,UAAd,EAA0BP,UAA1B,CAAf,CAAP;;;EAGDS,MAAM,CAACV,KAAD,EAAQQ,UAAR,EAAoBP,UAAU,GAAG,KAAjC,EAAwC;UACvCQ,IAAI,GAAG,KAAKT,KAAlB;WAEO,IAAIF,KAAJ,CAAUS,KAAK,CAACE,IAAD,EAAOT,KAAP,EAAcQ,UAAd,EAA0BP,UAA1B,EAAsC,IAAtC,CAAf,CAAP;;;EAGD/J,IAAI,CAACA,IAAD,EAAO;UACJyK,GAAG,GAAGC,SAAS,CAAC,KAAKZ,KAAN,CAArB;WAEO9J,IAAI,KAAKyJ,SAAT,GACJgB,GAAG,CAACzK,IADA,GAEL,IAAI4J,KAAJ,CAAUvM,MAAM,CAACoN,GAAD,EAAM;MAAEzK;KAAR,CAAhB,CAFF;;;EAKD2K,QAAQ,CAACL,UAAD,EAAa;UACdC,IAAI,GAAG,KAAKT,KAAlB;WAEO,IAAIF,KAAJ,CAAUe,QAAQ,CAACJ,IAAD,EAAOD,UAAP,CAAlB,CAAP;;;EAGDzH,KAAK,CAACA,KAAD,EAAQ;UACN4H,GAAG,GAAGC,SAAS,CAAC,KAAKZ,KAAN,CAArB;WAEOjH,KAAK,KAAK4G,SAAV,GACJgB,GAAG,CAAC5H,KADA,GAEL,IAAI+G,KAAJ,CAAUvM,MAAM,CAACoN,GAAD,EAAM;MAAE5H;KAAR,CAAhB,CAFF;;;EAKDmH,GAAG,CAACA,GAAD,EAAM;UACFY,GAAG,GAAGC,SAAS,CAAC,KAAKf,KAAN,CAArB;WAEOE,GAAG,KAAKP,SAAR,GACJmB,GAAG,CAACZ,GADA,GAEL,IAAIJ,KAAJ,CAAUvM,MAAM,CAACuN,GAAD,EAAM;MAAEZ;KAAR,CAAhB,CAFF;;;EAKDc,SAAS,CAACA,SAAD,EAAY;UACdF,GAAG,GAAGC,SAAS,CAAC,KAAKf,KAAN,CAArB;WAEOgB,SAAS,KAAKrB,SAAd,GACJmB,GAAG,CAACE,SADA,GAEL,IAAIlB,KAAJ,CAAUvM,MAAM,CAACuN,GAAD,EAAM;MAAEE;KAAR,CAAhB,CAFF;;;EAKD/D,GAAG,CAACA,GAAD,EAAM;UACF0D,GAAG,GAAGC,SAAS,CAAC,KAAKZ,KAAN,CAArB;WAEO/C,GAAG,KAAK0C,SAAR,GACJgB,GAAG,CAAC1D,GADA,GAEL,IAAI6C,KAAJ,CAAUvM,MAAM,CAACoN,GAAD,EAAM;MAAE1D;KAAR,CAAhB,CAFF;;;EAKD0D,GAAG,CAAC1D,GAAD,EAAMlE,KAAN,EAAa7C,IAAb,EAAmB;UACfyK,GAAG,GAAGC,SAAS,CAAC,KAAKZ,KAAN,CAArB;WAEO,IAAIF,KAAJ,CAAUvM,MAAM,CAACoN,GAAD,EAAM;MAAE1D,GAAF;MAAOlE,KAAP;MAAc7C;KAApB,CAAhB,CAAP;;;EAGD+K,UAAU,CAACA,UAAD,EAAa;UAChBH,GAAG,GAAGC,SAAS,CAAC,KAAKf,KAAN,CAArB;WAEOiB,UAAU,KAAKtB,SAAf,GACJmB,GAAG,CAACG,UADA,GAEL,IAAInB,KAAJ,CAAUvM,MAAM,CAACuN,GAAD,EAAM;MAAEG;KAAR,CAAhB,CAFF;;;EAKDC,KAAK,CAACV,UAAD,EAAa;UACXH,GAAG,GAAGC,SAAS,CAAC,KAAKN,KAAN,CAArB;UACMkB,KAAK,GAAG;MAAEhB,GAAG,EAAE,CAAP;MAAUiB,SAAS,EAAE,CAArB;MAAwBf,SAAS,EAAE,GAAnC;MAAwCH,UAAU,EAAE;KAAlE;UACMA,UAAU,GAAG,KAAnB;WAEOO,UAAU,KAAKb,SAAf,GACJU,GAAG,CAACD,SADA,GAEL,IAAIN,KAAJ,CAAUS,KAAK,CAACF,GAAD,EAAMa,KAAN,EAAaV,UAAb,EAAyBP,UAAzB,CAAf,CAFF;;;EAKDmB,IAAI,CAACZ,UAAD,EAAa;UACVH,GAAG,GAAGC,SAAS,CAAC,KAAKN,KAAN,CAArB;UACMoB,IAAI,GAAG;MAAElB,GAAG,EAAE,CAAP;MAAUiB,SAAS,EAAE,GAArB;MAA0Bf,SAAS,EAAE,CAArC;MAAwCH,UAAU,EAAE;KAAjE;UACMA,UAAU,GAAG,KAAnB;WAEOO,UAAU,KAAKb,SAAf,GACJU,GAAG,CAACD,SADA,GAEL,IAAIN,KAAJ,CAAUS,KAAK,CAACF,GAAD,EAAMe,IAAN,EAAYZ,UAAZ,EAAwBP,UAAxB,CAAf,CAFF;;;EAKDkB,SAAS,CAACA,SAAD,EAAY;UACdd,GAAG,GAAGC,SAAS,CAAC,KAAKN,KAAN,CAArB;WAEOmB,SAAS,KAAKxB,SAAd,GACJU,GAAG,CAACc,SADA,GAEL,IAAIrB,KAAJ,CAAUvM,MAAM,CAAC8M,GAAD,EAAM;MAAEc;KAAR,CAAhB,CAFF;;;EAKDE,KAAK,GAAG;WACAC,eAAe,CAAC,KAAKtB,KAAN,CAAtB;;;EAGDuB,KAAK,GAAG;WACAC,eAAe,CAAC,KAAKxB,KAAN,CAAtB;;;EAGDyB,QAAQ,GAAG;WACHC,kBAAkB,CAAC,KAAK1B,KAAN,CAAzB;;;EAGD2B,KAAK,GAAG;WACAC,eAAe,CAAC,KAAK5B,KAAN,CAAtB;;;EAGD6B,WAAW,GAAG;WACNC,qBAAqB,CAAC,KAAK9B,KAAN,CAA5B;;;EAGD+B,QAAQ,GAAG;WACHC,YAAY,CAAC,KAAKhC,KAAN,CAAnB;;;;;;;AAOF,SAASO,KAAT,CAAeE,IAAf,EAAqBT,KAArB,EAA4BQ,UAA5B,EAAwCP,UAAxC,EAAoDgC,eAApD,EAAqE;QAC9DC,QAAQ,GAAM1B,UAAU,GAAG,GAAjC;QACM2B,WAAW,GAAG,IAAID,QAAxB;;MAEIjC,UAAU,KAAK,KAAnB,EAA0B;uBACqCc,SAAS,CAACN,IAAD,CAD9C;UACZ2B,EADY,cACjBlC,GADiB;UACImC,EADJ,cACRpB,UADQ;UACmBqB,EADnB,cACQtB,SADR;UAC8BuB,EAD9B,cACuB1C,KADvB;;wBAEqCkB,SAAS,CAACf,KAAD,CAF9C;UAEZwC,EAFY,eAEjBtC,GAFiB;UAEIuC,EAFJ,eAERxB,UAFQ;UAEmByB,EAFnB,eAEQ1B,SAFR;UAE8B2B,EAF9B,eAEuB9C,KAFvB;;UAIlBK,GAJkB,GAKxBkC,EAAE,GAAGD,WAAL,GAAmBK,EAAE,GAAGN,QALA;UAIbjB,UAJa,GAMxBoB,EAAE,GAAGF,WAAL,GAAmBM,EAAE,GAAGP,QANA;UAIDlB,SAJC,GAOxBsB,EAAE,GAAGH,WAAL,GAAmBO,EAAE,GAAGR,QAPA;UAIUrC,KAJV,GAQxBoC,eAAe,GACZM,EAAE,GAAGJ,WAAL,GAAmBQ,EAAE,GAAGT,QADZ,GAEbK,EAVsB;WAalB;MAAErC,GAAF;MAAOe,UAAP;MAAmBD,SAAnB;MAA8BnB,KAA9B;MAAqCI,UAAU,EAAE;KAAxD;GAbD,MAcO,IAAIA,UAAU,KAAK,KAAnB,EAA0B;uBAC6BK,SAAS,CAACG,IAAD,CADtC;UACnB2B,EADmB,cACxBlC,GADwB;UACJ0C,EADI,cACfzB,SADe;UACW0B,EADX,cACAzC,SADA;UACsBmC,EADtB,cACe1C,KADf;;wBAE6BS,SAAS,CAACN,KAAD,CAFtC;UAEnBwC,EAFmB,eAExBtC,GAFwB;UAEJ4C,EAFI,eAEf3B,SAFe;UAEW4B,EAFX,eAEA3C,SAFA;UAEsBuC,EAFtB,eAEe9C,KAFf;;UAIzBK,GAJyB,GAK/BkC,EAAE,GAAGD,WAAL,GAAmBK,EAAE,GAAGN,QALO;UAIpBf,SAJoB,GAM/ByB,EAAE,GAAGT,WAAL,GAAmBW,EAAE,GAAGZ,QANO;UAIT9B,SAJS,GAO/ByC,EAAE,GAAGV,WAAL,GAAmBY,EAAE,GAAGb,QAPO;UAIErC,KAJF,GAQ/BoC,eAAe,GACZM,EAAE,GAAGJ,WAAL,GAAmBQ,EAAE,GAAGT,QADZ,GAEbK,EAV6B;WAazB;MAAErC,GAAF;MAAOiB,SAAP;MAAkBf,SAAlB;MAA6BP,KAA7B;MAAoCI,UAAU,EAAE;KAAvD;GAbM,MAcA;uBAC8CW,SAAS,CAACH,IAAD,CADvD;UACOuC,EADP,cACE/F,GADF;UACkBgG,EADlB,cACWlK,KADX;UAC4B8J,EAD5B,cACsB3M,IADtB;UACuCqM,EADvC,cACgC1C,KADhC;;wBAE8Ce,SAAS,CAACZ,KAAD,CAFvD;UAEOkD,EAFP,eAEEjG,GAFF;UAEkBkG,EAFlB,eAEWpK,KAFX;UAE4BgK,EAF5B,eAEsB7M,IAFtB;UAEuCyM,EAFvC,eAEgC9C,KAFhC;;UAIC5C,GAJD,GAKL+F,EAAE,GAAGb,WAAL,GAAmBe,EAAE,GAAGhB,QALnB;UAIMnJ,KAJN,GAMLkK,EAAE,GAAGd,WAAL,GAAmBgB,EAAE,GAAGjB,QANnB;UAIahM,IAJb,GAOL2M,EAAE,GAAGV,WAAL,GAAmBY,EAAE,GAAGb,QAPnB;UAImBrC,KAJnB,GAQLoC,eAAe,GACZM,EAAE,GAAGJ,WAAL,GAAmBQ,EAAE,GAAGT,QADZ,GAEbK,EAVG;WAaC;MAAEtF,GAAF;MAAOlE,KAAP;MAAc7C,IAAd;MAAoB2J,KAApB;MAA2BI,UAAU,EAAE;KAA9C;;;;;;;AAOF,SAAS1M,MAAT,CAAgBkN,IAAhB,EAAsB2C,QAAtB,EAAgC;QACzBpD,KAAK,GAAGpN,MAAM,CAACW,MAAP,CAAc,EAAd,EAAkBkN,IAAlB,CAAd;EAEA7N,MAAM,CAACyQ,IAAP,CAAYD,QAAZ,EAAsB7R,OAAtB,CACC+R,OAAO,IAAI;;UAEJC,KAAK,GAAGD,OAAO,KAAK,KAA1B;UACME,KAAK,GAAG,CAACD,KAAD,IAAUE,iBAAiB,CAAC/Q,IAAlB,CAAuB4Q,OAAvB,CAAxB,CAHU;;UAMJtR,KAAK,GAAG0R,SAAS,CAACN,QAAQ,CAACE,OAAD,CAAT,EAAoBA,OAApB,CAAvB,CANU;;IASVtD,KAAK,CAACsD,OAAD,CAAL,GAAiBtR,KAAjB;;QAEIwR,KAAJ,EAAW;;MAEVxD,KAAK,CAACE,GAAN,GAAYC,qBAAO,CAACH,KAAK,CAAC/C,GAAP,EAAY+C,KAAK,CAACjH,KAAlB,EAAyBiH,KAAK,CAAC9J,IAA/B,EAAqCuK,IAAI,CAACP,GAAL,IAAY,CAAjD,CAAnB;;GAdH;SAmBOF,KAAP;;;AAGD,SAAS0D,SAAT,CAAmB1R,KAAnB,EAA0BsR,OAA1B,EAAmC;;QAE5BC,KAAK,GAAGD,OAAO,KAAK,KAA1B,CAFkC;;QAK5BK,GAAG,GAAG,CAAZ;QACMC,GAAG,GAAGL,KAAK,GAAG,GAAH,GAAS,GAA1B;QAEMM,eAAe,GAAG3O,IAAI,CAACyO,GAAL,CAASzO,IAAI,CAAC0O,GAAL,CAASL,KAAK,GAC5CvR,KAAK,GAAG,GADoC,GAE7CA,KAF+B,EAExB2R,GAFwB,CAAT,EAETC,GAFS,CAAxB;SAIOC,eAAP;;;;;;AAMD,SAASjD,SAAT,CAAmBZ,KAAnB,EAA0B;eACIA,KAAK,CAACC,UAAN,KAAqB,KAArB,GAC1B6D,qBAAO,CAAC9D,KAAK,CAACE,GAAP,EAAYF,KAAK,CAACiB,UAAlB,EAA8BjB,KAAK,CAACgB,SAApC,CADmB,GAE3BhB,KAAK,CAACC,UAAN,KAAqB,KAArB,GACC8D,qBAAO,CAAC/D,KAAK,CAACE,GAAP,EAAYF,KAAK,CAACmB,SAAlB,EAA6BnB,KAAK,CAACI,SAAnC,CADR,GAEA,CAAEJ,KAAK,CAAC/C,GAAR,EAAa+C,KAAK,CAACjH,KAAnB,EAA0BiH,KAAK,CAAC9J,IAAhC,CALuB;;QACjB+G,GADiB;QACZlE,KADY;QACL7C,IADK;;SAOlB;IAAE+G,GAAF;IAAOlE,KAAP;IAAc7C,IAAd;IAAoBgK,GAAG,EAAEF,KAAK,CAACE,GAA/B;IAAoCL,KAAK,EAAEG,KAAK,CAACH,KAAjD;IAAwDI,UAAU,EAAE;GAA3E;;;AAGD,SAASc,SAAT,CAAmBf,KAAnB,EAA0B;gBACcA,KAAK,CAACC,UAAN,KAAqB,KAArB,GACpC+D,qBAAO,CAAChE,KAAK,CAAC/C,GAAP,EAAY+C,KAAK,CAACjH,KAAlB,EAAyBiH,KAAK,CAAC9J,IAA/B,EAAqC8J,KAAK,CAACE,GAA3C,CAD6B,GAErCF,KAAK,CAACC,UAAN,KAAqB,KAArB,GACCgE,qBAAO,CAACjE,KAAK,CAACE,GAAP,EAAYF,KAAK,CAACmB,SAAlB,EAA6BnB,KAAK,CAACI,SAAnC,CADR,GAEA,CAAEJ,KAAK,CAACE,GAAR,EAAaF,KAAK,CAACiB,UAAnB,EAA+BjB,KAAK,CAACgB,SAArC,CALuB;;QACjBd,GADiB;QACZe,UADY;QACAD,SADA;;SAOlB;IAAEd,GAAF;IAAOe,UAAP;IAAmBD,SAAnB;IAA8BnB,KAAK,EAAEG,KAAK,CAACH,KAA3C;IAAkDI,UAAU,EAAE;GAArE;;;AAGD,SAASK,SAAT,CAAmBN,KAAnB,EAA0B;gBACaA,KAAK,CAACC,UAAN,KAAqB,KAArB,GACnCiE,qBAAO,CAAClE,KAAK,CAAC/C,GAAP,EAAY+C,KAAK,CAACjH,KAAlB,EAAyBiH,KAAK,CAAC9J,IAA/B,EAAqC8J,KAAK,CAACE,GAA3C,CAD4B,GAEpCF,KAAK,CAACC,UAAN,KAAqB,KAArB,GACCkE,qBAAO,CAACnE,KAAK,CAACE,GAAP,EAAYF,KAAK,CAACiB,UAAlB,EAA8BjB,KAAK,CAACgB,SAApC,CADR,GAEA,CAAEhB,KAAK,CAACE,GAAR,EAAaF,KAAK,CAACmB,SAAnB,EAA8BnB,KAAK,CAACI,SAApC,CALuB;;QACjBF,GADiB;QACZiB,SADY;QACDf,SADC;;SAOlB;IAAEF,GAAF;IAAOiB,SAAP;IAAkBf,SAAlB;IAA6BP,KAAK,EAAEG,KAAK,CAACH,KAA1C;IAAiDI,UAAU,EAAE;GAApE;;;;;;AAMD,SAASY,QAAT,CAAkBb,KAAlB,EAAyBQ,UAAzB,EAAqC;;QAE9BH,GAAG,GAAGC,SAAS,CAACN,KAAD,CAArB;QACMW,GAAG,GAAGC,SAAS,CAACZ,KAAD,CAArB,CAHoC;;QAM9BoE,SAAS,GAAGC,aAAa,CAAC1D,GAAG,CAAC1D,GAAL,EAAU0D,GAAG,CAAC5H,KAAd,EAAqB4H,GAAG,CAACzK,IAAzB,CAA/B,CANoC;;QAS9BoO,gBAAgB,GAAGF,SAAS,GAAG,GAAZ;IAEtB;IAAElE,GAAG,EAAEG,GAAG,CAACH,GAAX;IAAgBiB,SAAS,EAAE,GAA3B;IAAgCf,SAAS,EAAE,CAA3C;IAA8CP,KAAK,EAAEQ,GAAG,CAACR,KAAzD;IAAgEI,UAAU,EAAE,KAA5E;;GAFsB,GAIvB;IAAEC,GAAG,EAAEG,GAAG,CAACH,GAAX;IAAgBiB,SAAS,EAAE,CAA3B;IAA8Bf,SAAS,EAAE,GAAzC;IAA8CP,KAAK,EAAEQ,GAAG,CAACR,KAAzD;IAAgEI,UAAU,EAAE;GAJ9E,CAToC;;QAgB9BsE,aAAa,GAAGC,eAAe,CAACxE,KAAD,EAAQsE,gBAAR,CAArC;QAEMG,gBAAgB,GAAGF,aAAa,GAAG,GAAhB;IAEtBG,yBAAyB,CAACrE,GAAD,EAAMiE,gBAAN,CAFH;IAIvBA,gBAJF,CAlBoC;;SAyB7B/D,KAAK,CAAC+D,gBAAD,EAAmBG,gBAAnB,EAAqCjE,UAArC,EAAiD,KAAjD,EAAwD,KAAxD,CAAZ;;;AAGD,SAASgE,eAAT,CAAyBG,MAAzB,EAAiCC,MAAjC,EAAyC;;QAElCC,IAAI,GAAGjE,SAAS,CAAC+D,MAAD,CAAtB;QACMG,IAAI,GAAGlE,SAAS,CAACgE,MAAD,CAAtB;QACMtC,EAAE,GAAG+B,aAAa,CAACQ,IAAI,CAAC5H,GAAN,EAAW4H,IAAI,CAAC9L,KAAhB,EAAuB8L,IAAI,CAAC3O,IAA5B,CAAxB;QACMwM,EAAE,GAAG2B,aAAa,CAACS,IAAI,CAAC7H,GAAN,EAAW6H,IAAI,CAAC/L,KAAhB,EAAuB+L,IAAI,CAAC5O,IAA5B,CAAxB;SAEOoM,EAAE,GAAGI,EAAL;IAEJ,CAACJ,EAAE,GAAG,IAAN,KAAeI,EAAE,GAAG,IAApB,CAFI;IAIL,CAACA,EAAE,GAAG,IAAN,KAAeJ,EAAE,GAAG,IAApB,CAJF;;;AAOD,SAAS+B,aAAT,CAAuBpH,GAAvB,EAA4BlE,KAA5B,EAAmC7C,IAAnC,EAAyC;gBACgB,CACvD6O,iBAAiB,CAAC9H,GAAD,CADsC,EAEvD8H,iBAAiB,CAAChM,KAAD,CAFsC,EAGvDgM,iBAAiB,CAAC7O,IAAD,CAHsC,CADhB;QAChC8O,YADgC;QAClBC,cADkB;QACFC,aADE;;QAQlCd,SAAS,GAAG,SAASY,YAAT,GAAwB,SAASC,cAAjC,GAAkD,SAASC,aAA7E;SAEOd,SAAP;;;AAGD,SAASW,iBAAT,CAA2B/S,KAA3B,EAAkC;;QAE3BoS,SAAS,GAAGpS,KAAK,IAAI,OAAT,GAAmBA,KAAK,GAAG,KAA3B,GAAmCkD,IAAI,CAACiQ,GAAL,CAAS,CAACnT,KAAK,GAAG,KAAT,IAAiB,KAA1B,EAAiC,GAAjC,CAArD;SAEOoS,SAAP;;;;AAID,SAASM,yBAAT,CAAmCrE,GAAnC,EAAwC+E,MAAxC,EAAgD;QACzCC,WAAW,GAAGzS,MAAM,CAACW,MAAP,CAAc,EAAd,EAAkB8M,GAAlB,CAApB,CAD+C;;MAI3CiF,IAAI,GAAGjF,GAAG,CAACc,SAAf;MACIoE,IAAI,GAAGlF,GAAG,CAACD,SAAf;MACIoF,IAAI,GAAGJ,MAAM,CAACjE,SAAlB;MACIsE,IAAI,GAAGL,MAAM,CAAChF,SAAlB,CAP+C;;SAUxClL,IAAI,CAACwQ,GAAL,CAASJ,IAAI,GAAGE,IAAhB,IAAwB,GAAxB,IAA+BtQ,IAAI,CAACwQ,GAAL,CAASH,IAAI,GAAGE,IAAhB,IAAwB,GAA9D,EAAmE;UAC5DE,IAAI,GAAGzQ,IAAI,CAAC0Q,KAAL,CAAW,CAACJ,IAAI,GAAGF,IAAR,IAAgB,CAA3B,CAAb;UACMO,IAAI,GAAG3Q,IAAI,CAAC0Q,KAAL,CAAW,CAACH,IAAI,GAAGF,IAAR,IAAgB,CAA3B,CAAb;IAEAF,WAAW,CAAClE,SAAZ,GAAwBwE,IAAxB;IACAN,WAAW,CAACjF,SAAZ,GAAwByF,IAAxB;;QAEIrB,eAAe,CAACa,WAAD,EAAchF,GAAd,CAAf,GAAoC,GAAxC,EAA6C;MAC5CmF,IAAI,GAAGG,IAAP;MACAF,IAAI,GAAGI,IAAP;KAFD,MAGO;MACNP,IAAI,GAAGK,IAAP;MACAJ,IAAI,GAAGM,IAAP;;;;SAIKR,WAAP;;;;;;AAMD,MAAM5B,iBAAiB,GAAG,qBAA1B;;;;AAKA,SAASzB,YAAT,CAAsBhC,KAAtB,EAA6B;SACrBA,KAAK,CAACC,UAAN,KAAqB,KAArB,GACJqB,eAAe,CAACtB,KAAD,CADX,GAELA,KAAK,CAACC,UAAN,KAAqB,KAArB,GACCuB,eAAe,CAACxB,KAAD,CADhB,GAEA4B,eAAe,CAAC5B,KAAD,CAJjB;;;AAOD,SAASsB,eAAT,CAAyBtB,KAAzB,EAAgC;QACzBc,GAAG,GAAUC,SAAS,CAACf,KAAD,CAA5B;QACM8F,QAAQ,GAAKhF,GAAG,CAACjB,KAAJ,KAAc,GAAjC;QACMK,GAAG,GAAUY,GAAG,CAACZ,GAAvB;QACMe,UAAU,GAAG/L,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACG,UAAJ,GAAiB,WAA5B,IAA2C,WAA9D;QACMD,SAAS,GAAI9L,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACE,SAAJ,GAAgB,WAA3B,IAA0C,WAA7D;QACMnB,KAAK,GAAQ3K,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACjB,KAAJ,GAAY,WAAvB,IAAsC,WAAzD;SAEQ,OAAMK,GAAI,IAAGe,UAAW,KAAID,SAAU,IAAG8E,QAAQ,GACtD,EADsD,GAEtD,MAAKjG,KAAM,GAAG,GAFjB;;;AAKD,SAAS2B,eAAT,CAAyBxB,KAAzB,EAAgC;QACzBK,GAAG,GAASC,SAAS,CAACN,KAAD,CAA3B;QACM8F,QAAQ,GAAIzF,GAAG,CAACR,KAAJ,KAAc,GAAhC;QACMK,GAAG,GAASG,GAAG,CAACH,GAAtB;QACMiB,SAAS,GAAGjM,IAAI,CAAC0Q,KAAL,CAAWvF,GAAG,CAACc,SAAJ,GAAgB,WAA3B,IAA0C,WAA5D;QACMf,SAAS,GAAGlL,IAAI,CAAC0Q,KAAL,CAAWvF,GAAG,CAACD,SAAJ,GAAgB,WAA3B,IAA0C,WAA5D;QACMP,KAAK,GAAO3K,IAAI,CAAC0Q,KAAL,CAAWvF,GAAG,CAACR,KAAJ,GAAY,WAAvB,IAAsC,WAAxD;SAEQ,OAAMK,GAAI,IAAGiB,SAAU,KAAIf,SAAU,IAAG0F,QAAQ,GACrD,EADqD,GAErD,MAAKjG,KAAM,GAAG,GAFjB;;;AAKD,SAAS+B,eAAT,CAAyB5B,KAAzB,EAAgC;QACzBW,GAAG,GAAQC,SAAS,CAACZ,KAAD,CAA1B;QACM8F,QAAQ,GAAGnF,GAAG,CAACd,KAAJ,KAAc,GAA/B;QACM5C,GAAG,GAAQ/H,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAAC1D,GAAJ,GAAU,WAArB,IAAoC,WAArD;QACMlE,KAAK,GAAM7D,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAAC5H,KAAJ,GAAY,WAAvB,IAAsC,WAAvD;QACM7C,IAAI,GAAOhB,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAACzK,IAAJ,GAAW,WAAtB,IAAqC,WAAtD;QACM2J,KAAK,GAAM3K,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAACd,KAAJ,GAAY,WAAvB,IAAsC,WAAvD;SAEQ,OAAM5C,GAAI,KAAIlE,KAAM,KAAI7C,IAAK,IAAG4P,QAAQ,GAC7C,EAD6C,GAE7C,MAAKjG,KAAM,GAAG,GAFjB;;;AAKD,SAAS6B,kBAAT,CAA4B1B,KAA5B,EAAmC;SAC3BA,KAAK,CAACC,UAAN,KAAqB,KAArB,GACJ8F,qBAAqB,CAAC/F,KAAD,CADjB,GAEL8B,qBAAqB,CAAC9B,KAAD,CAFvB;;;AAKD,SAAS8B,qBAAT,CAA+B9B,KAA/B,EAAsC;QAC/BW,GAAG,GAAQC,SAAS,CAACZ,KAAD,CAA1B;QACM8F,QAAQ,GAAGnF,GAAG,CAACd,KAAJ,KAAc,GAA/B;QACMtK,IAAI,GAAOuQ,QAAQ,GAAG,KAAH,GAAW,MAApC;QACM7I,GAAG,GAAQ/H,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAAC1D,GAAJ,GAAU,GAAV,GAAgB,GAA3B,CAAjB;QACMlE,KAAK,GAAM7D,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAAC5H,KAAJ,GAAY,GAAZ,GAAkB,GAA7B,CAAjB;QACM7C,IAAI,GAAOhB,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAACzK,IAAJ,GAAW,GAAX,GAAiB,GAA5B,CAAjB;QACM2J,KAAK,GAAM3K,IAAI,CAAC0Q,KAAL,CAAWjF,GAAG,CAACd,KAAJ,GAAY,GAAZ,GAAkB,WAA7B,IAA4C,WAA7D;SAEQ,GAAEtK,IAAK,IAAG0H,GAAI,KAAIlE,KAAM,KAAI7C,IAAK,GAAE4P,QAAQ,GAChD,EADgD,GAEhD,KAAIjG,KAAM,EAAE,GAFf;;;AAKD,SAASkG,qBAAT,CAA+B/F,KAA/B,EAAsC;QAC/Bc,GAAG,GAAUC,SAAS,CAACf,KAAD,CAA5B;QACM8F,QAAQ,GAAKhF,GAAG,CAACjB,KAAJ,KAAc,GAAjC;QACMtK,IAAI,GAASuQ,QAAQ,GAAG,KAAH,GAAW,MAAtC;QACM5F,GAAG,GAAUY,GAAG,CAACZ,GAAvB;QACMe,UAAU,GAAG/L,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACG,UAAJ,GAAiB,WAA5B,IAA2C,WAA9D;QACMD,SAAS,GAAI9L,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACE,SAAJ,GAAgB,WAA3B,IAA0C,WAA7D;QACMnB,KAAK,GAAQ3K,IAAI,CAAC0Q,KAAL,CAAW9E,GAAG,CAACjB,KAAJ,GAAY,GAAZ,GAAkB,WAA7B,IAA4C,WAA/D;SAEQ,GAAEtK,IAAK,IAAG2K,GAAI,KAAIe,UAAW,MAAKD,SAAU,IAAG8E,QAAQ,GAC5D,EAD4D,GAE5D,KAAIjG,KAAM,EAAE,GAFf;;;ACrdc,SAASmG,gBAAT,CAA0BxT,IAA1B,EAAgCtB,IAAhC,EAAsC+U,IAAtC,EAA4CC,OAA5C,EAAqD;MAC/D,WAAWhV,IAAI,CAACiV,UAApB,EAAgC;IAC/BjV,IAAI,CAACU,IAAL,CAAUwU,IAAV,CAAelV,IAAI,CAACwD,MAApB,EAA4BwR,OAA5B,EAAqC;MAAED;KAAvC;GADD,MAEO,IAAI,aAAa/U,IAAI,CAACiV,UAAtB,EAAkC;UAClCjV,IAAI,CAACU,IAAL,CAAU6C,KAAV,CAAgByR,OAAhB,EAAyB;MAAED;KAA3B,CAAN;;;;ACEF;;;AAGA,AAAe,SAASI,YAAT,CAAsB7T,IAAtB,EAA4BtB,IAA5B,EAAkC;EAChDsB,IAAI,CAACnB,KAAL,CAAWC,KAAX,CAAiB,CAAjB,EAAoBC,OAApB,CAA4B+U,KAAK,IAAI;QAChCC,kBAAkB,CAACD,KAAD,CAAtB,EAA+B;;UAE1BpV,IAAI,CAACsV,aAAT,EAAwB;QACvBC,kBAAkB,CAACH,KAAD,EAAQpV,IAAR,CAAlB;OAH6B;;;YAOxB8O,KAAK,GAAG0G,yBAAyB,CAACJ,KAAD,EAAQpV,IAAR,CAAvC;;UAEI8O,KAAJ,EAAW;;QAEVsG,KAAK,CAACK,WAAN,CAAkBC,WAAM,CAACX,IAAP,CAAY;UAC7BY,IAAI,EAAEP,KAAK,CAACO,IADiB;UAE7B7U,KAAK,EAAEd,IAAI,CAAC4V,WAAL,CAAiB9G,KAAjB;SAFU,CAAlB;;KAXF,MAgBO,IAAIsG,KAAK,CAACjV,KAAN,IAAeuB,MAAM,CAAC0T,KAAK,CAACjV,KAAP,CAAN,CAAoBwB,MAAvC,EAA+C;MACrDwT,YAAY,CAACC,KAAD,EAAQpV,IAAR,CAAZ;;GAlBF;;;;;AA0BD,SAASuV,kBAAT,CAA4BjU,IAA5B,EAAkCtB,IAAlC,EAAwC;EACvC6V,IAAI,CAACvU,IAAD,EAAO8T,KAAK,IAAI;QACfU,UAAU,CAACV,KAAD,CAAd,EAAuB;;oCAEOW,qBAAqB,CAACX,KAAD,EAAQ;OAExDY,aAAD,EAAgBC,OAAhB,EAAyBC,aAAzB,CAFyD,CAAR,CAF5B;;YAEftV,IAFe;YAETuV,YAFS;;;UAQlBvV,IAAI,IAAIZ,IAAI,CAACoC,gBAAjB,EAAmC;YAC9BgU,mBAAmB,GAAGpW,IAAI,CAACoC,gBAAL,CAAsBxB,IAAtB,CAA1B,CADkC;;YAI9ByV,aAAa,CAAC7U,IAAd,CAAmB4U,mBAAnB,CAAJ,EAA6C;gBACtCE,YAAY,GAAGF,mBAAmB,CAACG,KAApB,EAArB;UAEAhB,kBAAkB,CAACe,YAAD,EAAetW,IAAf,CAAlB;UAEAoW,mBAAmB,GAAGE,YAAtB;SATiC;;;YAa9BF,mBAAmB,CAACjW,KAApB,CAA0BwB,MAA1B,KAAqC,CAArC,IAA0CyU,mBAAmB,CAACjW,KAApB,CAA0B,CAA1B,EAA6BA,KAA7B,CAAmCwB,MAAjF,EAAyF;UACxFyU,mBAAmB,CAACjW,KAApB,CAA0B,CAA1B,EAA6BA,KAA7B,CAAmCE,OAAnC,CAA2CmW,mBAAmB,IAAI;YACjEpB,KAAK,CAACqB,MAAN,CAAaC,YAAb,CAA0BtB,KAA1B,EAAiCoB,mBAAjC;WADD;;;QAKDpB,KAAK,CAACnU,MAAN;OAnBD,MAoBO,IAAIkV,YAAY,IAAIA,YAAY,CAAChW,KAAb,CAAmBwB,MAAnB,KAA8B,CAA9C,IAAmDwU,YAAY,CAAChW,KAAb,CAAmB,CAAnB,EAAsBA,KAAtB,CAA4BwB,MAAnF,EAA2F;;QAEjG4T,kBAAkB,CAACY,YAAD,EAAenW,IAAf,CAAlB;QAEAoV,KAAK,CAACK,WAAN,CAAkB,GAAGU,YAAY,CAAChW,KAAb,CAAmB,CAAnB,EAAsBA,KAAtB,CAA4B,CAA5B,CAArB;;;GAjCC,CAAJ;;;;;;AA0CD,SAASwW,cAAT,CAAwBrV,IAAxB,EAA8BtB,IAA9B,EAAoC;MAC/B4W,aAAa,CAACtV,IAAD,CAAjB,EAAyB;WACjBuV,oBAAoB,CAACvV,IAAD,EAAOtB,IAAP,CAA3B;GADD,MAEO,IAAI8W,aAAa,CAACxV,IAAD,CAAjB,EAAyB;WACxByV,oBAAoB,CAACzV,IAAD,EAAOtB,IAAP,CAA3B;GADM,MAEA,IAAIgX,aAAa,CAAC1V,IAAD,CAAjB,EAAyB;WACxB2V,oBAAoB,CAAC3V,IAAD,EAAOtB,IAAP,CAA3B;GADM,MAEA,IAAIqV,kBAAkB,CAAC/T,IAAD,CAAtB,EAA8B;WAC7BkU,yBAAyB,CAAClU,IAAD,EAAOtB,IAAP,CAAhC;GADM,MAEA,IAAIkX,UAAU,CAAC5V,IAAD,CAAd,EAAsB;WACrB6V,iBAAiB,CAAC7V,IAAD,EAAOtB,IAAP,CAAxB;GADM,MAEA,IAAIoX,YAAY,CAAC9V,IAAD,CAAhB,EAAwB;WACvB+V,mBAAmB,CAAC/V,IAAD,EAAOtB,IAAP,CAA1B;GADM,MAEA;WACC8U,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,kBAA1B,CAAvB;;;;;AAKF,SAAS+V,oBAAT,CAA8BvV,IAA9B,EAAoCtB,IAApC,EAA0C;iCACD+V,qBAAqB,CAACzU,IAAD,EAAO;GAElEgW,mBAAD,EAAsBA,mBAAtB,EAA2CA,mBAA3C,EAAgEC,OAAhE,EAAyEC,cAAzE,CAFmE;GAIlEC,kBAAD,EAAqBA,kBAArB,EAAyCA,kBAAzC,EAA6DF,OAA7D,EAAsEC,cAAtE,CAJmE;GAMlEF,mBAAD,EAAsBrB,OAAtB,EAA+BqB,mBAA/B,EAAoDrB,OAApD,EAA6DqB,mBAA7D,EAAkFrB,OAAlF,EAA2FuB,cAA3F,CANmE;GAQlEC,kBAAD,EAAqBxB,OAArB,EAA8BwB,kBAA9B,EAAkDxB,OAAlD,EAA2DwB,kBAA3D,EAA+ExB,OAA/E,EAAwFuB,cAAxF,CARmE,CAAP,CADpB;;QAClCzL,GADkC;QAC7BlE,KAD6B;QACtB7C,IADsB;;QAChB2J,KADgB,uCACR,GADQ;;MAYrC5C,GAAG,KAAK0C,SAAZ,EAAuB;UAChBK,KAAK,GAAG,IAAIF,KAAJ,CAAU;MAAE7C,GAAF;MAAOlE,KAAP;MAAc7C,IAAd;MAAoB2J,KAApB;MAA2BI,UAAU,EAAE;KAAjD,CAAd;WAEOD,KAAP;GAHD,MAIO;WACCgG,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,iCAA1B,CAAvB;;;;;AAKF,SAASiW,oBAAT,CAA8BzV,IAA9B,EAAoCtB,IAApC,EAA0C;iCACS+V,qBAAqB,CAACzU,IAAD,EAAO;GAE5EoW,YAAD,EAAeJ,mBAAf,EAAoCA,mBAApC,EAAyDC,OAAzD,EAAkEC,cAAlE,CAF6E;GAI5EE,YAAD,EAAezB,OAAf,EAAwBqB,mBAAxB,EAA6CrB,OAA7C,EAAsDqB,mBAAtD,EAA2ErB,OAA3E,EAAoFuB,cAApF,CAJ6E,CAAP,CAD9B;;QAClCxI,GADkC;QAC7Be,UAD6B;QACjBD,SADiB;;QACNnB,KADM,uCACE,GADF;;MAQrCmB,SAAS,KAAKrB,SAAlB,EAA6B;UACtBK,KAAK,GAAG,IAAIF,KAAJ,CAAU;MAAEI,GAAF;MAAOe,UAAP;MAAmBD,SAAnB;MAA8BnB,KAA9B;MAAqCI,UAAU,EAAE;KAA3D,CAAd;WAEOD,KAAP;GAHD,MAIO;WACCgG,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,iCAA1B,CAAvB;;;;;AAKF,SAASmW,oBAAT,CAA8B3V,IAA9B,EAAoCtB,IAApC,EAA0C;iCACQ+V,qBAAqB,CAACzU,IAAD,EAAO;GAE3EoW,YAAD,EAAeJ,mBAAf,EAAoCA,mBAApC,EAAyDC,OAAzD,EAAkEC,cAAlE,CAF4E,CAAP,CAD7B;;QAClCxI,GADkC;QAC7BiB,SAD6B;QAClBf,SADkB;;QACPP,KADO,wCACC,GADD;;MAMrCO,SAAS,KAAKT,SAAlB,EAA6B;UACtBK,KAAK,GAAG,IAAIF,KAAJ,CAAU;MAAEI,GAAF;MAAOiB,SAAP;MAAkBf,SAAlB;MAA6BP,KAA7B;MAAoCI,UAAU,EAAE;KAA1D,CAAd;WAEOD,KAAP;GAHD,MAIO;WACCgG,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,iCAA1B,CAAvB;;;;;AAKF,SAAS0U,yBAAT,CAAmClU,IAAnC,EAAyCtB,IAAzC,EAA+C;;eAEH,CAACsB,IAAI,CAACnB,KAAL,IAAc,EAAf,EAAmBC,KAAnB,CAAyB,CAAzB,EAA4B,CAAC,CAA7B,KAAmC,EAFhC;;QAEvCuX,cAFuC;QAEpBC,aAFoB;;MAI1CD,cAAc,KAAKlJ,SAAvB,EAAkC;UAC3BK,KAAK,GAAGuD,KAAK,CAACsF,cAAD,CAAL,GACX,IAAI/I,KAAJ,CAAU;MACXI,GAAG,EAAE0I,YAAY,CAACC,cAAD,EAAiB3X,IAAjB,CADN;MAEX+P,UAAU,EAAE,GAFD;MAGXD,SAAS,EAAE,EAHA;MAIXnB,KAAK,EAAE,GAJI;MAKXI,UAAU,EAAE;KALX,CADW,GAQZ4H,cAAc,CAACgB,cAAD,EAAiB3X,IAAjB,CARhB;;QAUI8O,KAAJ,EAAW;YACJ+I,aAAa,GAAGC,yBAAyB,CAAChJ,KAAD,EAAQ8I,aAAR,EAAuB5X,IAAvB,CAA/C;aAEO6X,aAAP;KAHD,MAIO;aACC/C,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,wBAA1B,CAAvB;;GAhBF,MAkBO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,uCAA1B,CAAvB;;;;;AAKF,SAASqW,iBAAT,CAA2B7V,IAA3B,EAAiCtB,IAAjC,EAAuC;MAClCgO,eAAa,CAACxM,IAAd,CAAmBF,IAAI,CAACR,KAAxB,CAAJ,EAAoC;;2BAED+M,aAAa,CAACvM,IAAI,CAACR,KAAN,CAFZ;;UAE5BiL,GAF4B;UAEvBlE,KAFuB;UAEhB7C,IAFgB;UAEV2J,KAFU;;UAI7BG,KAAK,GAAG,IAAIF,KAAJ,CAAU;MAAE7C,GAAF;MAAOlE,KAAP;MAAc7C,IAAd;MAAoB2J;KAA9B,CAAd;WAEOG,KAAP;GAND,MAOO;WACCgG,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,4BAA1B,CAAvB;;;;;AAKF,SAASuW,mBAAT,CAA6B/V,IAA7B,EAAmCtB,IAAnC,EAAyC;MACpCoX,YAAY,CAAC9V,IAAD,CAAhB,EAAwB;;2BAEI8C,aAAa,CAAC9C,IAAI,CAACR,KAAN,CAFjB;;UAEhBiL,GAFgB;UAEXlE,KAFW;UAEJ7C,IAFI;;UAIjB8J,KAAK,GAAG,IAAIF,KAAJ,CAAU;MAAE7C,GAAF;MAAOlE,KAAP;MAAc7C,IAAd;MAAoB2J,KAAK,EAAE,GAA3B;MAAgCI,UAAU,EAAE;KAAtD,CAAd;WAEOD,KAAP;GAND,MAOO;WACCgG,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,8BAA1B,CAAvB;;;;;;;;AAQF,SAASgX,yBAAT,CAAmChJ,KAAnC,EAA0C8I,aAA1C,EAAyD5X,IAAzD,EAA+D;QACxD6X,aAAa,GAAGD,aAAa,CAACxU,MAAd,CAAqB,CAACmM,IAAD,EAAOjO,IAAP,KAAgB;QACtDyW,2BAA2B,CAACzW,IAAD,CAA/B,EAAuC;aAC/B0W,kCAAkC,CAACzI,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAAzC;KADD,MAEO,IAAIiY,aAAa,CAAC3W,IAAD,CAAjB,EAAyB;aACxB4W,oBAAoB,CAAC3I,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAA3B;KADM,MAEA,IAAImY,aAAa,CAAC7W,IAAD,CAAjB,EAAyB;aACxB8W,oBAAoB,CAAC7I,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAA3B;KADM,MAEA,IAAIqY,+CAA+C,CAAC/W,IAAD,CAAnD,EAA2D;aAC1DgX,sDAAsD,CAAC/I,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAA7D;KADM,MAEA,IAAIuY,mBAAmB,CAACjX,IAAD,CAAvB,EAA+B;aAC9BkX,0BAA0B,CAACjJ,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAAjC;KADM,MAEA,IAAIyY,eAAe,CAACnX,IAAD,CAAnB,EAA2B;aAC1BoX,sBAAsB,CAACnJ,IAAD,EAAOjO,IAAP,EAAaA,IAAI,CAACR,KAAL,KAAe,QAA5B,EAAsCd,IAAtC,CAA7B;KADM,MAEA,IAAI2Y,kBAAkB,CAACrX,IAAD,CAAtB,EAA8B;aAC7BsX,yBAAyB,CAACrJ,IAAD,EAAOjO,IAAP,EAAatB,IAAb,CAAhC;KADM,MAEA;MACN8U,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,iCAA1B,CAAhB;aAEOyO,IAAP;;GAlBoB,EAoBnBT,KApBmB,CAAtB;SAsBO+I,aAAP;;;;AAID,SAASG,kCAAT,CAA4CzI,IAA5C,EAAkDjO,IAAlD,EAAwDtB,IAAxD,EAA8D;kCACvB+V,qBAAqB,CAACzU,IAAD,EAAOuX,UAAU,CAACrX,IAAX,CAAgBF,IAAI,CAACR,KAArB;IAE9D;GAEAgY,0BAAD,EAA6BtB,cAA7B,CAFC;GAIAuB,sBAAD,EAAyBzB,mBAAzB,CAJC;GAMAE,cAAD,CANC,CAF8D;IAW/D;GAEAsB,0BAAD,EAA6BxB,mBAA7B,CAFC;GAIAwB,0BAAD,EAA6BrB,kBAA7B,CAJC;GAMAsB,sBAAD,EAAyBzB,mBAAzB,CANC;GAQAA,mBAAD,CARC;GAUAG,kBAAD,CAVC,CAXwD,CADE;;QACtDuB,eADsD;QACrCC,UADqC;;MA0BzDD,eAAe,KAAKvK,SAAxB,EAAmC;;UAE5B2D,OAAO,GAAG9Q,IAAI,CAACR,KAAL,CAAWqC,WAAX,GAAyB+V,OAAzB,CAAiCL,UAAjC,EAA6C,OAA7C,CAAhB;UAEMM,aAAa,GAAG5J,IAAI,CAAC6C,OAAD,CAAJ,EAAtB;UAEMgH,aAAa,GAAGH,UAAU,KAAKxK,SAAf,GACnBuK,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAD,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAD,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAI,MAAM,CAACJ,UAAD,CAPa,GAQpBI,MAAM,CAACL,eAAD,CARR;UAUMM,aAAa,GAAG/J,IAAI,CAAC6C,OAAD,CAAJ,CAAcgH,aAAd,CAAtB;WAEOE,aAAP;GAlBD,MAmBO;WACCxE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,6BAA1B,CAAvB;;;;;AAKF,SAASoX,oBAAT,CAA8B3I,IAA9B,EAAoCjO,IAApC,EAA0CtB,IAA1C,EAAgD;kCACd+V,qBAAqB,CAACzU,IAAD,EAAO;GAE1DwX,0BAAD,EAA6BxB,mBAA7B,EAAkDA,mBAAlD,EAAuEA,mBAAvE,CAF2D;GAI1DwB,0BAAD,EAA6BrB,kBAA7B,EAAiDA,kBAAjD,EAAqEA,kBAArE,CAJ2D;GAM1DqB,0BAAD,EAA6B3B,iBAA7B,CAN2D;GAQ1D4B,sBAAD,EAAyBzB,mBAAzB,CAR2D,CAAP,CADP;;QACxCiC,IADwC;QAClCC,IADkC;QAC5BC,IAD4B;QACtBC,IADsB;;MAa3CF,IAAI,KAAK/K,SAAT,IAAsB+K,IAAI,CAAC1K,KAA/B,EAAsC;UAC/BwK,aAAa,GAAG/J,IAAI,CAACE,GAAL,CACrB8J,IAAI,KAAK,GAAT,GACGhK,IAAI,CAACxD,GAAL,KAAayN,IAAI,CAACzN,GAAL,EADhB,GAEEwD,IAAI,CAACxD,GAAL,KAAayN,IAAI,CAACzN,GAAL,EAHM,EAIrBwN,IAAI,KAAK,GAAT,GACGhK,IAAI,CAAC1H,KAAL,KAAe2R,IAAI,CAAC3R,KAAL,EADlB,GAEE0H,IAAI,CAAC1H,KAAL,KAAe2R,IAAI,CAAC3R,KAAL,EANI,EAOrB0R,IAAI,KAAK,GAAT,GACGhK,IAAI,CAACvK,IAAL,KAAcwU,IAAI,CAACxU,IAAL,EADjB,GAEEuK,IAAI,CAACvK,IAAL,KAAcwU,IAAI,CAACxU,IAAL,EATK,CAAtB;WAYOsU,aAAP;GAbD,MAcO,IAAIC,IAAI,KAAK9K,SAAT,IAAsBkL,cAAc,CAACnY,IAAf,CAAoB+X,IAApB,CAA1B,EAAqD;UACrDD,aAAa,GAAG/J,IAAI,CAACE,GAAL,CACrB8J,IAAI,KAAK,GAAT,GACGhK,IAAI,CAACxD,GAAL,KAAayN,IADhB,GAEEjK,IAAI,CAACxD,GAAL,KAAayN,IAHM,EAIrBD,IAAI,KAAK,GAAT,GACGhK,IAAI,CAAC1H,KAAL,KAAe4R,IADlB,GAEElK,IAAI,CAAC1H,KAAL,KAAe4R,IANI,EAOrBF,IAAI,KAAK,GAAT,GACGhK,IAAI,CAACvK,IAAL,KAAc0U,IADjB,GAEEnK,IAAI,CAACvK,IAAL,KAAc0U,IATK,CAAtB;WAYOJ,aAAP;GAbM,MAcA,IAAIC,IAAI,KAAK9K,SAAT,IAAsB+K,IAAI,KAAK/K,SAAnC,EAA8C;UAC9C6K,aAAa,GAAG/J,IAAI,CAACE,GAAL,CACrBF,IAAI,CAACxD,GAAL,KAAayN,IADQ,EAErBjK,IAAI,CAAC1H,KAAL,KAAe2R,IAFM,EAGrBjK,IAAI,CAACvK,IAAL,KAAcwU,IAHO,CAAtB;WAMOF,aAAP;GAPM,MAQA;WACCxE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,iCAA1B,CAAvB;;;;;AAKF,SAAS4X,sBAAT,CAAgCnJ,IAAhC,EAAsCjO,IAAtC,EAA4CsY,YAA5C,EAA0D5Z,IAA1D,EAAgE;kCACf+V,qBAAqB,CAACzU,IAAD,EAAO,CAC3E,CAACqV,cAAD,EAAiBW,mBAAjB,EAAsCuC,mBAAtC,CAD2E,CAAP,CADN;;QACxD/K,KADwD;QACjDQ,UADiD;;QACrCP,UADqC,wCACxB,KADwB;;MAK3DO,UAAU,KAAKb,SAAnB,EAA8B;UACvB6K,aAAa,GAAGM,YAAY,GAC/BrK,IAAI,CAACC,MAAL,CAAYV,KAAK,CAACA,KAAlB,EAAyBQ,UAAzB,EAAqCP,UAArC,CAD+B,GAEhCQ,IAAI,CAACF,KAAL,CAAWP,KAAK,CAACA,KAAjB,EAAwBQ,UAAxB,EAAoCP,UAApC,CAFF;WAIOuK,aAAP;GALD,MAMO;WACCxE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,oCAA1B,CAAvB;;;;;AAKF,SAAS8X,yBAAT,CAAmCrJ,IAAnC,EAAyCjO,IAAzC,EAA+CtB,IAA/C,EAAqD;kCAC/B+V,qBAAqB,CAACzU,IAAD,EAAO;GAE/CgW,mBAAD,CAFgD,CAAP,CADU;;QAC7ChI,UAD6C;;MAMhDA,UAAU,KAAKb,SAAnB,EAA8B;UACvB6K,aAAa,GAAG/J,IAAI,CAACI,QAAL,CAAcL,UAAd,CAAtB;WAEOgK,aAAP;GAHD,MAIO;WACCxE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,uCAA1B,CAAvB;;;;;AAKF,SAASsX,oBAAT,CAA8B7I,IAA9B,EAAoCjO,IAApC,EAA0CtB,IAA1C,EAAgD;kCACX+V,qBAAqB,CAACzU,IAAD,EAAO;GAE9DwY,+BAAD,EAAkCpC,YAAlC,CAF+D;GAI9DA,YAAD,CAJ+D,CAAP,CADV;;QACxCqC,aADwC;QACzBd,UADyB;;MAQ3Cc,aAAa,KAAKtL,SAAtB,EAAiC;UAC1BuL,WAAW,GAAGzK,IAAI,CAACP,GAAL,EAApB;UAEMoK,aAAa,GAAGH,UAAU,KAAKxK,SAAf,GACnBsL,aAAa,KAAK,GAAlB,GACCC,WAAW,GAAGX,MAAM,CAACJ,UAAD,CADrB,GAEAc,aAAa,KAAK,GAAlB,GACCC,WAAW,GAAGX,MAAM,CAACJ,UAAD,CADrB,GAEAc,aAAa,KAAK,GAAlB,GACCC,WAAW,GAAGX,MAAM,CAACJ,UAAD,CADrB,GAEAI,MAAM,CAACJ,UAAD,CAPa,GAQpBI,MAAM,CAACU,aAAD,CARR;WAUOxK,IAAI,CAACP,GAAL,CAASoK,aAAT,CAAP;GAbD,MAcO;WACCtE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,kCAA1B,CAAvB;;;;;AAKF,SAASwX,sDAAT,CAAgE/I,IAAhE,EAAsEjO,IAAtE,EAA4EtB,IAA5E,EAAkF;QAC3EoS,OAAO,GAAG9Q,IAAI,CAACR,KAAL,CAAWqC,WAAX,GAAyB+V,OAAzB,CAAiC,KAAjC,EAAwC,WAAxC,EAAqDA,OAArD,CAA6D,KAA7D,EAAoE,WAApE,EAAiFA,OAAjF,CAAyF,KAAzF,EAAgG,YAAhG,EAA8GA,OAA9G,CAAsH,KAAtH,EAA6H,WAA7H,CAAhB;;kCACsCnD,qBAAqB,CAACzU,IAAD,EAAO,CACjE,CAACwY,+BAAD,EAAkCxC,mBAAlC,CADiE,EAEjE,CAACA,mBAAD,CAFiE,CAAP,CAFsB;;QAE1E0B,eAF0E;QAEzDC,UAFyD;;MAO7ED,eAAe,KAAKvK,SAAxB,EAAmC;UAC5B0K,aAAa,GAAG5J,IAAI,CAAC6C,OAAD,CAAJ,EAAtB;UAEMgH,aAAa,GAAGH,UAAU,KAAKxK,SAAf,GACnBuK,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAD,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAD,eAAe,KAAK,GAApB,GACCG,aAAa,GAAGE,MAAM,CAACJ,UAAD,CADvB,GAEAI,MAAM,CAACJ,UAAD,CAPa,GAQpBI,MAAM,CAACL,eAAD,CARR;WAUOzJ,IAAI,CAAC6C,OAAD,CAAJ,CAAcgH,aAAd,CAAP;GAbD,MAcO;WACCtE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,oBAAmBsR,OAAQ,cAArD,CAAvB;;;;;AAKF,SAASoG,0BAAT,CAAoCjJ,IAApC,EAA0CjO,IAA1C,EAAgDtB,IAAhD,EAAsD;QAC/CoS,OAAO,GAAG9Q,IAAI,CAACR,KAAL,CAAWqC,WAAX,EAAhB;;kCACqB4S,qBAAqB,CAACzU,IAAD,EAAO;GAE/CgW,mBAAD,CAFgD,CAAP,CAFW;;QAE9ChI,UAF8C;;MAOjDA,UAAU,KAAKb,SAAnB,EAA8B;UACvB2K,aAAa,GAAGC,MAAM,CAAC/J,UAAD,CAA5B;WAEOC,IAAI,CAAC6C,OAAD,CAAJ,CAAcgH,aAAd,CAAP;GAHD,MAIO;WACCtE,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,kBAAiBsR,OAAQ,cAAnD,CAAvB;;;;;;;;AAQF,SAASyH,mBAAT,CAA6BvY,IAA7B,EAAmCtB,IAAnC,EAAyC;MACpCia,YAAY,CAAC3Y,IAAD,CAAhB,EAAwB;;WAEhBA,IAAI,CAACR,KAAZ;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,+BAA1B,CAAvB;;;;;AAKF,SAAS0W,cAAT,CAAwBlW,IAAxB,EAA8BtB,IAA9B,EAAoC;MAC/Bka,QAAQ,CAAC5Y,IAAD,CAAZ,EAAoB;;WAEZA,IAAI,CAACR,KAAL,GAAa,GAApB;GAFD,MAGO,IAAIqZ,YAAY,CAAC7Y,IAAD,CAAhB,EAAwB;;WAEvBgW,mBAAmB,CAAChW,IAAD,EAAOtB,IAAP,CAA1B;GAFM,MAGA;WACC8U,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,+BAA1B,CAAvB;;;;;AAKF,SAAS2W,kBAAT,CAA4BnW,IAA5B,EAAkCtB,IAAlC,EAAwC;MACnCka,QAAQ,CAAC5Y,IAAD,CAAZ,EAAoB;;WAEZA,IAAI,CAACR,KAAL,GAAa,IAApB;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,6BAA1B,CAAvB;;;;;AAKF,SAAS4W,YAAT,CAAsBpW,IAAtB,EAA4BtB,IAA5B,EAAkC;MAC7BqS,KAAK,CAAC/Q,IAAD,CAAT,EAAiB;;UAEV8Y,IAAI,GAAG9Y,IAAI,CAAC8Y,IAAL,CAAUjX,WAAV,EAAb;;QAEIiX,IAAI,KAAK,MAAb,EAAqB;;aAEbxW,WAAW,CAACtC,IAAI,CAACR,KAAN,CAAlB;KAFD,MAGO,IAAIsZ,IAAI,KAAK,KAAb,EAAoB;;aAEnBtW,WAAW,CAACxC,IAAI,CAACR,KAAN,CAAlB;KAFM,MAGA,IAAIsZ,IAAI,KAAK,MAAb,EAAqB;;aAEpBlW,WAAW,CAAC5C,IAAI,CAACR,KAAN,CAAlB;KAFM,MAGA;;aAEC4C,WAAW,CAACpC,IAAI,CAACR,KAAN,CAAlB;;GAfF,MAiBO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,sBAA1B,CAAvB;;;;;AAKF,SAASwW,mBAAT,CAA6BhW,IAA7B,EAAmCtB,IAAnC,EAAyC;MACpCma,YAAY,CAAC7Y,IAAD,CAAhB,EAAwB;;WAEhB+X,MAAM,CAAC/X,IAAI,CAACR,KAAN,CAAb;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,sBAA1B,CAAvB;;;;;AAKF,SAASgY,0BAAT,CAAoCxX,IAApC,EAA0CtB,IAA1C,EAAgD;MAC3Cqa,mBAAmB,CAAC/Y,IAAD,CAAvB,EAA+B;;WAEvBA,IAAI,CAACR,KAAZ;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,mCAA1B,CAAvB;;;;;AAKF,SAASiY,sBAAT,CAAgCzX,IAAhC,EAAsCtB,IAAtC,EAA4C;MACvCsa,eAAe,CAAChZ,IAAD,CAAnB,EAA2B;;WAEnBA,IAAI,CAACR,KAAZ;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,2BAA1B,CAAvB;;;;;AAKF,SAASgZ,+BAAT,CAAyCxY,IAAzC,EAA+CtB,IAA/C,EAAqD;MAChDua,wBAAwB,CAACjZ,IAAD,CAA5B,EAAoC;;WAE5BA,IAAI,CAACR,KAAZ;GAFD,MAGO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,2CAA1B,CAAvB;;;;;;;AAOF,SAASkV,aAAT,CAAuB1U,IAAvB,EAA6BtB,IAA7B,EAAmC;MAC9Bwa,MAAM,CAAClZ,IAAD,CAAV,EAAkB;WACVA,IAAI,CAACR,KAAZ;GADD,MAEO;WACCgU,gBAAgB,CAACxT,IAAD,EAAOtB,IAAP,EAAasB,IAAI,CAACR,KAAlB,EAA0B,uBAA1B,CAAvB;;;;AAIF,SAASoV,aAAT,CAAuB5U,IAAvB,EAA6B;SACrBI,MAAM,CAACJ,IAAD,CAAb;;;;;;;AAOD,SAASyU,qBAAT,CAA+BzU,IAA/B,EAAqCmZ,MAArC,EAA6C;QACtCta,KAAK,GAAG,CAACmB,IAAI,CAACnB,KAAL,IAAc,EAAf,EAAmBC,KAAnB,CAAyB,CAAzB,EAA4B,CAAC,CAA7B,CAAd;QACMJ,IAAI,GAAG;IAAEiV,UAAU,EAAE;GAA3B;SAEOwF,MAAM,CAAC9X,GAAP,CAAW+X,KAAK,IAAIva,KAAK,CAACwC,GAAN,CAC1B,CAACgY,SAAD,EAAYC,KAAZ,KAAsB,OAAOF,KAAK,CAACE,KAAD,CAAZ,KAAwB,UAAxB,GAAqCF,KAAK,CAACE,KAAD,CAAL,CAAaD,SAAb,EAAwB3a,IAAxB,CAArC,GAAqEyO,SADjE,EAEzBoM,MAFyB,CAElBzF,KAAK,IAAI,OAAOA,KAAP,KAAiB,SAFR,CAApB,EAEwCyF,MAFxC,CAE+CH,KAAK,IAAIA,KAAK,CAACI,KAAN,CAC9DtX,MAAM,IAAIA,MAAM,KAAKiL,SADyC,CAFxD,EAIJ,CAJI,KAIE,EAJT;;;;;;;AAWD,SAASoH,IAAT,CAAcvU,IAAd,EAAoByZ,EAApB,EAAwB;EACvBA,EAAE,CAACzZ,IAAD,CAAF;;MAEII,MAAM,CAACJ,IAAI,CAACnB,KAAN,CAAN,CAAmBwB,MAAvB,EAA+B;IAC9BL,IAAI,CAACnB,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2Bsa,SAAS,IAAI;MACvC9E,IAAI,CAAC8E,SAAD,EAAYI,EAAZ,CAAJ;KADD;;;;;;;;AAUF,SAASjF,UAAT,CAAoBxU,IAApB,EAA0B;;SAElBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCyZ,QAAQ,CAACxZ,IAAT,CAAcF,IAAI,CAACR,KAAnB,CAAvC;;;;;;;AAOD,SAASiX,2BAAT,CAAqCzW,IAArC,EAA2C;;SAEnCI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC0Z,sBAAsB,CAACzZ,IAAvB,CAA4BF,IAAI,CAACR,KAAjC,CAAvC;;;;AAID,SAASmX,aAAT,CAAuB3W,IAAvB,EAA6B;SACrBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC2Z,QAAQ,CAAC1Z,IAAT,CAAcF,IAAI,CAACR,KAAnB,CAAvC;;;;AAID,SAASqX,aAAT,CAAuB7W,IAAvB,EAA6B;;SAErBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC4Z,QAAQ,CAAC3Z,IAAT,CAAcF,IAAI,CAACR,KAAnB,CAAvC;;;;AAID,SAASuX,+CAAT,CAAyD/W,IAAzD,EAA+D;;SAEvDI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC6Z,0CAA0C,CAAC5Z,IAA3C,CAAgDF,IAAI,CAACR,KAArD,CAAvC;;;;AAID,SAASyX,mBAAT,CAA6BjX,IAA7B,EAAmC;;SAE3BI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC8Z,cAAc,CAAC7Z,IAAf,CAAoBF,IAAI,CAACR,KAAzB,CAAvC;;;;AAID,SAAS2X,eAAT,CAAyBnX,IAAzB,EAA+B;;SAEvBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgC+Z,UAAU,CAAC9Z,IAAX,CAAgBF,IAAI,CAACR,KAArB,CAAvC;;;;AAID,SAAS6X,kBAAT,CAA4BrX,IAA5B,EAAkC;;SAE1BI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCga,aAAa,CAAC/Z,IAAd,CAAmBF,IAAI,CAACR,KAAxB,CAAvC;;;;;;;AAOD,SAAS8V,aAAT,CAAuBtV,IAAvB,EAA6B;;SAErBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCia,SAAS,CAACha,IAAV,CAAeF,IAAI,CAACR,KAApB,CAAvC;;;;AAID,SAASgW,aAAT,CAAuBxV,IAAvB,EAA6B;;SAErBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCka,SAAS,CAACja,IAAV,CAAeF,IAAI,CAACR,KAApB,CAAvC;;;;AAID,SAASkW,aAAT,CAAuB1V,IAAvB,EAA6B;;SAErBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCma,QAAQ,CAACla,IAAT,CAAcF,IAAI,CAACR,KAAnB,CAAvC;;;;AAID,SAASuU,kBAAT,CAA4B/T,IAA5B,EAAkC;;SAE1BI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCoa,aAAa,CAACna,IAAd,CAAmBF,IAAI,CAACR,KAAxB,CAAvC;;;;AAID,SAASsW,YAAT,CAAsB9V,IAAtB,EAA4B;SACpBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCqa,OAAO,CAACxX,aAAa,CAAC9C,IAAI,CAACR,KAAN,CAAd,CAA9C;;;;AAID,SAASoW,UAAT,CAAoB5V,IAApB,EAA0B;;SAElBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCyM,eAAa,CAACxM,IAAd,CAAmBF,IAAI,CAACR,KAAxB,CAAvC;;;;AAID,SAASmZ,YAAT,CAAsB3Y,IAAtB,EAA4B;;SAEpBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAAtB,IAAgCsa,eAAe,CAACra,IAAhB,CAAqBF,IAAI,CAACR,KAA1B,CAAvC;;;;;;;AAOD,SAASuR,KAAT,CAAe/Q,IAAf,EAAqB;SACbI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,QAAtB,IAAkCua,YAAY,CAACta,IAAb,CAAkBF,IAAI,CAAC8Y,IAAvB,CAAzC;;;;AAID,SAASnE,OAAT,CAAiB3U,IAAjB,EAAuB;SACfI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,OAA7B;;;;AAID,SAASgW,OAAT,CAAiBjW,IAAjB,EAAuB;SACfI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,UAAtB,IAAoCD,IAAI,CAACR,KAAL,KAAe,GAA1D;;;;AAID,SAASoZ,QAAT,CAAkB5Y,IAAlB,EAAwB;SAChBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,QAAtB,IAAkCD,IAAI,CAAC8Y,IAAL,KAAc,EAAvD;;;;AAID,SAASC,mBAAT,CAA6B/Y,IAA7B,EAAmC;SAC3BI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,UAAtB,IAAoCoY,cAAc,CAACnY,IAAf,CAAoBF,IAAI,CAACR,KAAzB,CAA3C;;;;AAID,SAASyZ,wBAAT,CAAkCjZ,IAAlC,EAAwC;SAChCI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,UAAtB,IAAoCwa,mBAAmB,CAACva,IAApB,CAAyBF,IAAI,CAACR,KAA9B,CAA3C;;;;AAID,SAASwZ,eAAT,CAAyBhZ,IAAzB,EAA+B;SACvBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,UAAtB,IAAoCya,UAAU,CAACxa,IAAX,CAAgBF,IAAI,CAACR,KAArB,CAA3C;;;;AAID,SAASqZ,YAAT,CAAsB7Y,IAAtB,EAA4B;SACpBI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,QAAtB,KAAmCD,IAAI,CAAC8Y,IAAL,KAAc,GAAd,IAAqB9Y,IAAI,CAACR,KAAL,KAAe,GAAvE,CAAP;;;;AAID,SAAS0Z,MAAT,CAAgBlZ,IAAhB,EAAsB;;SAEdI,MAAM,CAACJ,IAAD,CAAN,CAAaC,IAAb,KAAsB,MAA7B;;;;;;AAMD,MAAMsX,UAAU,GAAG,aAAnB;AACA,MAAMoC,sBAAsB,GAAG,8BAA/B;AACA,MAAMG,0CAA0C,GAAG,2DAAnD;AACA,MAAME,UAAU,GAAG,YAAnB;AACA,MAAMK,aAAa,GAAG,cAAtB;AACA,MAAME,eAAe,GAAG,kBAAxB;AACA,MAAMN,aAAa,GAAG,aAAtB;AACA,MAAMvN,eAAa,GAAG,yGAAtB;AACA,MAAMyN,SAAS,GAAG,UAAlB;AACA,MAAMK,YAAY,GAAG,yBAArB;AACA,MAAMX,QAAQ,GAAG,WAAjB;AACA,MAAMO,QAAQ,GAAG,QAAjB;AACA,MAAM/B,cAAc,GAAG,QAAvB;AACA,MAAMoC,mBAAmB,GAAG,SAA5B;AACA,MAAMb,QAAQ,GAAG,QAAjB;AACA,MAAMM,SAAS,GAAG,UAAlB;AACA,MAAMH,cAAc,GAAG,iBAAvB;AACA,MAAML,QAAQ,GAAG,QAAjB;AACA,MAAM3E,aAAa,GAAG,kBAAtB;AACA,MAAM2F,UAAU,GAAG,OAAnB;;ACpvBA,YAAe/Z,OAAO,CAACga,MAAR,CAAe,4BAAf,EAA6Cjc,IAAI,IAAI;;QAE7Dkc,aAAa,GAAGnZ,MAAM,CAACrB,MAAM,CAAC1B,IAAD,CAAN,CAAaiV,UAAb,IAA2B,OAA5B,CAAN,CAA2C9R,WAA3C,EAAtB,CAFmE;;QAK7DgZ,cAAc,GAAGza,MAAM,CAAC1B,IAAD,CAAN,CAAa4V,WAAb,KAA6B9G,KAAK,IAAIA,KAAK,CAACyB,QAAN,EAAtC,CAAvB,CALmE;;;QAQ7D6L,UAAU,GAAG,GAAGC,MAAH,CAAU3a,MAAM,CAAC1B,IAAD,CAAN,CAAaoc,UAAb,IAA2B,EAArC,CAAnB,CARmE;;QAW7DE,gBAAgB,GAAG,mBAAmB5a,MAAM,CAAC1B,IAAD,CAAzB,GAAkCA,IAAI,CAACsV,aAAvC,GAAuD,IAAhF,CAXmE;;QAc7DiH,uBAAuB,GAAG9Z,iCAAiC,CAAC2Z,UAAD,CAAjE;;;;mCAEO,WAAOrc,IAAP,EAAayD,MAAb,EAAwB;cACxBpB,gBAAgB,GAAGV,MAAM,CAACW,MAAP,QAClBka,uBADkB,GAExBzc,mBAAmB,CAACC,IAAD,EAAO;UAAEiB,QAAQ,EAAE;SAAnB,CAFK,CAAzB;QAKAjB,IAAI,CAACyc,SAAL,CAAe9b,IAAI,IAAI;gBAChB+b,aAAa,GAAG/b,IAAI,CAACI,KAA3B;;cAEI4b,qBAAqB,CAAClb,IAAtB,CAA2Bib,aAA3B,CAAJ,EAA+C;kBACxCE,GAAG,GAAGjH,WAAM,CAAC+G,aAAD,EAAgB;cAAEG,KAAK,EAAE;aAAzB,CAAN,CAAuC7b,KAAvC,EAAZ;YAEAoU,YAAY,CAACwH,GAAD,EAAM;cACjB1H,UAAU,EAAEiH,aADK;cAEjBtG,WAAW,EAAEuG,cAFI;cAGjB7G,aAAa,EAAEgH,gBAHE;cAIjB5b,IAJiB;cAKjB8C,MALiB;cAMjBpB;aANW,CAAZ;kBASMgX,aAAa,GAAGuD,GAAG,CAAC9L,QAAJ,EAAtB;;gBAEI4L,aAAa,KAAKrD,aAAtB,EAAqC;cACpC1Y,IAAI,CAACI,KAAL,GAAasY,aAAb;;;SAlBH;OAND;;;;;;;CAhBc,CAAf;AA+CA,MAAMsD,qBAAqB,GAAG,wBAA9B;;;;"}