# Changes to PostCSS Gray

### 5.0.0 (July 26, 2018)

- Rewritten to follow the latest [CSSWG specification](https://drafts.csswg.org/css-color/#grays)
- Added: Compatibility with PostCSS v7.

# 4.1.0 - December 19, 2017

- Changed: relicense ([MIT](https://opensource.org/licenses/MIT) → [ISC](https://opensource.org/licenses/ISC))
- Updated dependencies

# 4.0.0 - May 15, 2017

 - Added: compatibility with PostCSS v6
 - Updated dependencies

# 3.0.1 - November 28, 2016

- Bump `color` dependency version
(@KenanY)

# 3.0.0 - September 8, 2015

- Added: compatibility with PostCSS v5
- Removed: compatibility with PostCSS v4

# 2.0.0 - January 26, 2015

- Added: compatibility with PostCSS v4
- Removed: compatibility with PostCSS v3

# 1.1.0 - November 25, 2014

- Changed: Enhanced exceptions

# 1.0.0 - November 1, 2014

- Initial release
