{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport parser from 'postcss-selector-parser';\n\nconst anyAnyLinkMatch = /:any-link/;\n\nexport default postcss.plugin('postcss-pseudo-class-any-link', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : true;\n\n\treturn root => {\n\t\t// walk each matching rule\n\t\troot.walkRules(anyAnyLinkMatch, rule => {\n\t\t\tconst rawSelector = rule.raws.selector && rule.raws.selector.raw || rule.selector;\n\n\t\t\t// workaround for https://github.com/postcss/postcss-selector-parser/issues/28#issuecomment-171910556\n\t\t\tif (rawSelector[rawSelector.length - 1] !== ':') {\n\t\t\t\t// update the selector\n\t\t\t\tconst updatedSelector = parser(selectors => {\n\t\t\t\t\t// cache variables\n\t\t\t\t\tlet node;\n\t\t\t\t\tlet nodeIndex;\n\t\t\t\t\tlet selector;\n\t\t\t\t\tlet selectorLink;\n\t\t\t\t\tlet selectorVisited;\n\n\t\t\t\t\t// cache the selector index\n\t\t\t\t\tlet selectorIndex = -1;\n\n\t\t\t\t\t// for each selector\n\t\t\t\t\twhile (selector = selectors.nodes[++selectorIndex]) {\n\t\t\t\t\t\t// reset the node index\n\t\t\t\t\t\tnodeIndex = -1;\n\n\t\t\t\t\t\t// for each node\n\t\t\t\t\t\twhile (node = selector.nodes[++nodeIndex]) {\n\t\t\t\t\t\t\t// if the node value matches the any-link value\n\t\t\t\t\t\t\tif (node.value === ':any-link') {\n\t\t\t\t\t\t\t\t// clone the selector\n\t\t\t\t\t\t\t\tselectorLink = selector.clone();\n\t\t\t\t\t\t\t\tselectorVisited = selector.clone();\n\n\t\t\t\t\t\t\t\t// update the matching clone values\n\t\t\t\t\t\t\t\tselectorLink.nodes[nodeIndex].value = ':link';\n\t\t\t\t\t\t\t\tselectorVisited.nodes[nodeIndex].value = ':visited';\n\n\t\t\t\t\t\t\t\t// replace the selector with the clones and roll back the selector index\n\t\t\t\t\t\t\t\tselectors.nodes.splice(selectorIndex--, 1, selectorLink, selectorVisited);\n\n\t\t\t\t\t\t\t\t// stop updating the selector\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}).processSync(rawSelector);\n\n\t\t\t\tif (updatedSelector !== rawSelector) {\n\t\t\t\t\tif (preserve) {\n\t\t\t\t\t\trule.cloneBefore({\n\t\t\t\t\t\t\tselector: updatedSelector\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\trule.selector = updatedSelector;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t};\n});\n"], "names": ["anyAnyLinkMatch", "postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkRules", "rule", "rawSelector", "raws", "selector", "raw", "length", "updatedSelector", "parser", "selectors", "node", "nodeIndex", "selectorLink", "selectorVisited", "selectorIndex", "nodes", "value", "clone", "splice", "processSync", "cloneBefore"], "mappings": ";;;AAGA,MAAMA,eAAe,GAAG,WAAxB;AAEA,YAAeC,OAAO,CAACC,MAAR,CAAe,+BAAf,EAAgDC,IAAI,IAAI;QAChEC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,IAAvE;SAEOG,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeR,eAAf,EAAgCS,IAAI,IAAI;YACjCC,WAAW,GAAGD,IAAI,CAACE,IAAL,CAAUC,QAAV,IAAsBH,IAAI,CAACE,IAAL,CAAUC,QAAV,CAAmBC,GAAzC,IAAgDJ,IAAI,CAACG,QAAzE,CADuC;;UAInCF,WAAW,CAACA,WAAW,CAACI,MAAZ,GAAqB,CAAtB,CAAX,KAAwC,GAA5C,EAAiD;;cAE1CC,eAAe,GAAGC,MAAM,CAACC,SAAS,IAAI;;cAEvCC,IAAJ;cACIC,SAAJ;cACIP,QAAJ;cACIQ,YAAJ;cACIC,eAAJ,CAN2C;;cASvCC,aAAa,GAAG,CAAC,CAArB,CAT2C;;iBAYpCV,QAAQ,GAAGK,SAAS,CAACM,KAAV,CAAgB,EAAED,aAAlB,CAAlB,EAAoD;;YAEnDH,SAAS,GAAG,CAAC,CAAb,CAFmD;;mBAK5CD,IAAI,GAAGN,QAAQ,CAACW,KAAT,CAAe,EAAEJ,SAAjB,CAAd,EAA2C;;kBAEtCD,IAAI,CAACM,KAAL,KAAe,WAAnB,EAAgC;;gBAE/BJ,YAAY,GAAGR,QAAQ,CAACa,KAAT,EAAf;gBACAJ,eAAe,GAAGT,QAAQ,CAACa,KAAT,EAAlB,CAH+B;;gBAM/BL,YAAY,CAACG,KAAb,CAAmBJ,SAAnB,EAA8BK,KAA9B,GAAsC,OAAtC;gBACAH,eAAe,CAACE,KAAhB,CAAsBJ,SAAtB,EAAiCK,KAAjC,GAAyC,UAAzC,CAP+B;;gBAU/BP,SAAS,CAACM,KAAV,CAAgBG,MAAhB,CAAuBJ,aAAa,EAApC,EAAwC,CAAxC,EAA2CF,YAA3C,EAAyDC,eAAzD,EAV+B;;;;;;SAnBL,CAAN,CAoCrBM,WApCqB,CAoCTjB,WApCS,CAAxB;;YAsCIK,eAAe,KAAKL,WAAxB,EAAqC;cAChCN,QAAJ,EAAc;YACbK,IAAI,CAACmB,WAAL,CAAiB;cAChBhB,QAAQ,EAAEG;aADX;WADD,MAIO;YACNN,IAAI,CAACG,QAAL,GAAgBG,eAAhB;;;;KAlDJ;GAFD;CAHc,CAAf;;;;"}