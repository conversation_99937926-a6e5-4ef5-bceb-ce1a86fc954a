"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});

var react_1 = require("react");

var useUpdateEffect = function useUpdateEffect(effect, deps) {
  var isMounted = react_1.useRef(false);
  react_1.useEffect(function () {
    return function () {
      isMounted.current = false;
    };
  }, []);
  react_1.useEffect(function () {
    if (!isMounted.current) {
      isMounted.current = true;
    } else {
      return effect();
    }
  }, deps);
};

exports["default"] = useUpdateEffect;