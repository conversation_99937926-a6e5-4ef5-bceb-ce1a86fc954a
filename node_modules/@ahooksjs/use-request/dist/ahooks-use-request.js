!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("React")):"function"==typeof define&&define.amd?define(["React"],e):"object"==typeof exports?exports.ahooksUseRequest=e(require("React")):t.ahooksUseRequest=e(t.React)}(this,(function(t){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=4)}([function(e,n){e.exports=t},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){(function(e){var n=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,a=parseInt,u="object"==typeof e&&e&&e.Object===Object&&e,c="object"==typeof self&&self&&self.Object===Object&&self,l=u||c||Function("return this")(),f=Object.prototype.toString,s=Math.max,d=Math.min,h=function(){return l.Date.now()};function v(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function p(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&"[object Symbol]"==f.call(t)}(t))return NaN;if(v(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=v(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(n,"");var u=o.test(t);return u||i.test(t)?a(t.slice(2),u?2:8):r.test(t)?NaN:+t}t.exports=function(t,e,n){var r,o,i,a,u,c,l=0,f=!1,y=!1,b=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function g(e){var n=r,i=o;return r=o=void 0,l=e,a=t.apply(i,n)}function m(t){return l=t,u=setTimeout(j,e),f?g(t):a}function O(t){var n=t-c;return void 0===c||n>=e||n<0||y&&t-l>=i}function j(){var t=h();if(O(t))return w(t);u=setTimeout(j,function(t){var n=e-(t-c);return y?d(n,i-(t-l)):n}(t))}function w(t){return u=void 0,b&&r?g(t):(r=o=void 0,a)}function S(){var t=h(),n=O(t);if(r=arguments,o=this,c=t,n){if(void 0===u)return m(c);if(y)return u=setTimeout(j,e),g(c)}return void 0===u&&(u=setTimeout(j,e)),a}return e=p(e)||0,v(n)&&(f=!!n.leading,i=(y="maxWait"in n)?s(p(n.maxWait)||0,e):i,b="trailing"in n?!!n.trailing:b),S.cancel=function(){void 0!==u&&clearTimeout(u),l=0,r=c=o=u=void 0},S.flush=function(){return void 0===u?a:w(h())},S}}).call(this,n(1))},function(t,e,n){(function(e){var n=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,a=parseInt,u="object"==typeof e&&e&&e.Object===Object&&e,c="object"==typeof self&&self&&self.Object===Object&&self,l=u||c||Function("return this")(),f=Object.prototype.toString,s=Math.max,d=Math.min,h=function(){return l.Date.now()};function v(t,e,n){var r,o,i,a,u,c,l=0,f=!1,v=!1,b=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function g(e){var n=r,i=o;return r=o=void 0,l=e,a=t.apply(i,n)}function m(t){return l=t,u=setTimeout(j,e),f?g(t):a}function O(t){var n=t-c;return void 0===c||n>=e||n<0||v&&t-l>=i}function j(){var t=h();if(O(t))return w(t);u=setTimeout(j,function(t){var n=e-(t-c);return v?d(n,i-(t-l)):n}(t))}function w(t){return u=void 0,b&&r?g(t):(r=o=void 0,a)}function S(){var t=h(),n=O(t);if(r=arguments,o=this,c=t,n){if(void 0===u)return m(c);if(v)return u=setTimeout(j,e),g(c)}return void 0===u&&(u=setTimeout(j,e)),a}return e=y(e)||0,p(n)&&(f=!!n.leading,i=(v="maxWait"in n)?s(y(n.maxWait)||0,e):i,b="trailing"in n?!!n.trailing:b),S.cancel=function(){void 0!==u&&clearTimeout(u),l=0,r=c=o=u=void 0},S.flush=function(){return void 0===u?a:w(h())},S}function p(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function y(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&"[object Symbol]"==f.call(t)}(t))return NaN;if(p(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=p(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(n,"");var u=o.test(t);return u||i.test(t)?a(t.slice(2),u?2:8):r.test(t)?NaN:+t}t.exports=function(t,e,n){var r=!0,o=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return p(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),v(t,e,{leading:r,maxWait:e,trailing:o})}}).call(this,n(1))},function(t,e,n){"use strict";n.r(e),n.d(e,"useAsync",(function(){return M})),n.d(e,"usePaginated",(function(){return q})),n.d(e,"useLoadMore",(function(){return _})),n.d(e,"UseRequestProvider",(function(){return Y})),n.d(e,"UseAPIProvider",(function(){return Q}));var r=n(0),o=n.n(r),i=n(2),a=n.n(i),u=n(3),c=n.n(u);function l(){return"undefined"==typeof document||void 0===document.visibilityState||"hidden"!==document.visibilityState}var f=new Map,s=function(t){var e=f.get(t);return{data:null==e?void 0:e.data,startTime:null==e?void 0:e.startTime}},d=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},h=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(d(arguments[e]));return t};var v=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},p=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(v(arguments[e]));return t};var y=function(t){var e=Object(r.useRef)((function(){throw new Error("Cannot call an event handler while rendering.")}));e.current=t;var n=Object(r.useCallback)((function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=e.current;if(r)return r.apply(void 0,p(t))}),[e]);if("function"==typeof t)return n},b=function(t,e){var n=Object(r.useRef)(!1);Object(r.useEffect)((function(){return function(){n.current=!1}}),[]),Object(r.useEffect)((function(){if(n.current)return t();n.current=!0}),e)},g=[];var m=!1;if("undefined"!=typeof window&&window.addEventListener&&!m){var O=function(){if(l()&&(void 0===navigator.onLine||navigator.onLine))for(var t=0;t<g.length;t++){(0,g[t])()}};window.addEventListener("visibilitychange",O,!1),window.addEventListener("focus",O,!1),m=!0}var j=function(t){return g.push(t),function(){var e=g.indexOf(t);g.splice(e,1)}},w=[];var S=!1;if("undefined"!=typeof window&&window.addEventListener&&!S){window.addEventListener("visibilitychange",(function(){if(l())for(var t=0;t<w.length;t++){(0,w[t])()}}),!1),S=!0}var T=function(t){return w.push(t),function(){var e=w.indexOf(t);w.splice(e,1)}},x=function(){return(x=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},E=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},R=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(E(arguments[e]));return t},P="AHOOKS_USE_REQUEST_DEFAULT_KEY",D=function(){function t(t,e,n,r){var o,i,u;this.count=0,this.pollingWhenVisibleFlag=!1,this.pollingTimer=void 0,this.loadingDelayTimer=void 0,this.unsubscribe=[],this.that=this,this.state={loading:!1,params:[],data:void 0,error:void 0,run:this.run.bind(this.that),mutate:this.mutate.bind(this.that),refresh:this.refresh.bind(this.that),cancel:this.cancel.bind(this.that),unmount:this.unmount.bind(this.that)},this.service=t,this.config=e,this.subscribe=n,r&&(this.state=x(x({},this.state),r)),this.debounceRun=this.config.debounceInterval?a()(this._run,this.config.debounceInterval):void 0,this.throttleRun=this.config.throttleInterval?c()(this._run,this.config.throttleInterval):void 0,this.limitRefresh=(o=this.refresh.bind(this),i=this.config.focusTimespan,u=!1,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];u||(u=!0,o.apply(void 0,h(t)),setTimeout((function(){u=!1}),i))}),this.config.pollingInterval&&this.unsubscribe.push(T(this.rePolling.bind(this))),this.config.refreshOnWindowFocus&&this.unsubscribe.push(j(this.limitRefresh.bind(this)))}return t.prototype.setState=function(t){void 0===t&&(t={}),this.state=x(x({},this.state),t),this.subscribe(this.state)},t.prototype._run=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];this.pollingTimer&&clearTimeout(this.pollingTimer),this.loadingDelayTimer&&clearTimeout(this.loadingDelayTimer),this.count+=1;var r=this.count;return this.setState({loading:!this.config.loadingDelay,params:e}),this.config.loadingDelay&&(this.loadingDelayTimer=setTimeout((function(){t.setState({loading:!0})}),this.config.loadingDelay)),this.service.apply(this,R(e)).then((function(n){if(r!==t.count)return new Promise((function(){}));t.loadingDelayTimer&&clearTimeout(t.loadingDelayTimer);var o=t.config.formatResult?t.config.formatResult(n):n;return t.setState({data:o,error:void 0,loading:!1}),t.config.onSuccess&&t.config.onSuccess(o,e),o})).catch((function(n){if(r!==t.count)return new Promise((function(){}));if(t.loadingDelayTimer&&clearTimeout(t.loadingDelayTimer),t.setState({data:void 0,error:n,loading:!1}),t.config.onError&&t.config.onError(n,e),t.config.throwOnError)throw n;return console.error(n),Promise.reject("useRequest has caught the exception, if you need to handle the exception yourself, you can set options.throwOnError to true.")})).finally((function(){if(r===t.count&&t.config.pollingInterval){if(!l()&&!t.config.pollingWhenHidden)return void(t.pollingWhenVisibleFlag=!0);t.pollingTimer=setTimeout((function(){t._run.apply(t,R(e))}),t.config.pollingInterval)}}))},t.prototype.run=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return this.debounceRun?(this.debounceRun.apply(this,R(t)),Promise.resolve(null)):this.throttleRun?(this.throttleRun.apply(this,R(t)),Promise.resolve(null)):this._run.apply(this,R(t))},t.prototype.cancel=function(){this.debounceRun&&this.debounceRun.cancel(),this.throttleRun&&this.throttleRun.cancel(),this.loadingDelayTimer&&clearTimeout(this.loadingDelayTimer),this.pollingTimer&&clearTimeout(this.pollingTimer),this.pollingWhenVisibleFlag=!1,this.count+=1,this.setState({loading:!1})},t.prototype.refresh=function(){return this.run.apply(this,R(this.state.params))},t.prototype.rePolling=function(){this.pollingWhenVisibleFlag&&(this.pollingWhenVisibleFlag=!1,this.refresh())},t.prototype.mutate=function(t){"function"==typeof t?this.setState({data:t(this.state.data)||{}}):this.setState({data:t})},t.prototype.unmount=function(){this.cancel(),this.unsubscribe.forEach((function(t){t()}))},t}();var M=function(t,e){var n,o=e||{},i=o.refreshDeps,a=void 0===i?[]:i,u=o.manual,c=void 0!==u&&u,l=o.onSuccess,d=void 0===l?function(){}:l,h=o.onError,v=void 0===h?function(){}:h,p=o.defaultLoading,g=void 0!==p&&p,m=o.loadingDelay,O=o.pollingInterval,j=void 0===O?0:O,w=o.pollingWhenHidden,S=void 0===w||w,T=o.defaultParams,M=void 0===T?[]:T,k=o.refreshOnWindowFocus,C=void 0!==k&&k,I=o.focusTimespan,W=void 0===I?5e3:I,_=o.fetchKey,F=o.cacheKey,K=o.cacheTime,L=void 0===K?3e5:K,N=o.staleTime,q=void 0===N?0:N,z=o.debounceInterval,U=o.throttleInterval,$=o.initialData,H=o.ready,V=void 0===H||H,A=o.throwOnError,Y=void 0!==A&&A,Q=Object(r.useRef)(P),B=y(t),G=y(d),J=y(v),X=y(_);"formatResult"in o&&(n=o.formatResult);var Z={formatResult:y(n),onSuccess:G,onError:J,loadingDelay:m,pollingInterval:j,pollingWhenHidden:S,refreshOnWindowFocus:!c&&C,focusTimespan:W,debounceInterval:z,throttleInterval:U,throwOnError:Y},tt=y((function(t,e){rt((function(n){return n[t]=e,x({},n)}))})),et=E(Object(r.useState)((function(){var t;if(F){var e=null===(t=s(F))||void 0===t?void 0:t.data;if(e){Q.current=e.newstFetchKey;var n={};return Object.keys(e.fetches).forEach((function(t){var r=e.fetches[t],o=new D(B,Z,tt.bind(null,t),{loading:r.loading,params:r.params,data:r.data,error:r.error});n[t]=o.state})),n}}return{}})),2),nt=et[0],rt=et[1],ot=Object(r.useRef)(nt);ot.current=nt;var it=Object(r.useRef)(),at=Object(r.useCallback)((function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(V){if(X){var n=X.apply(void 0,R(t));Q.current=void 0===n?P:n}var r=Q.current,o=ot.current[r];if(!o){var i=new D(B,Z,tt.bind(null,r),{data:$});o=i.state,rt((function(t){return t[r]=o,x({},t)}))}return o.run.apply(o,R(t))}it.current=t}),[_,tt,V]),ut=Object(r.useRef)(at);ut.current=at,b((function(){F&&function(t,e,n){var r=f.get(t);(null==r?void 0:r.timer)&&clearTimeout(r.timer);var o=void 0;e>-1&&(o=setTimeout((function(){f.delete(t)}),e)),f.set(t,{data:n,timer:o,startTime:(new Date).getTime()})}(F,L,{fetches:nt,newstFetchKey:Q.current})}),[F,nt]);var ct=Object(r.useRef)(!1);b((function(){V&&(!ct.current&&it.current&&ut.current.apply(ut,R(it.current)),ct.current=!0)}),[V]),Object(r.useEffect)((function(){var t;if(!c)if(Object.keys(nt).length>0){var e=F&&(null===(t=s(F))||void 0===t?void 0:t.startTime)||0;-1===q||(new Date).getTime()-e<=q||Object.values(nt).forEach((function(t){t.refresh()}))}else ut.current.apply(ut,R(M))}),[]);var lt=Object(r.useCallback)((function(){Object.values(ot.current).forEach((function(t){t.unmount()})),Q.current=P,rt({}),ot.current={}}),[rt]);b((function(){c||Object.values(ot.current).forEach((function(t){t.refresh()}))}),R(a)),Object(r.useEffect)((function(){return function(){Object.values(ot.current).forEach((function(t){t.unmount()}))}}),[]);var ft=Object(r.useCallback)((function(t){return function(){console.warn("You should't call "+t+" when service not executed once.")}}),[]);return x(x({loading:V&&!c||g,data:$,error:void 0,params:[],cancel:ft("cancel"),refresh:ft("refresh"),mutate:ft("mutate")},nt[Q.current]||{}),{run:at,fetches:nt,reset:lt})},k=function(){return(k=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},C=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n},I=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},W=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(I(arguments[e]));return t};var _=function(t,e){var n=e.refreshDeps,o=void 0===n?[]:n,i=e.ref,a=e.isNoMore,u=e.threshold,c=void 0===u?100:u,l=(e.fetchKey,C(e,["refreshDeps","ref","isNoMore","threshold","fetchKey"])),f=I(Object(r.useState)(!1),2),s=f[0],d=f[1];Object(r.useEffect)((function(){e.fetchKey&&console.warn("useRequest loadMore mode don't need fetchKey!")}),[]);var h=M(t,k(k({},l),{fetchKey:function(t){var e;return(null===(e=null==t?void 0:t.list)||void 0===e?void 0:e.length)||0},onSuccess:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];d(!1),e.onSuccess&&e.onSuccess.apply(e,W(t))}})),v=h.data,p=h.run,y=h.params,g=h.reset,m=h.loading,O=h.fetches,j=Object(r.useCallback)((function(){g();var t=I(y).slice(1);p.apply(void 0,W([void 0],t))}),[p,g,y]),w=Object(r.useRef)(j);w.current=j,b((function(){e.manual||w.current()}),W(o));var S=Object(r.useMemo)((function(){var t=[],e=v;return Object.values(O).forEach((function(n){var r,o;(null===(r=n.data)||void 0===r?void 0:r.list)&&(t=t.concat(null===(o=n.data)||void 0===o?void 0:o.list)),n.loading||(e=n.data)})),k(k({},e),{list:t})}),[O,v]),T=!!a&&(!m&&!s&&a(S)),x=Object(r.useCallback)((function(){if(!T){d(!0);var t=I(y).slice(1);p.apply(void 0,W([S],t))}}),[T,p,S,y]),E=function(){!m&&!s&&i&&i.current&&i.current.scrollHeight-i.current.scrollTop<=i.current.clientHeight+c&&x()},R=Object(r.useRef)(E);return R.current=E,Object(r.useEffect)((function(){if(!i||!i.current)return function(){};var t=function(){return R.current()};return i.current.addEventListener("scroll",t),function(){i&&i.current&&i.current.removeEventListener("scroll",t)}}),[R]),k(k({},h),{data:S,reload:j,loading:m&&0===S.list.length,loadMore:x,loadingMore:s,noMore:T})},F=function(){return(F=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},K=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n},L=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},N=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(L(arguments[e]));return t};var q=function(t,e){e.paginated;var n=e.defaultPageSize,o=void 0===n?10:n,i=e.refreshDeps,a=void 0===i?[]:i,u=e.fetchKey,c=K(e,["paginated","defaultPageSize","refreshDeps","fetchKey"]);Object(r.useEffect)((function(){u&&console.error("useRequest pagination's fetchKey will not work!")}),[]);var l=M(t,F({defaultParams:[{current:1,pageSize:o}]},c)),f=l.data,s=l.params,d=l.run,h=l.loading,v=K(l,["data","params","run","loading"]),p=s&&s[0]?s[0]:{},y=p.current,g=void 0===y?1:y,m=p.pageSize,O=void 0===m?o:m,j=p.sorter,w=void 0===j?{}:j,S=p.filters,T=void 0===S?{}:S,x=Object(r.useCallback)((function(t){var e=L(s),n=e[0],r=e.slice(1);d.apply(void 0,N([F(F({},n),t)],r))}),[d,s]),E=(null==f?void 0:f.total)||0,R=Object(r.useMemo)((function(){return Math.ceil(E/O)}),[O,E]),P=Object(r.useCallback)((function(t,e){var n=t<=0?1:t,r=e<=0?1:e,o=Math.ceil(E/r);n>o&&(n=Math.max(1,o)),x({current:n,pageSize:r})}),[E,x]),D=Object(r.useCallback)((function(t){P(t,O)}),[P,O]),k=Object(r.useCallback)((function(t){P(g,t)}),[P,g]),C=Object(r.useRef)(D);C.current=D,b((function(){e.manual||C.current(1)}),N(a));var I=Object(r.useCallback)((function(t,e,n){x({current:t.current,pageSize:t.pageSize||o,filters:e,sorter:n})}),[T,w,x]);return F({loading:h,data:f,params:s,run:d,pagination:{current:g,pageSize:O,total:E,totalPage:R,onChange:P,changeCurrent:D,changePageSize:k},tableProps:{dataSource:(null==f?void 0:f.list)||[],loading:h,onChange:I,pagination:{current:g,pageSize:O,total:E}},sorter:w,filters:T},v)},z=o.a.createContext({});z.displayName="UseRequestConfigContext";var U=z,$=function(){return($=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},H=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n},V=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},A=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(V(arguments[e]));return t};var Y=U.Provider,Q=Y;e.default=function(t,e){void 0===e&&(e={});var n=Object(r.useContext)(U),o=$($({},n),e),i=o.paginated,a=o.loadMore,u=o.requestMethod,c=Object(r.useRef)(i),l=Object(r.useRef)(a);if(c.current!==i)throw Error("You should not modify the paginated of options");if(l.current!==a)throw Error("You should not modify the loadMore of options");c.current=i,l.current=a;var f,s=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return fetch.apply(void 0,A(t)).then((function(t){if(t.ok)return t.json();throw new Error(t.statusText)}))},d=u||s;switch(typeof t){case"string":f=function(){return d(t)};break;case"object":var h=t.url,v=H(t,["url"]);f=function(){return u?u(t):s(h,v)};break;default:f=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return new Promise((function(n,r){var o=t.apply(void 0,A(e)),i=o;if(!o.then)switch(typeof o){case"string":i=d(o);break;case"object":var a=o.url,c=H(o,["url"]);i=u?u(o):s(a,c)}i.then(n).catch(r)}))}}return a?_(f,o):i?q(f,o):M(f,o)}}])}));