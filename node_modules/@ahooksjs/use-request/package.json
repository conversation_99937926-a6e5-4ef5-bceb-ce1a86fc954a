{"name": "@ahooksjs/use-request", "version": "2.8.15", "description": "React Hooks for fetching, caching and updating asynchronous data", "main": "./lib/index.js", "module": "./es/index.js", "types": "./lib/index.d.ts", "unpkg": "dist/ahooks-use-request.js", "files": ["dist", "lib", "es", "package.json"], "repository": {"type": "git", "url": "git+https://github.com/alibaba/hooks.git"}, "scripts": {"build": "gulp && webpack-cli"}, "publishConfig": {"registry": "https://registry.npmjs.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/alibaba/hooks/issues"}, "homepage": "https://github.com/alibaba/hooks", "peerDependencies": {"react": "^16.8.0 || ^17.0.0"}, "dependencies": {"lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1"}, "devDependencies": {"axios": "^0.21.1", "umi-request": "^1.2.17"}, "gitHead": "b5ff724e5f4c9d83a21f531f3dab5824444fda80"}