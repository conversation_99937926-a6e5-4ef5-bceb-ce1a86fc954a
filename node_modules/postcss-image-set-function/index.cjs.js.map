{"version": 3, "file": "index.cjs.js", "sources": ["lib/get-comma.js", "lib/get-image.js", "lib/get-media.js", "lib/handle-invalidation.js", "lib/process-image-set.js", "index.js"], "sourcesContent": ["// return whether a node is a valid comma\nexport default node => Object(node).type === 'comma';\n", "const imageSetFunctionMatchRegExp = /^(-webkit-)?image-set$/i\n\n// return a valid image\nexport default node =>\n\t// <url> | <image()> | <cross-fade()> | <gradient>\n\t// the image-set() function can not be nested inside of itself\n\tObject(node).type === 'func' && /^(cross-fade|image|(repeating-)?(conic|linear|radial)-gradient|url)$/i.test(node.value) && !(\n\t\tnode.parent.parent && node.parent.parent.type === 'func' && imageSetFunctionMatchRegExp.test(node.parent.parent.value)\n\t)\n\t? String(node)\n: Object(node).type === 'string'\n\t? node.value\n: false;\n", "import postcss from 'postcss';\n\nconst dpiRatios = { dpcm: 2.54, dpi: 1, dppx: 96, x: 96 };\n\n// return a valid @media rule\nexport default (node, mediasByDpr) => {\n\tif (Object(node).type === 'number' && node.unit in dpiRatios) {\n\t\t// calculate min-device-pixel-ratio and min-resolution\n\t\tconst dpi = Number(node.value) * dpiRatios[node.unit.toLowerCase()];\n\t\tconst dpr = Math.floor(dpi / dpiRatios.x * 100) / 100;\n\n\t\tif (dpi in mediasByDpr) {\n\t\t\treturn false;\n\t\t} else {\n\t\t\tconst media = mediasByDpr[dpi] = postcss.atRule({\n\t\t\t\tname: 'media',\n\t\t\t\tparams: `(-webkit-min-device-pixel-ratio: ${dpr}), (min-resolution: ${dpi}dpi)`\n\t\t\t});\n\n\t\t\treturn media;\n\t\t}\n\t} else {\n\t\treturn false;\n\t}\n};\n", "export default (opts, message, word) => {\n\tif (opts.oninvalid === 'warn') {\n\t\topts.decl.warn(opts.result, message, { word: String(word) });\n\t} else if (opts.oninvalid === 'throw') {\n\t\tthrow opts.decl.error(message, { word: String(word) });\n\t}\n};\n", "import getComma from './get-comma';\nimport getImage from './get-image';\nimport getMedia from './get-media';\nimport handleInvalidation from './handle-invalidation';\n\nexport default (imageSetOptionNodes, decl, opts) => {\n\tconst parent = decl.parent;\n\tconst mediasByDpr = {};\n\n\tlet length = imageSetOptionNodes.length;\n\tlet index = -1;\n\n\twhile (index < length) {\n\t\tconst [comma, value, media] = [\n\t\t\tindex < 0 ? true : getComma(imageSetOptionNodes[index]),\n\t\t\tgetImage(imageSetOptionNodes[index + 1]),\n\t\t\tgetMedia(imageSetOptionNodes[index + 2], mediasByDpr)\n\t\t];\n\n\t\t// handle invalidations\n\t\tif (!comma) {\n\t\t\treturn handleInvalidation(opts, 'unexpected comma', imageSetOptionNodes[index]);\n\t\t} else if (!value) {\n\t\t\treturn handleInvalidation(opts, 'unexpected image', imageSetOptionNodes[index + 1]);\n\t\t} else if (!media) {\n\t\t\treturn handleInvalidation(opts, 'unexpected resolution', imageSetOptionNodes[index + 2]);\n\t\t}\n\n\t\t// prepare @media { decl: <image> }\n\t\tconst parentClone = parent.clone().removeAll();\n\t\tconst declClone = decl.clone({ value });\n\n\t\tparentClone.append(declClone);\n\t\tmedia.append(parentClone);\n\n\t\tindex += 3\n\t}\n\n\tconst medias = Object.keys(mediasByDpr).sort((a, b) => a - b).map(params => mediasByDpr[params]);\n\n\t// conditionally prepend previous siblings\n\tif (medias.length) {\n\t\tconst firstDecl = medias[0].nodes[0].nodes[0];\n\n\t\tif (medias.length === 1) {\n\t\t\tdecl.value = firstDecl.value\n\t\t} else {\n\t\t\tconst siblings = parent.nodes;\n\t\t\tconst previousSiblings = siblings.slice(0, siblings.indexOf(decl)).concat(firstDecl);\n\n\t\t\tif (previousSiblings.length) {\n\t\t\t\tconst parentClone = parent.cloneBefore().removeAll();\n\n\t\t\t\tparentClone.append(previousSiblings);\n\t\t\t}\n\n\t\t\t// prepend any @media { decl: <image> } rules\n\t\t\tparent.before(medias.slice(1));\n\n\t\t\t// conditionally remove the current rule\n\t\t\tif (!opts.preserve) {\n\t\t\t\tdecl.remove();\n\n\t\t\t\t// and then conditionally remove its parent\n\t\t\t\tif (!parent.nodes.length) {\n\t\t\t\t\tparent.remove();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "import postcss from 'postcss';\nimport valueParser from 'postcss-values-parser';\nimport processImageSet from './lib/process-image-set';\n\nconst imageSetValueMatchRegExp = /(^|[^\\w-])(-webkit-)?image-set\\(/\nconst imageSetFunctionMatchRegExp = /^(-webkit-)?image-set$/i\n\nexport default postcss.plugin('postcss-image-set-function', opts => {\n\t// prepare options\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : true;\n\tconst oninvalid = 'oninvalid' in Object(opts) ? opts.oninvalid : 'ignore';\n\n\treturn (root, result) => {\n\t\t// for every declaration\n\t\troot.walkDecls(decl => {\n\t\t\tconst { value } = decl;\n\n\t\t\t// if a declaration likely uses an image-set() function\n\t\t\tif (imageSetValueMatchRegExp.test(value)) {\n\t\t\t\tconst valueAST = valueParser(value).parse();\n\n\t\t\t\t// process every image-set() function\n\t\t\t\tvalueAST.walkType('func', node => {\n\t\t\t\t\tif (imageSetFunctionMatchRegExp.test(node.value)) {\n\t\t\t\t\t\tprocessImageSet(\n\t\t\t\t\t\t\tnode.nodes.slice(1, -1),\n\t\t\t\t\t\t\tdecl,\n\t\t\t\t\t\t\t{ decl, oninvalid, preserve, result }\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t}\n});\n"], "names": ["node", "Object", "type", "imageSetFunctionMatchRegExp", "test", "value", "parent", "String", "dpiRatios", "dpcm", "dpi", "dppx", "x", "mediasByDpr", "unit", "Number", "toLowerCase", "dpr", "Math", "floor", "media", "postcss", "atRule", "name", "params", "opts", "message", "word", "oninvalid", "decl", "warn", "result", "error", "imageSetOptionNodes", "length", "index", "getComma", "getImage", "getMedia", "comma", "handleInvalidation", "parentClone", "clone", "removeAll", "declClone", "append", "medias", "keys", "sort", "a", "b", "map", "firstDecl", "nodes", "siblings", "previous<PERSON><PERSON><PERSON>", "slice", "indexOf", "concat", "cloneBefore", "before", "preserve", "remove", "imageSetValueMatchRegExp", "plugin", "Boolean", "root", "walkDecls", "valueAST", "valueParser", "parse", "walkType", "processImageSet"], "mappings": ";;;;;;;AAAA;AACA,gBAAeA,IAAI,IAAIC,MAAM,CAACD,IAAD,CAAN,CAAaE,IAAb,KAAsB,OAA7C;;ACDA,MAAMC,2BAA2B,GAAG,yBAApC;;AAGA,gBAAeH,IAAI;;AAGlBC,MAAM,CAACD,IAAD,CAAN,CAAaE,IAAb,KAAsB,MAAtB,IAAgC,wEAAwEE,IAAxE,CAA6EJ,IAAI,CAACK,KAAlF,CAAhC,IAA4H,EAC3HL,IAAI,CAACM,MAAL,CAAYA,MAAZ,IAAsBN,IAAI,CAACM,MAAL,CAAYA,MAAZ,CAAmBJ,IAAnB,KAA4B,MAAlD,IAA4DC,2BAA2B,CAACC,IAA5B,CAAiCJ,IAAI,CAACM,MAAL,CAAYA,MAAZ,CAAmBD,KAApD,CAD+D,CAA5H,GAGEE,MAAM,CAACP,IAAD,CAHR,GAICC,MAAM,CAACD,IAAD,CAAN,CAAaE,IAAb,KAAsB,QAAtB,GACCF,IAAI,CAACK,KADN,GAEA,KATF;;ACDA,MAAMG,SAAS,GAAG;EAAEC,IAAI,EAAE,IAAR;EAAcC,GAAG,EAAE,CAAnB;EAAsBC,IAAI,EAAE,EAA5B;EAAgCC,CAAC,EAAE;CAArD;;AAGA,gBAAe,CAACZ,IAAD,EAAOa,WAAP,KAAuB;MACjCZ,MAAM,CAACD,IAAD,CAAN,CAAaE,IAAb,KAAsB,QAAtB,IAAkCF,IAAI,CAACc,IAAL,IAAaN,SAAnD,EAA8D;;UAEvDE,GAAG,GAAGK,MAAM,CAACf,IAAI,CAACK,KAAN,CAAN,GAAqBG,SAAS,CAACR,IAAI,CAACc,IAAL,CAAUE,WAAV,EAAD,CAA1C;UACMC,GAAG,GAAGC,IAAI,CAACC,KAAL,CAAWT,GAAG,GAAGF,SAAS,CAACI,CAAhB,GAAoB,GAA/B,IAAsC,GAAlD;;QAEIF,GAAG,IAAIG,WAAX,EAAwB;aAChB,KAAP;KADD,MAEO;YACAO,KAAK,GAAGP,WAAW,CAACH,GAAD,CAAX,GAAmBW,OAAO,CAACC,MAAR,CAAe;QAC/CC,IAAI,EAAE,OADyC;QAE/CC,MAAM,EAAG,oCAAmCP,GAAI,uBAAsBP,GAAI;OAF1C,CAAjC;aAKOU,KAAP;;GAbF,MAeO;WACC,KAAP;;CAjBF;;ACLA,0BAAe,CAACK,IAAD,EAAOC,OAAP,EAAgBC,IAAhB,KAAyB;MACnCF,IAAI,CAACG,SAAL,KAAmB,MAAvB,EAA+B;IAC9BH,IAAI,CAACI,IAAL,CAAUC,IAAV,CAAeL,IAAI,CAACM,MAApB,EAA4BL,OAA5B,EAAqC;MAAEC,IAAI,EAAEpB,MAAM,CAACoB,IAAD;KAAnD;GADD,MAEO,IAAIF,IAAI,CAACG,SAAL,KAAmB,OAAvB,EAAgC;UAChCH,IAAI,CAACI,IAAL,CAAUG,KAAV,CAAgBN,OAAhB,EAAyB;MAAEC,IAAI,EAAEpB,MAAM,CAACoB,IAAD;KAAvC,CAAN;;CAJF;;ACKA,uBAAe,CAACM,mBAAD,EAAsBJ,IAAtB,EAA4BJ,IAA5B,KAAqC;QAC7CnB,MAAM,GAAGuB,IAAI,CAACvB,MAApB;QACMO,WAAW,GAAG,EAApB;MAEIqB,MAAM,GAAGD,mBAAmB,CAACC,MAAjC;MACIC,KAAK,GAAG,CAAC,CAAb;;SAEOA,KAAK,GAAGD,MAAf,EAAuB;iBACQ,CAC7BC,KAAK,GAAG,CAAR,GAAY,IAAZ,GAAmBC,QAAQ,CAACH,mBAAmB,CAACE,KAAD,CAApB,CADE,EAE7BE,QAAQ,CAACJ,mBAAmB,CAACE,KAAK,GAAG,CAAT,CAApB,CAFqB,EAG7BG,QAAQ,CAACL,mBAAmB,CAACE,KAAK,GAAG,CAAT,CAApB,EAAiCtB,WAAjC,CAHqB,CADR;UACf0B,KADe;UACRlC,KADQ;UACDe,KADC;;QAQlB,CAACmB,KAAL,EAAY;aACJC,kBAAkB,CAACf,IAAD,EAAO,kBAAP,EAA2BQ,mBAAmB,CAACE,KAAD,CAA9C,CAAzB;KADD,MAEO,IAAI,CAAC9B,KAAL,EAAY;aACXmC,kBAAkB,CAACf,IAAD,EAAO,kBAAP,EAA2BQ,mBAAmB,CAACE,KAAK,GAAG,CAAT,CAA9C,CAAzB;KADM,MAEA,IAAI,CAACf,KAAL,EAAY;aACXoB,kBAAkB,CAACf,IAAD,EAAO,uBAAP,EAAgCQ,mBAAmB,CAACE,KAAK,GAAG,CAAT,CAAnD,CAAzB;KAbqB;;;UAiBhBM,WAAW,GAAGnC,MAAM,CAACoC,KAAP,GAAeC,SAAf,EAApB;UACMC,SAAS,GAAGf,IAAI,CAACa,KAAL,CAAW;MAAErC;KAAb,CAAlB;IAEAoC,WAAW,CAACI,MAAZ,CAAmBD,SAAnB;IACAxB,KAAK,CAACyB,MAAN,CAAaJ,WAAb;IAEAN,KAAK,IAAI,CAAT;;;QAGKW,MAAM,GAAG7C,MAAM,CAAC8C,IAAP,CAAYlC,WAAZ,EAAyBmC,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAA5C,EAA+CC,GAA/C,CAAmD3B,MAAM,IAAIX,WAAW,CAACW,MAAD,CAAxE,CAAf,CAjCmD;;MAoC/CsB,MAAM,CAACZ,MAAX,EAAmB;UACZkB,SAAS,GAAGN,MAAM,CAAC,CAAD,CAAN,CAAUO,KAAV,CAAgB,CAAhB,EAAmBA,KAAnB,CAAyB,CAAzB,CAAlB;;QAEIP,MAAM,CAACZ,MAAP,KAAkB,CAAtB,EAAyB;MACxBL,IAAI,CAACxB,KAAL,GAAa+C,SAAS,CAAC/C,KAAvB;KADD,MAEO;YACAiD,QAAQ,GAAGhD,MAAM,CAAC+C,KAAxB;YACME,gBAAgB,GAAGD,QAAQ,CAACE,KAAT,CAAe,CAAf,EAAkBF,QAAQ,CAACG,OAAT,CAAiB5B,IAAjB,CAAlB,EAA0C6B,MAA1C,CAAiDN,SAAjD,CAAzB;;UAEIG,gBAAgB,CAACrB,MAArB,EAA6B;cACtBO,WAAW,GAAGnC,MAAM,CAACqD,WAAP,GAAqBhB,SAArB,EAApB;QAEAF,WAAW,CAACI,MAAZ,CAAmBU,gBAAnB;OAPK;;;MAWNjD,MAAM,CAACsD,MAAP,CAAcd,MAAM,CAACU,KAAP,CAAa,CAAb,CAAd,EAXM;;UAcF,CAAC/B,IAAI,CAACoC,QAAV,EAAoB;QACnBhC,IAAI,CAACiC,MAAL,GADmB;;YAIf,CAACxD,MAAM,CAAC+C,KAAP,CAAanB,MAAlB,EAA0B;UACzB5B,MAAM,CAACwD,MAAP;;;;;CA5DL;;ACDA,MAAMC,wBAAwB,GAAG,kCAAjC;AACA,MAAM5D,6BAA2B,GAAG,yBAApC;AAEA,YAAekB,OAAO,CAAC2C,MAAR,CAAe,4BAAf,EAA6CvC,IAAI,IAAI;;QAE7DoC,QAAQ,GAAG,cAAc5D,MAAM,CAACwB,IAAD,CAApB,GAA6BwC,OAAO,CAACxC,IAAI,CAACoC,QAAN,CAApC,GAAsD,IAAvE;QACMjC,SAAS,GAAG,eAAe3B,MAAM,CAACwB,IAAD,CAArB,GAA8BA,IAAI,CAACG,SAAnC,GAA+C,QAAjE;SAEO,CAACsC,IAAD,EAAOnC,MAAP,KAAkB;;IAExBmC,IAAI,CAACC,SAAL,CAAetC,IAAI,IAAI;YACdxB,KADc,GACJwB,IADI,CACdxB,KADc;;UAIlB0D,wBAAwB,CAAC3D,IAAzB,CAA8BC,KAA9B,CAAJ,EAA0C;cACnC+D,QAAQ,GAAGC,WAAW,CAAChE,KAAD,CAAX,CAAmBiE,KAAnB,EAAjB,CADyC;;QAIzCF,QAAQ,CAACG,QAAT,CAAkB,MAAlB,EAA0BvE,IAAI,IAAI;cAC7BG,6BAA2B,CAACC,IAA5B,CAAiCJ,IAAI,CAACK,KAAtC,CAAJ,EAAkD;YACjDmE,eAAe,CACdxE,IAAI,CAACqD,KAAL,CAAWG,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,CADc,EAEd3B,IAFc,EAGd;cAAEA,IAAF;cAAQD,SAAR;cAAmBiC,QAAnB;cAA6B9B;aAHf,CAAf;;SAFF;;KARF;GAFD;CALc,CAAf;;;;"}