{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\n\n// gap shorthand property matcher\nconst gapPropertyRegExp = /^(column-gap|gap|row-gap)$/i;\n\nexport default postcss.plugin('postcss-gap-properties', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.preserve) : true;\n\n\treturn root => {\n\t\t// for each shorthand gap, column-gap, or row-gap declaration\n\t\troot.walkDecls(gapPropertyRegExp, decl => {\n\t\t\t// insert a grid-* fallback declaration\n\t\t\tdecl.cloneBefore({\n\t\t\t\tprop: `grid-${decl.prop}`\n\t\t\t});\n\n\t\t\t// conditionally remove the original declaration\n\t\t\tif (!preserve) {\n\t\t\t\tdecl.remove();\n\t\t\t}\n\t\t})\n\t};\n});\n"], "names": ["gapPropertyRegExp", "postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "cloneBefore", "prop", "remove"], "mappings": ";;AAGA,MAAMA,iBAAiB,GAAG,6BAA1B;AAEA,YAAeC,OAAO,CAACC,MAAR,CAAe,wBAAf,EAAyCC,IAAI,IAAI;QACzDC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,IAAvE;SAEOG,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeR,iBAAf,EAAkCS,IAAI,IAAI;;MAEzCA,IAAI,CAACC,WAAL,CAAiB;QAChBC,IAAI,EAAG,QAAOF,IAAI,CAACE,IAAK;OADzB,EAFyC;;UAOrC,CAACP,QAAL,EAAe;QACdK,IAAI,CAACG,MAAL;;KARF;GAFD;CAHc,CAAf;;;;"}