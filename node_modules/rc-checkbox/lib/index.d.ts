import * as React from 'react';
export interface CheckboxChangeEvent {
    target: CheckboxChangeEventTarget;
    stopPropagation: () => void;
    preventDefault: () => void;
    nativeEvent: React.ChangeEvent<HTMLInputElement>['nativeEvent'];
}
export interface CheckboxChangeEventTarget extends CheckboxProps {
    checked: boolean;
}
export interface CheckboxRef {
    focus: (options?: FocusOptions) => void;
    blur: () => void;
    input: HTMLInputElement | null;
    nativeElement: HTMLElement | null;
}
export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
    prefixCls?: string;
    onChange?: (e: CheckboxChangeEvent) => void;
}
export declare const Checkbox: React.ForwardRefExoticComponent<CheckboxProps & React.RefAttributes<CheckboxRef>>;
export default Checkbox;
