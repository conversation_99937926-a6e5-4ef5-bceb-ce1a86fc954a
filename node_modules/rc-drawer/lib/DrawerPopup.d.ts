import type { CSSMotionProps } from 'rc-motion';
import * as React from 'react';
import type { DrawerPanelAccessibility, DrawerPanelEvents } from './DrawerPanel';
import type { DrawerClassNames, DrawerStyles } from './inter';
export type Placement = 'left' | 'right' | 'top' | 'bottom';
export interface PushConfig {
    distance?: number | string;
}
export interface DrawerPopupProps extends DrawerPanelEvents, DrawerPanelAccessibility {
    prefixCls: string;
    open?: boolean;
    inline?: boolean;
    push?: boolean | PushConfig;
    forceRender?: boolean;
    autoFocus?: boolean;
    keyboard?: boolean;
    rootClassName?: string;
    rootStyle?: React.CSSProperties;
    zIndex?: number;
    placement?: Placement;
    id?: string;
    className?: string;
    style?: React.CSSProperties;
    children?: React.ReactNode;
    width?: number | string;
    height?: number | string;
    mask?: boolean;
    maskClosable?: boolean;
    maskClassName?: string;
    maskStyle?: React.CSSProperties;
    motion?: CSSMotionProps | ((placement: Placement) => CSSMotionProps);
    maskMotion?: CSSMotionProps;
    afterOpenChange?: (open: boolean) => void;
    onClose?: (event: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>) => void;
    classNames?: DrawerClassNames;
    styles?: DrawerStyles;
    drawerRender?: (node: React.ReactNode) => React.ReactNode;
}
declare const RefDrawerPopup: React.ForwardRefExoticComponent<DrawerPopupProps & React.RefAttributes<HTMLDivElement>>;
export default RefDrawerPopup;
