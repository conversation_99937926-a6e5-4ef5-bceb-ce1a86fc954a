{"version": 3, "file": "index.cjs.js", "sources": ["lib/clone-rule.js", "lib/transform-border.js", "lib/transform-float.js", "lib/transform-inset.js", "lib/transform-resize.js", "lib/match-side.js", "lib/match-inset-prefix.js", "lib/clone-decl.js", "lib/transform-side.js", "lib/match-size.js", "lib/transform-size.js", "lib/transform-spacing.js", "lib/transform-text-align.js", "lib/split.js", "lib/transform-transition.js", "lib/match-supported-properties.js", "index.js"], "sourcesContent": ["import postcss from 'postcss';\n\nexport default (decl, dir) => {\n\tconst rule = Object(decl.parent).type === 'rule' ? decl.parent.clone({\n\t\traws: {}\n\t}).removeAll() : postcss.rule({ selector: '&' });\n\n\trule.selectors = rule.selectors.map(selector => `${selector}:dir(${dir})`);\n\n\treturn rule;\n};\n", "import cloneRule from './clone-rule';\n\nconst matchLogical = /^\\s*logical\\s+/i;\nconst matchLogicalBorder = /^border(-width|-style|-color)?$/i;\nconst matchLogicalBorderSide = /^border-(block|block-start|block-end|inline|inline-start|inline-end|start|end)(-(width|style|color))?$/i;\n\nexport default {\n\t// border\n\t'border': (decl, values, dir) => {\n\t\tconst isLogical = matchLogical.test(values[0]);\n\n\t\tif (isLogical) {\n\t\t\tvalues[0] = values[0].replace(matchLogical, '');\n\t\t}\n\n\t\tconst ltrDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[2] || values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[3] || values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[2] || values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\tvalue: values[3] || values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\treturn isLogical ? 1 === values.length\n\t\t\t? decl.clone({\n\t\t\t\tvalue: decl.value.replace(matchLogical, '')\n\t\t\t})\n\t\t\t: !values[3] || values[3] === values[1]\n\t\t\t\t? [\n\t\t\t\t\tdecl.clone({\n\t\t\t\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\t\t\tvalue: values[0]\n\t\t\t\t\t}),\n\t\t\t\t\tdecl.clone({\n\t\t\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\t\t\tvalue: values[3] || values[1] || values[0]\n\t\t\t\t\t}),\n\t\t\t\t\tdecl.clone({\n\t\t\t\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\t\t\tvalue: values[2] || values[0]\n\t\t\t\t\t}),\n\t\t\t\t\tdecl.clone({\n\t\t\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorder, '$1')}`,\n\t\t\t\t\t\tvalue: values[1] || values[0]\n\t\t\t\t\t})\n\t\t\t\t]\n\t\t\t\t: 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t\t\t]\n\t\t\t: null;\n\t},\n\n\t// border-block\n\t'border-block': (decl, values) => [\n\t\tdecl.clone({\n\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\tvalue: values[0]\n\t\t}),\n\t\tdecl.clone({\n\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\tvalue: values[0]\n\t\t})\n\t],\n\n\t// border-block-start\n\t'border-block-start': decl => {\n\t\tdecl.prop = 'border-top';\n\t},\n\n\t// border-block-end\n\t'border-block-end': decl => {\n\t\tdecl.prop = 'border-bottom';\n\t},\n\n\t// border-inline\n\t'border-inline': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\tconst isLTR = 1 === values.length || 2 === values.length && values[0] === values[1];\n\n\t\treturn isLTR ? ltrDecls : 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t];\n\t},\n\n\t// border-inline-start\n\t'border-inline-start': (decl, values, dir) => {\n\t\tconst ltrDecl = decl.clone({\n\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`\n\t\t});\n\n\t\tconst rtlDecl = decl.clone({\n\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`\n\t\t});\n\n\t\treturn 'ltr' === dir ? ltrDecl : 'rtl' === dir ? rtlDecl : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t\t];\n\t},\n\n\t// border-inline-end\n\t'border-inline-end': (decl, values, dir) => {\n\t\tconst ltrDecl = decl.clone({\n\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`\n\t\t});\n\n\t\tconst rtlDecl = decl.clone({\n\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`\n\t\t});\n\n\t\treturn 'ltr' === dir ? ltrDecl : 'rtl' === dir ? rtlDecl : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t\t];\n\t},\n\n\t// border-start\n\t'border-start': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-top${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\treturn 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t];\n\t},\n\n\t// border-end\n\t'border-end': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-right${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-bottom${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[0]\n\t\t\t}),\n\t\t\tdecl.clone({\n\t\t\t\tprop: `border-left${decl.prop.replace(matchLogicalBorderSide, '$2')}`,\n\t\t\t\tvalue: values[1] || values[0]\n\t\t\t})\n\t\t];\n\n\t\treturn 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t];\n\t}\n};\n", "import cloneRule from './clone-rule';\n\nexport default (decl, values, dir) => {\n\tconst lDecl = decl.clone({ value: 'left' });\n\tconst rDecl = decl.clone({ value: 'right' });\n\n\treturn /^inline-start$/i.test(decl.value) ? 'ltr' === dir ? lDecl : 'rtl' === dir ? rDecl : [\n\t\tcloneRule(decl, 'ltr').append(lDecl),\n\t\tcloneRule(decl, 'rtl').append(rDecl)\n\t] : /^inline-end$/i.test(decl.value) ? 'ltr' === dir ? rDecl : 'rtl' === dir ? lDecl : [\n\t\tcloneRule(decl, 'ltr').append(rDecl),\n\t\tcloneRule(decl, 'rtl').append(lDecl)\n\t] : null;\n};\n", "import cloneRule from './clone-rule';\n\nexport default (decl, values, dir) => {\n\tif ('logical' !== values[0]) {\n\t\treturn [\n\t\t\tdecl.clone({ prop: 'top', value: values[0] }),\n\t\t\tdecl.clone({ prop: 'right', value: values[1] || values[0] }),\n\t\t\tdecl.clone({ prop: 'bottom', value: values[2] || values[0] }),\n\t\t\tdecl.clone({ prop: 'left', value: values[3] || values[1] || values[0] })\n\t\t];\n\t}\n\n\tconst isLTR = !values[4] || values[4] === values[2];\n\n\tconst ltrDecls = [\n\t\tdecl.clone({ prop: 'top', value: values[1] }),\n\t\tdecl.clone({ prop: 'left', value: values[2] || values[1] }),\n\t\tdecl.clone({ prop: 'bottom', value: values[3] || values[1] }),\n\t\tdecl.clone({ prop: 'right', value: values[4] || values[2] || values[1] })\n\t];\n\n\tconst rtlDecls = [\n\t\tdecl.clone({ prop: 'top', value: values[1] }),\n\t\tdecl.clone({ prop: 'right', value: values[2] || values[1] }),\n\t\tdecl.clone({ prop: 'bottom', value: values[3] || values[1] }),\n\t\tdecl.clone({ prop: 'left', value: values[4] || values[2] || values[1] })\n\t];\n\n\treturn isLTR || 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t];\n}\n", "export default decl => /^block$/i.test(decl.value)\n\t? decl.clone({ value: 'vertical' })\n\t: /^inline$/i.test(decl.value)\n\t\t? decl.clone({ value: 'horizontal' })\n\t\t: null;\n", "export default /^(inset|margin|padding)(?:-(block|block-start|block-end|inline|inline-start|inline-end|start|end))$/i;\n", "export default /^inset-/i;\n", "import matchSide from './match-side';\nimport matchInsetPrefix from './match-inset-prefix';\n\nexport default (decl, suffix, value) => decl.clone({\n\tprop: `${decl.prop.replace(matchSide, '$1')}${suffix}`.replace(matchInsetPrefix, ''),\n\tvalue\n});\n", "import cloneDecl from './clone-decl';\nimport cloneRule from './clone-rule';\nimport matchSide from './match-side';\n\nimport matchInsetPrefix from './match-inset-prefix';\n\nexport default {\n\t// inset-block, margin-block, padding-block\n\t'block': (decl, values) => [\n\t\tcloneDecl(decl, '-top', values[0]),\n\t\tcloneDecl(decl, '-bottom', values[1] || values[0])\n\t],\n\n\t// inset-block-start, margin-block-start, padding-block-start\n\t'block-start': decl => {\n\t\tdecl.prop = decl.prop.replace(matchSide, '$1-top').replace(matchInsetPrefix, '');\n\t},\n\n\t// inset-block-end, margin-block-end, padding-block-end\n\t'block-end': decl => {\n\t\tdecl.prop = decl.prop.replace(matchSide, '$1-bottom').replace(matchInsetPrefix, '');\n\t},\n\n\t// inset-inline, margin-inline, padding-inline\n\t'inline': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tcloneDecl(decl, '-left', values[0]),\n\t\t\tcloneDecl(decl, '-right', values[1] || values[0])\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tcloneDecl(decl, '-right', values[0]),\n\t\t\tcloneDecl(decl, '-left', values[1] || values[0])\n\t\t];\n\n\t\tconst isLTR = 1 === values.length || 2 === values.length && values[0] === values[1];\n\n\t\treturn isLTR ? ltrDecls : 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls),\n\t\t];\n\t},\n\n\t// inset-inline-start, margin-inline-start, padding-inline-start\n\t'inline-start': (decl, values, dir) => {\n\t\tconst ltrDecl = cloneDecl(decl, '-left', decl.value);\n\t\tconst rtlDecl = cloneDecl(decl, '-right', decl.value);\n\n\t\treturn 'ltr' === dir ? ltrDecl : 'rtl' === dir ? rtlDecl : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t\t];\n\t},\n\n\t// inset-inline-end, margin-inline-end, padding-inline-end\n\t'inline-end': (decl, values, dir) => {\n\t\tconst ltrDecl = cloneDecl(decl, '-right', decl.value);\n\t\tconst rtlDecl = cloneDecl(decl, '-left', decl.value);\n\n\t\treturn 'ltr' === dir ? ltrDecl : 'rtl' === dir ? rtlDecl : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t\t];\n\t},\n\n\t// inset-start, margin-start, padding-start\n\t'start': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tcloneDecl(decl, '-top', values[0]),\n\t\t\tcloneDecl(decl, '-left', values[1] || values[0])\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tcloneDecl(decl, '-top', values[0]),\n\t\t\tcloneDecl(decl, '-right', values[1] || values[0])\n\t\t];\n\n\t\treturn 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t];\n\t},\n\n\t// inset-end, margin-end, padding-end\n\t'end': (decl, values, dir) => {\n\t\tconst ltrDecls = [\n\t\t\tcloneDecl(decl, '-bottom', values[0]),\n\t\t\tcloneDecl(decl, '-right', values[1] || values[0])\n\t\t];\n\n\t\tconst rtlDecls = [\n\t\t\tcloneDecl(decl, '-bottom', values[0]),\n\t\t\tcloneDecl(decl, '-left', values[1] || values[0])\n\t\t];\n\n\t\treturn 'ltr' === dir ? ltrDecls : 'rtl' === dir ? rtlDecls : [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecls),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecls)\n\t\t];\n\t}\n}\n", "export default /^(min-|max-)?(block|inline)-(size)$/i;\n", "import matchSize from './match-size';\n\nexport default decl => {\n\tdecl.prop = decl.prop.replace(\n\t\tmatchSize,\n\t\t($0, minmax, flow) => `${minmax||''}${'block' === flow ? 'height' : 'width'}`\n\t);\n};\n", "import cloneRule from './clone-rule';\n\nexport default (decl, values, dir) => {\n\tif ('logical' !== values[0]) {\n\t\treturn null;\n\t}\n\n\tconst isLTR = !values[4] || values[4] === values[2];\n\n\tconst ltrDecl = decl.clone({\n\t\tvalue: [\n\t\t\tvalues[1],\n\t\t\tvalues[4] || values[2] || values[1],\n\t\t\tvalues[3] || values[1],\n\t\t\tvalues[2] || values[1]\n\t\t].join(' ')\n\t});\n\n\tconst rtlDecl = decl.clone({\n\t\tvalue: [\n\t\t\tvalues[1],\n\t\t\tvalues[2] || values[1],\n\t\t\tvalues[3] || values[1],\n\t\t\tvalues[4] || values[2] || values[1]\n\t\t].join(' ')\n\t});\n\n\treturn isLTR ? decl.clone({\n\t\tvalue: decl.value.replace(/^\\s*logical\\s+/i, '')\n\t}) : 'ltr' === dir ? ltrDecl : 'rtl' === dir ? rtlDecl : [\n\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t];\n};\n", "import cloneRule from './clone-rule';\n\nexport default (decl, values, dir) => {\n\tconst lDecl = decl.clone({ value: 'left' });\n\tconst rDecl = decl.clone({ value: 'right' });\n\n\treturn /^start$/i.test(decl.value) ? 'ltr' === dir ? lDecl : 'rtl' === dir ? rDecl : [\n\t\tcloneRule(decl, 'ltr').append(lDecl),\n\t\tcloneRule(decl, 'rtl').append(rDecl)\n\t] : /^end$/i.test(decl.value) ? 'ltr' === dir ? rDecl : 'rtl' === dir ? lDecl : [\n\t\tcloneRule(decl, 'ltr').append(rDecl),\n\t\tcloneRule(decl, 'rtl').append(lDecl)\n\t] : null;\n};\n", "export function splitByComma(string, isTrimmed) {\n\treturn splitByRegExp(string, /^,$/, isTrimmed);\n}\n\nexport function splitBySpace(string, isTrimmed) {\n\treturn splitByRegExp(string, /^\\s$/, isTrimmed);\n}\n\nexport function splitBySlash(string, isTrimmed) {\n\treturn splitByRegExp(string, /^\\/$/, isTrimmed);\n}\n\nfunction splitByRegExp(string, re, isTrimmed) {\n\tconst array = [];\n\tlet buffer = '';\n\tlet split = false;\n\tlet func = 0;\n\tlet i = -1;\n\n\twhile (++i < string.length) {\n\t\tconst char = string[i];\n\n\t\tif (char === '(') {\n\t\t\tfunc += 1;\n\t\t} else if (char === ')') {\n\t\t\tif (func > 0) {\n\t\t\t\tfunc -= 1;\n\t\t\t}\n\t\t} else if (func === 0) {\n\t\t\tif (re.test(char)) {\n\t\t\t\tsplit = true;\n\t\t\t}\n\t\t}\n\n\t\tif (split) {\n\t\t\tif (!isTrimmed || buffer.trim()) {\n\t\t\t\tarray.push(isTrimmed ? buffer.trim() : buffer);\n\t\t\t}\n\n\t\t\tif (!isTrimmed) {\n\t\t\t\tarray.push(char);\n\t\t\t}\n\n\t\t\tbuffer = '';\n\t\t\tsplit = false;\n\t\t} else {\n\t\t\tbuffer += char\n\t\t}\n\t}\n\n\tif (buffer !== '') {\n\t\tarray.push(isTrimmed ? buffer.trim() : buffer);\n\t}\n\n\treturn array;\n}\n", "import cloneRule from './clone-rule';\nimport { splitByComma, splitBySpace } from './split';\n\nexport default (decl, notValues, dir) => {\n\tconst ltrValues = [];\n\tconst rtlValues = [];\n\n\tsplitByComma(decl.value).forEach(value => {\n\t\tlet hasBeenSplit = false;\n\n\t\tsplitBySpace(value).forEach((word, index, words) => {\n\t\t\tif (word in valueMap) {\n\t\t\t\thasBeenSplit = true;\n\n\t\t\t\tvalueMap[word].ltr.forEach(replacement => {\n\t\t\t\t\tconst clone = words.slice();\n\n\t\t\t\t\tclone.splice(index, 1, replacement);\n\n\t\t\t\t\tif (ltrValues.length && !/^,$/.test(ltrValues[ltrValues.length - 1])) {\n\t\t\t\t\t\tltrValues.push(',');\n\t\t\t\t\t}\n\n\t\t\t\t\tltrValues.push(clone.join(''));\n\t\t\t\t});\n\n\t\t\t\tvalueMap[word].rtl.forEach(replacement => {\n\t\t\t\t\tconst clone = words.slice();\n\n\t\t\t\t\tclone.splice(index, 1, replacement);\n\n\t\t\t\t\tif (rtlValues.length && !/^,$/.test(rtlValues[rtlValues.length - 1])) {\n\t\t\t\t\t\trtlValues.push(',');\n\t\t\t\t\t}\n\n\t\t\t\t\trtlValues.push(clone.join(''));\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\tif (!hasBeenSplit) {\n\t\t\tltrValues.push(value);\n\t\t\trtlValues.push(value);\n\t\t}\n\t});\n\n\tconst ltrDecl = decl.clone({ value: ltrValues.join('') });\n\tconst rtlDecl = decl.clone({ value: rtlValues.join('') });\n\n\treturn ltrValues.length && 'ltr' === dir\n\t\t? ltrDecl\n\t: rtlValues.length && 'rtl' === dir\n\t\t? rtlDecl\n\t: ltrDecl.value !== rtlDecl.value\n\t\t? [\n\t\t\tcloneRule(decl, 'ltr').append(ltrDecl),\n\t\t\tcloneRule(decl, 'rtl').append(rtlDecl)\n\t\t]\n\t: null;\n};\n\nconst valueMap = {\n\t'border-block': { ltr: ['border-top', 'border-bottom'], rtl: ['border-top', 'border-bottom'] },\n\t'border-block-color': { ltr: ['border-top-color', 'border-bottom-color'], rtl: ['border-top-color', 'border-bottom-color'] },\n\t'border-block-end': { ltr: ['border-bottom'], rtl: ['border-bottom'] },\n\t'border-block-end-color': { ltr: ['border-bottom-color'], rtl: ['border-bottom-color'] },\n\t'border-block-end-style': { ltr: ['border-bottom-style'], rtl: ['border-bottom-style'] },\n\t'border-block-end-width': { ltr: ['border-bottom-width'], rtl: ['border-bottom-width'] },\n\t'border-block-start': { ltr: ['border-top'], rtl: ['border-top'] },\n\t'border-block-start-color': { ltr: ['border-top-color'], rtl: ['border-top-color'] },\n\t'border-block-start-style': { ltr: ['border-top-style'], rtl: ['border-top-style'] },\n\t'border-block-start-width': { ltr: ['border-top-width'], rtl: ['border-top-width'] },\n\t'border-block-style': { ltr: ['border-top-style', 'border-bottom-style'], rtl: ['border-top-style', 'border-bottom-style'] },\n\t'border-block-width': { ltr: ['border-top-width', 'border-bottom-width'], rtl: ['border-top-width', 'border-bottom-width'] },\n\t'border-end': { ltr: ['border-bottom', 'border-right'], rtl: ['border-bottom', 'border-left'] },\n\t'border-end-color': { ltr: ['border-bottom-color', 'border-right-color'], rtl: ['border-bottom-color', 'border-left-color'] },\n\t'border-end-style': { ltr: ['border-bottom-style', 'border-right-style'], rtl: ['border-bottom-style', 'border-left-style'] },\n\t'border-end-width': { ltr: ['border-bottom-width', 'border-right-width'], rtl: ['border-bottom-width', 'border-left-width'] },\n\t'border-inline': { ltr: ['border-left', 'border-right'], rtl: ['border-left', 'border-right'] },\n\t'border-inline-color': { ltr: ['border-left-color', 'border-right-color'], rtl: ['border-left-color', 'border-right-color'] },\n\t'border-inline-end': { ltr: ['border-right'], rtl: ['border-left'] },\n\t'border-inline-end-color': { ltr: ['border-right-color'], rtl: ['border-left-color'] },\n\t'border-inline-end-style': { ltr: ['border-right-style'], rtl: ['border-left-style'] },\n\t'border-inline-end-width': { ltr: ['border-right-width'], rtl: ['border-left-width'] },\n\t'border-inline-start': { ltr: ['border-left'], rtl: ['border-right'] },\n\t'border-inline-start-color': { ltr: ['border-left-color'], rtl: ['border-right-color'] },\n\t'border-inline-start-style': { ltr: ['border-left-style'], rtl: ['border-right-style'] },\n\t'border-inline-start-width': { ltr: ['border-left-width'], rtl: ['border-right-width'] },\n\t'border-inline-style': { ltr: ['border-left-style', 'border-right-style'], rtl: ['border-left-style', 'border-right-style'] },\n\t'border-inline-width': { ltr: ['border-left-width', 'border-right-width'], rtl: ['border-left-width', 'border-right-width'] },\n\t'border-start': { ltr: ['border-top', 'border-left'], rtl: ['border-top', 'border-right'] },\n\t'border-start-color': { ltr: ['border-top-color', 'border-left-color'], rtl: ['border-top-color', 'border-right-color'] },\n\t'border-start-style': { ltr: ['border-top-style', 'border-left-style'], rtl: ['border-top-style', 'border-right-style'] },\n\t'border-start-width': { ltr: ['border-top-width', 'border-left-width'], rtl: ['border-top-width', 'border-right-width'] },\n\t'block-size': { ltr: ['height'], rtl: ['height'] },\n\t'inline-size': { ltr: ['width'], rtl: ['width'] },\n\t'inset': { ltr: ['top', 'right', 'bottom', 'left'], rtl: ['top', 'right', 'bottom', 'left'] },\n\t'inset-block': { ltr: ['top', 'bottom'], rtl: ['top', 'bottom'] },\n\t'inset-block-start': { ltr: ['top'], rtl: ['top'] },\n\t'inset-block-end': { ltr: ['bottom'], rtl: ['bottom'] },\n\t'inset-end': { ltr: ['bottom', 'right'], rtl: ['bottom', 'left'] },\n\t'inset-inline': { ltr: ['left', 'right'], rtl: ['left', 'right'] },\n\t'inset-inline-start': { ltr: ['left'], rtl: ['right'] },\n\t'inset-inline-end': { ltr: ['right'], rtl: ['left'] },\n\t'inset-start': { ltr: ['top', 'left'], rtl: ['top', 'right'] },\n\t'margin-block': { ltr: ['margin-top', 'margin-bottom'], rtl: ['margin-top', 'margin-bottom'] },\n\t'margin-block-start': { ltr: ['margin-top'], rtl: ['margin-top'] },\n\t'margin-block-end': { ltr: ['margin-bottom'], rtl: ['margin-bottom'] },\n\t'margin-end': { ltr: ['margin-bottom', 'margin-right'], rtl: ['margin-bottom', 'margin-left'] },\n\t'margin-inline': { ltr: ['margin-left', 'margin-right'], rtl: ['margin-left', 'margin-right'] },\n\t'margin-inline-start': { ltr: ['margin-left'], rtl: ['margin-right'] },\n\t'margin-inline-end': { ltr: ['margin-right'], rtl: ['margin-left'] },\n\t'margin-start': { ltr: ['margin-top', 'margin-left'], rtl: ['margin-top', 'margin-right'] },\n\t'padding-block': { ltr: ['padding-top', 'padding-bottom'], rtl: ['padding-top', 'padding-bottom'] },\n\t'padding-block-start': { ltr: ['padding-top'], rtl: ['padding-top'] },\n\t'padding-block-end': { ltr: ['padding-bottom'], rtl: ['padding-bottom'] },\n\t'padding-end': { ltr: ['padding-bottom', 'padding-right'], rtl: ['padding-bottom', 'padding-left'] },\n\t'padding-inline': { ltr: ['padding-left', 'padding-right'], rtl: ['padding-left', 'padding-right'] },\n\t'padding-inline-start': { ltr: ['padding-left'], rtl: ['padding-right'] },\n\t'padding-inline-end': { ltr: ['padding-right'], rtl: ['padding-left'] },\n\t'padding-start': { ltr: ['padding-top', 'padding-left'], rtl: ['padding-top', 'padding-right'] },\n};\n", "export default /^(?:(inset|margin|padding)(?:-(block|block-start|block-end|inline|inline-start|inline-end|start|end))|(min-|max-)?(block|inline)-(size))$/i;\n", "// tooling\nimport postcss from 'postcss';\n\n// internal tooling\nimport transformBorder from './lib/transform-border';\nimport transformFloat from './lib/transform-float';\nimport transformInset from './lib/transform-inset';\nimport transformResize from './lib/transform-resize';\nimport transformSide from './lib/transform-side';\nimport transformSize from './lib/transform-size';\nimport transformSpacing from './lib/transform-spacing';\nimport transformTextAlign from './lib/transform-text-align';\nimport transformTransition from './lib/transform-transition';\nimport matchSupportedProperties from './lib/match-supported-properties';\nimport { splitBySlash, splitBySpace } from './lib/split';\n\n// supported transforms\nconst transforms = {\n\t'border': transformBorder['border'], 'border-width': transformBorder['border'], 'border-style': transformBorder['border'], 'border-color': transformBorder['border'],\n\t'border-block': transformBorder['border-block'], 'border-block-width': transformBorder['border-block'], 'border-block-style': transformBorder['border-block'], 'border-block-color': transformBorder['border-block'],\n\t'border-block-start': transformBorder['border-block-start'], 'border-block-start-width': transformBorder['border-block-start'], 'border-block-start-style': transformBorder['border-block-start'], 'border-block-start-color': transformBorder['border-block-start'],\n\t'border-block-end': transformBorder['border-block-end'], 'border-block-end-width': transformBorder['border-block-end'], 'border-block-end-style': transformBorder['border-block-end'], 'border-block-end-color': transformBorder['border-block-end'],\n\t'border-inline': transformBorder['border-inline'], 'border-inline-width': transformBorder['border-inline'], 'border-inline-style': transformBorder['border-inline'], 'border-inline-color': transformBorder['border-inline'],\n\t'border-inline-start': transformBorder['border-inline-start'], 'border-inline-start-width': transformBorder['border-inline-start'], 'border-inline-start-style': transformBorder['border-inline-start'], 'border-inline-start-color': transformBorder['border-inline-start'],\n\t'border-inline-end': transformBorder['border-inline-end'], 'border-inline-end-width': transformBorder['border-inline-end'], 'border-inline-end-style': transformBorder['border-inline-end'], 'border-inline-end-color': transformBorder['border-inline-end'],\n\t'border-start': transformBorder['border-start'], 'border-start-width': transformBorder['border-start'], 'border-start-style': transformBorder['border-start'], 'border-start-color': transformBorder['border-start'],\n\t'border-end': transformBorder['border-end'], 'border-end-width': transformBorder['border-end'], 'border-end-style': transformBorder['border-end'], 'border-end-color': transformBorder['border-end'],\n\t'clear': transformFloat,\n\t'inset': transformInset,\n\t'margin': transformSpacing,\n\t'padding': transformSpacing,\n\t'block': transformSide['block'],\n\t'block-start': transformSide['block-start'],\n\t'block-end': transformSide['block-end'],\n\t'inline': transformSide['inline'],\n\t'inline-start': transformSide['inline-start'],\n\t'inline-end': transformSide['inline-end'],\n\t'start': transformSide['start'],\n\t'end': transformSide['end'],\n\t'float': transformFloat,\n\t'resize': transformResize,\n\t'size': transformSize,\n\t'text-align': transformTextAlign,\n\t'transition': transformTransition,\n\t'transition-property': transformTransition\n};\n\n// properties that will be split by slash\nconst splitBySlashPropRegExp = /^border(-block|-inline|-start|-end)?(-width|-style|-color)?$/i;\n\n// plugin\nexport default postcss.plugin('postcss-logical-properties', opts => {\n\tconst preserve = Boolean(Object(opts).preserve);\n\tconst dir = !preserve && typeof Object(opts).dir === 'string'\n\t\t? /^rtl$/i.test(opts.dir)\n\t\t\t? 'rtl'\n\t\t: 'ltr'\n\t: false;\n\n\treturn root => {\n\t\troot.walkDecls(decl => {\n\t\t\tconst parent = decl.parent;\n\t\t\tconst values = splitBySlashPropRegExp.test(decl.prop) ? splitBySlash(decl.value, true) : splitBySpace(decl.value, true);\n\t\t\tconst prop = decl.prop.replace(matchSupportedProperties, '$2$5').toLowerCase();\n\n\t\t\tif (prop in transforms) {\n\t\t\t\tconst replacer = transforms[prop](decl, values, dir);\n\n\t\t\t\tif (replacer) {\n\t\t\t\t\t[].concat(replacer).forEach(replacement => {\n\t\t\t\t\t\tif (replacement.type === 'rule') {\n\t\t\t\t\t\t\tparent.before(replacement);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdecl.before(replacement);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (!preserve) {\n\t\t\t\t\t\tdecl.remove();\n\n\t\t\t\t\t\tif (!parent.nodes.length) {\n\t\t\t\t\t\t\tparent.remove();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n"], "names": ["decl", "dir", "rule", "Object", "parent", "type", "clone", "raws", "removeAll", "postcss", "selector", "selectors", "map", "matchLogical", "matchLogicalBorder", "matchLogicalBorderSide", "values", "isLogical", "test", "replace", "ltrDecls", "prop", "value", "rtlDecls", "length", "cloneRule", "append", "isLTR", "ltrDecl", "rtlDecl", "lDecl", "rDecl", "suffix", "matchSide", "matchInsetPrefix", "cloneDecl", "matchSize", "$0", "minmax", "flow", "join", "splitByComma", "string", "isTrimmed", "splitByRegExp", "splitBySpace", "splitBySlash", "re", "array", "buffer", "split", "func", "i", "char", "trim", "push", "notValues", "ltrValues", "rtlValues", "for<PERSON>ach", "hasBeenSplit", "word", "index", "words", "valueMap", "ltr", "replacement", "slice", "splice", "rtl", "transforms", "transformBorder", "transformFloat", "transformInset", "transformSpacing", "transformSide", "transformResize", "transformSize", "transformTextAlign", "transformTransition", "splitBySlashPropRegExp", "plugin", "opts", "preserve", "Boolean", "root", "walkDecls", "matchSupportedProperties", "toLowerCase", "replacer", "concat", "before", "remove", "nodes"], "mappings": ";;;;;;AAEA,iBAAe,CAACA,IAAD,EAAOC,GAAP,KAAe;QACvBC,IAAI,GAAGC,MAAM,CAACH,IAAI,CAACI,MAAN,CAAN,CAAoBC,IAApB,KAA6B,MAA7B,GAAsCL,IAAI,CAACI,MAAL,CAAYE,KAAZ,CAAkB;IACpEC,IAAI,EAAE;GAD4C,EAEhDC,SAFgD,EAAtC,GAEIC,OAAO,CAACP,IAAR,CAAa;IAAEQ,QAAQ,EAAE;GAAzB,CAFjB;EAIAR,IAAI,CAACS,SAAL,GAAiBT,IAAI,CAACS,SAAL,CAAeC,GAAf,CAAmBF,QAAQ,IAAK,GAAEA,QAAS,QAAOT,GAAI,GAAtD,CAAjB;SAEOC,IAAP;CAPD;;ACAA,MAAMW,YAAY,GAAG,iBAArB;AACA,MAAMC,kBAAkB,GAAG,kCAA3B;AACA,MAAMC,sBAAsB,GAAG,yGAA/B;AAEA,sBAAe;;YAEJ,CAACf,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAC1BgB,SAAS,GAAGJ,YAAY,CAACK,IAAb,CAAkBF,MAAM,CAAC,CAAD,CAAxB,CAAlB;;QAEIC,SAAJ,EAAe;MACdD,MAAM,CAAC,CAAD,CAAN,GAAYA,MAAM,CAAC,CAAD,CAAN,CAAUG,OAAV,CAAkBN,YAAlB,EAAgC,EAAhC,CAAZ;;;UAGKO,QAAQ,GAAG,CAChBpB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADrD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADtD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,EAShBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADxD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CATgB,EAahBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADvD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;KAFxC,CAbgB,CAAjB;UAmBMO,QAAQ,GAAG,CAChBvB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADrD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADvD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,EAShBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADxD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CATgB,EAahBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADtD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;KAFxC,CAbgB,CAAjB;WAmBOC,SAAS,GAAG,MAAMD,MAAM,CAACQ,MAAb,GAChBxB,IAAI,CAACM,KAAL,CAAW;MACZgB,KAAK,EAAEtB,IAAI,CAACsB,KAAL,CAAWH,OAAX,CAAmBN,YAAnB,EAAiC,EAAjC;KADN,CADgB,GAIhB,CAACG,MAAM,CAAC,CAAD,CAAP,IAAcA,MAAM,CAAC,CAAD,CAAN,KAAcA,MAAM,CAAC,CAAD,CAAlC,GACC,CACDhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADrD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADC,EAKDhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADvD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;KAFxC,CALC,EASDhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADxD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CATC,EAaDhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBL,kBAAlB,EAAsC,IAAtC,CAA4C,EADtD;MAEVQ,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CAbC,CADD,GAmBC,UAAUf,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CACvDE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CADuD,EAEvDK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAFuD,CAvB1C,GA2Bb,IA3BH;GA/Ca;;kBA8EE,CAACvB,IAAD,EAAOgB,MAAP,KAAkB,CACjChB,IAAI,CAACM,KAAL,CAAW;IACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EADzD;IAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;GAFd,CADiC,EAKjChB,IAAI,CAACM,KAAL,CAAW;IACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD5D;IAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;GAFd,CALiC,CA9EpB;;wBA0FQhB,IAAI,IAAI;IAC7BA,IAAI,CAACqB,IAAL,GAAY,YAAZ;GA3Fa;;sBA+FMrB,IAAI,IAAI;IAC3BA,IAAI,CAACqB,IAAL,GAAY,eAAZ;GAhGa;;mBAoGG,CAACrB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UACjCmB,QAAQ,GAAG,CAChBpB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD1D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD3D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;UAWMO,QAAQ,GAAG,CAChBvB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD3D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD1D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;UAWMW,KAAK,GAAG,MAAMX,MAAM,CAACQ,MAAb,IAAuB,MAAMR,MAAM,CAACQ,MAAb,IAAuBR,MAAM,CAAC,CAAD,CAAN,KAAcA,MAAM,CAAC,CAAD,CAAhF;WAEOW,KAAK,GAAGP,QAAH,GAAc,UAAUnB,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC/EE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD+E,EAE/EK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF+E,CAAhF;GA7Ha;;yBAoIS,CAACvB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UACvC2B,OAAO,GAAG5B,IAAI,CAACM,KAAL,CAAW;MAC1Be,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD;KADrD,CAAhB;UAIMc,OAAO,GAAG7B,IAAI,CAACM,KAAL,CAAW;MAC1Be,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD;KADtD,CAAhB;WAIO,UAAUd,GAAV,GAAgB2B,OAAhB,GAA0B,UAAU3B,GAAV,GAAgB4B,OAAhB,GAA0B,CAC1DJ,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CAD0D,EAE1DH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAF0D,CAA3D;GA7Ia;;uBAoJO,CAAC7B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UACrC2B,OAAO,GAAG5B,IAAI,CAACM,KAAL,CAAW;MAC1Be,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD;KADtD,CAAhB;UAIMc,OAAO,GAAG7B,IAAI,CAACM,KAAL,CAAW;MAC1Be,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD;KADrD,CAAhB;WAIO,UAAUd,GAAV,GAAgB2B,OAAhB,GAA0B,UAAU3B,GAAV,GAAgB4B,OAAhB,GAA0B,CAC1DJ,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CAD0D,EAE1DH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAF0D,CAA3D;GA7Ja;;kBAoKE,CAAC7B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAChCmB,QAAQ,GAAG,CAChBpB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EADzD;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD1D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;UAWMO,QAAQ,GAAG,CAChBvB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,aAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EADzD;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD3D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;WAWO,UAAUf,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC5DE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD4D,EAE5DK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF4D,CAA7D;GA3La;;gBAkMA,CAACvB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAC9BmB,QAAQ,GAAG,CAChBpB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD5D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,eAAcrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD3D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;UAWMO,QAAQ,GAAG,CAChBvB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,gBAAerB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD5D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD;KAFd,CADgB,EAKhBhB,IAAI,CAACM,KAAL,CAAW;MACVe,IAAI,EAAG,cAAarB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBJ,sBAAlB,EAA0C,IAA1C,CAAgD,EAD1D;MAEVO,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAF3B,CALgB,CAAjB;WAWO,UAAUf,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC5DE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD4D,EAE5DK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF4D,CAA7D;;CAzNF;;ACJA,sBAAe,CAACvB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;QAC/B6B,KAAK,GAAG9B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAE;GAApB,CAAd;QACMS,KAAK,GAAG/B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAE;GAApB,CAAd;SAEO,kBAAkBJ,IAAlB,CAAuBlB,IAAI,CAACsB,KAA5B,IAAqC,UAAUrB,GAAV,GAAgB6B,KAAhB,GAAwB,UAAU7B,GAAV,GAAgB8B,KAAhB,GAAwB,CAC3FN,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BI,KAA9B,CAD2F,EAE3FL,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BK,KAA9B,CAF2F,CAArF,GAGH,gBAAgBb,IAAhB,CAAqBlB,IAAI,CAACsB,KAA1B,IAAmC,UAAUrB,GAAV,GAAgB8B,KAAhB,GAAwB,UAAU9B,GAAV,GAAgB6B,KAAhB,GAAwB,CACtFL,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BK,KAA9B,CADsF,EAEtFN,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BI,KAA9B,CAFsF,CAAnF,GAGA,IANJ;CAJD;;ACAA,sBAAe,CAAC9B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;MACjC,cAAce,MAAM,CAAC,CAAD,CAAxB,EAA6B;WACrB,CACNhB,IAAI,CAACM,KAAL,CAAW;MAAEe,IAAI,EAAE,KAAR;MAAeC,KAAK,EAAEN,MAAM,CAAC,CAAD;KAAvC,CADM,EAENhB,IAAI,CAACM,KAAL,CAAW;MAAEe,IAAI,EAAE,OAAR;MAAiBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAAtD,CAFM,EAGNhB,IAAI,CAACM,KAAL,CAAW;MAAEe,IAAI,EAAE,QAAR;MAAkBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;KAAvD,CAHM,EAINhB,IAAI,CAACM,KAAL,CAAW;MAAEe,IAAI,EAAE,MAAR;MAAgBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;KAAlE,CAJM,CAAP;;;QAQKW,KAAK,GAAG,CAACX,MAAM,CAAC,CAAD,CAAP,IAAcA,MAAM,CAAC,CAAD,CAAN,KAAcA,MAAM,CAAC,CAAD,CAAhD;QAEMI,QAAQ,GAAG,CAChBpB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,KAAR;IAAeC,KAAK,EAAEN,MAAM,CAAC,CAAD;GAAvC,CADgB,EAEhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,MAAR;IAAgBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;GAArD,CAFgB,EAGhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,QAAR;IAAkBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;GAAvD,CAHgB,EAIhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,OAAR;IAAiBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;GAAnE,CAJgB,CAAjB;QAOMO,QAAQ,GAAG,CAChBvB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,KAAR;IAAeC,KAAK,EAAEN,MAAM,CAAC,CAAD;GAAvC,CADgB,EAEhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,OAAR;IAAiBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;GAAtD,CAFgB,EAGhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,QAAR;IAAkBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD;GAAvD,CAHgB,EAIhBhB,IAAI,CAACM,KAAL,CAAW;IAAEe,IAAI,EAAE,MAAR;IAAgBC,KAAK,EAAEN,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD;GAAlE,CAJgB,CAAjB;SAOOW,KAAK,IAAI,UAAU1B,GAAnB,GAAyBmB,QAAzB,GAAoC,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CACrEE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CADqE,EAErEK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAFqE,CAAtE;CA1BD;;ACFA,uBAAevB,IAAI,IAAI,WAAWkB,IAAX,CAAgBlB,IAAI,CAACsB,KAArB,IACpBtB,IAAI,CAACM,KAAL,CAAW;EAAEgB,KAAK,EAAE;CAApB,CADoB,GAEpB,YAAYJ,IAAZ,CAAiBlB,IAAI,CAACsB,KAAtB,IACCtB,IAAI,CAACM,KAAL,CAAW;EAAEgB,KAAK,EAAE;CAApB,CADD,GAEC,IAJJ;;ACAA,gBAAe,sGAAf;;ACAA,uBAAe,UAAf;;ACGA,iBAAe,CAACtB,IAAD,EAAOgC,MAAP,EAAeV,KAAf,KAAyBtB,IAAI,CAACM,KAAL,CAAW;EAClDe,IAAI,EAAG,GAAErB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBc,SAAlB,EAA6B,IAA7B,CAAmC,GAAED,MAAO,EAA/C,CAAiDb,OAAjD,CAAyDe,gBAAzD,EAA2E,EAA3E,CAD4C;EAElDZ;CAFuC,CAAxC;;ACGA,oBAAe;;WAEL,CAACtB,IAAD,EAAOgB,MAAP,KAAkB,CAC1BmB,SAAS,CAACnC,IAAD,EAAO,MAAP,EAAegB,MAAM,CAAC,CAAD,CAArB,CADiB,EAE1BmB,SAAS,CAACnC,IAAD,EAAO,SAAP,EAAkBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAArC,CAFiB,CAFb;;iBAQChB,IAAI,IAAI;IACtBA,IAAI,CAACqB,IAAL,GAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBc,SAAlB,EAA6B,QAA7B,EAAuCd,OAAvC,CAA+Ce,gBAA/C,EAAiE,EAAjE,CAAZ;GATa;;eAaDlC,IAAI,IAAI;IACpBA,IAAI,CAACqB,IAAL,GAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBc,SAAlB,EAA6B,WAA7B,EAA0Cd,OAA1C,CAAkDe,gBAAlD,EAAoE,EAApE,CAAZ;GAda;;YAkBJ,CAAClC,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAC1BmB,QAAQ,GAAG,CAChBe,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBgB,MAAM,CAAC,CAAD,CAAtB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAApC,CAFO,CAAjB;UAKMO,QAAQ,GAAG,CAChBY,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBgB,MAAM,CAAC,CAAD,CAAvB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnC,CAFO,CAAjB;UAKMW,KAAK,GAAG,MAAMX,MAAM,CAACQ,MAAb,IAAuB,MAAMR,MAAM,CAACQ,MAAb,IAAuBR,MAAM,CAAC,CAAD,CAAN,KAAcA,MAAM,CAAC,CAAD,CAAhF;WAEOW,KAAK,GAAGP,QAAH,GAAc,UAAUnB,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC/EE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD+E,EAE/EK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF+E,CAAhF;GA/Ba;;kBAsCE,CAACvB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAChC2B,OAAO,GAAGO,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBA,IAAI,CAACsB,KAArB,CAAzB;UACMO,OAAO,GAAGM,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBA,IAAI,CAACsB,KAAtB,CAAzB;WAEO,UAAUrB,GAAV,GAAgB2B,OAAhB,GAA0B,UAAU3B,GAAV,GAAgB4B,OAAhB,GAA0B,CAC1DJ,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CAD0D,EAE1DH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAF0D,CAA3D;GA1Ca;;gBAiDA,CAAC7B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UAC9B2B,OAAO,GAAGO,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBA,IAAI,CAACsB,KAAtB,CAAzB;UACMO,OAAO,GAAGM,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBA,IAAI,CAACsB,KAArB,CAAzB;WAEO,UAAUrB,GAAV,GAAgB2B,OAAhB,GAA0B,UAAU3B,GAAV,GAAgB4B,OAAhB,GAA0B,CAC1DJ,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CAD0D,EAE1DH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAF0D,CAA3D;GArDa;;WA4DL,CAAC7B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UACzBmB,QAAQ,GAAG,CAChBe,SAAS,CAACnC,IAAD,EAAO,MAAP,EAAegB,MAAM,CAAC,CAAD,CAArB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnC,CAFO,CAAjB;UAKMO,QAAQ,GAAG,CAChBY,SAAS,CAACnC,IAAD,EAAO,MAAP,EAAegB,MAAM,CAAC,CAAD,CAArB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAApC,CAFO,CAAjB;WAKO,UAAUf,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC5DE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD4D,EAE5DK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF4D,CAA7D;GAvEa;;SA8EP,CAACvB,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;UACvBmB,QAAQ,GAAG,CAChBe,SAAS,CAACnC,IAAD,EAAO,SAAP,EAAkBgB,MAAM,CAAC,CAAD,CAAxB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,QAAP,EAAiBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAApC,CAFO,CAAjB;UAKMO,QAAQ,GAAG,CAChBY,SAAS,CAACnC,IAAD,EAAO,SAAP,EAAkBgB,MAAM,CAAC,CAAD,CAAxB,CADO,EAEhBmB,SAAS,CAACnC,IAAD,EAAO,OAAP,EAAgBgB,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnC,CAFO,CAAjB;WAKO,UAAUf,GAAV,GAAgBmB,QAAhB,GAA2B,UAAUnB,GAAV,GAAgBsB,QAAhB,GAA2B,CAC5DE,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BN,QAA9B,CAD4D,EAE5DK,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BH,QAA9B,CAF4D,CAA7D;;CAzFF;;ACNA,gBAAe,sCAAf;;ACEA,qBAAevB,IAAI,IAAI;EACtBA,IAAI,CAACqB,IAAL,GAAYrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CACXiB,SADW,EAEX,CAACC,EAAD,EAAKC,MAAL,EAAaC,IAAb,KAAuB,GAAED,MAAM,IAAE,EAAG,GAAE,YAAYC,IAAZ,GAAmB,QAAnB,GAA8B,OAAQ,EAFjE,CAAZ;CADD;;ACAA,wBAAe,CAACvC,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;MACjC,cAAce,MAAM,CAAC,CAAD,CAAxB,EAA6B;WACrB,IAAP;;;QAGKW,KAAK,GAAG,CAACX,MAAM,CAAC,CAAD,CAAP,IAAcA,MAAM,CAAC,CAAD,CAAN,KAAcA,MAAM,CAAC,CAAD,CAAhD;QAEMY,OAAO,GAAG5B,IAAI,CAACM,KAAL,CAAW;IAC1BgB,KAAK,EAAE,CACNN,MAAM,CAAC,CAAD,CADA,EAENA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD,CAF1B,EAGNA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAHb,EAINA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAJb,EAKLwB,IALK,CAKA,GALA;GADQ,CAAhB;QASMX,OAAO,GAAG7B,IAAI,CAACM,KAAL,CAAW;IAC1BgB,KAAK,EAAE,CACNN,MAAM,CAAC,CAAD,CADA,EAENA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAFb,EAGNA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAHb,EAINA,MAAM,CAAC,CAAD,CAAN,IAAaA,MAAM,CAAC,CAAD,CAAnB,IAA0BA,MAAM,CAAC,CAAD,CAJ1B,EAKLwB,IALK,CAKA,GALA;GADQ,CAAhB;SASOb,KAAK,GAAG3B,IAAI,CAACM,KAAL,CAAW;IACzBgB,KAAK,EAAEtB,IAAI,CAACsB,KAAL,CAAWH,OAAX,CAAmB,iBAAnB,EAAsC,EAAtC;GADO,CAAH,GAEP,UAAUlB,GAAV,GAAgB2B,OAAhB,GAA0B,UAAU3B,GAAV,GAAgB4B,OAAhB,GAA0B,CACxDJ,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CADwD,EAExDH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAFwD,CAFzD;CAzBD;;ACAA,0BAAe,CAAC7B,IAAD,EAAOgB,MAAP,EAAef,GAAf,KAAuB;QAC/B6B,KAAK,GAAG9B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAE;GAApB,CAAd;QACMS,KAAK,GAAG/B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAE;GAApB,CAAd;SAEO,WAAWJ,IAAX,CAAgBlB,IAAI,CAACsB,KAArB,IAA8B,UAAUrB,GAAV,GAAgB6B,KAAhB,GAAwB,UAAU7B,GAAV,GAAgB8B,KAAhB,GAAwB,CACpFN,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BI,KAA9B,CADoF,EAEpFL,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BK,KAA9B,CAFoF,CAA9E,GAGH,SAASb,IAAT,CAAclB,IAAI,CAACsB,KAAnB,IAA4B,UAAUrB,GAAV,GAAgB8B,KAAhB,GAAwB,UAAU9B,GAAV,GAAgB6B,KAAhB,GAAwB,CAC/EL,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BK,KAA9B,CAD+E,EAE/EN,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BI,KAA9B,CAF+E,CAA5E,GAGA,IANJ;CAJD;;ACFO,SAASW,YAAT,CAAsBC,MAAtB,EAA8BC,SAA9B,EAAyC;SACxCC,aAAa,CAACF,MAAD,EAAS,KAAT,EAAgBC,SAAhB,CAApB;;AAGD,AAAO,SAASE,YAAT,CAAsBH,MAAtB,EAA8BC,SAA9B,EAAyC;SACxCC,aAAa,CAACF,MAAD,EAAS,MAAT,EAAiBC,SAAjB,CAApB;;AAGD,AAAO,SAASG,YAAT,CAAsBJ,MAAtB,EAA8BC,SAA9B,EAAyC;SACxCC,aAAa,CAACF,MAAD,EAAS,MAAT,EAAiBC,SAAjB,CAApB;;;AAGD,SAASC,aAAT,CAAuBF,MAAvB,EAA+BK,EAA/B,EAAmCJ,SAAnC,EAA8C;QACvCK,KAAK,GAAG,EAAd;MACIC,MAAM,GAAG,EAAb;MACIC,KAAK,GAAG,KAAZ;MACIC,IAAI,GAAG,CAAX;MACIC,CAAC,GAAG,CAAC,CAAT;;SAEO,EAAEA,CAAF,GAAMV,MAAM,CAAClB,MAApB,EAA4B;UACrB6B,IAAI,GAAGX,MAAM,CAACU,CAAD,CAAnB;;QAEIC,IAAI,KAAK,GAAb,EAAkB;MACjBF,IAAI,IAAI,CAAR;KADD,MAEO,IAAIE,IAAI,KAAK,GAAb,EAAkB;UACpBF,IAAI,GAAG,CAAX,EAAc;QACbA,IAAI,IAAI,CAAR;;KAFK,MAIA,IAAIA,IAAI,KAAK,CAAb,EAAgB;UAClBJ,EAAE,CAAC7B,IAAH,CAAQmC,IAAR,CAAJ,EAAmB;QAClBH,KAAK,GAAG,IAAR;;;;QAIEA,KAAJ,EAAW;UACN,CAACP,SAAD,IAAcM,MAAM,CAACK,IAAP,EAAlB,EAAiC;QAChCN,KAAK,CAACO,IAAN,CAAWZ,SAAS,GAAGM,MAAM,CAACK,IAAP,EAAH,GAAmBL,MAAvC;;;UAGG,CAACN,SAAL,EAAgB;QACfK,KAAK,CAACO,IAAN,CAAWF,IAAX;;;MAGDJ,MAAM,GAAG,EAAT;MACAC,KAAK,GAAG,KAAR;KAVD,MAWO;MACND,MAAM,IAAII,IAAV;;;;MAIEJ,MAAM,KAAK,EAAf,EAAmB;IAClBD,KAAK,CAACO,IAAN,CAAWZ,SAAS,GAAGM,MAAM,CAACK,IAAP,EAAH,GAAmBL,MAAvC;;;SAGMD,KAAP;;;ACnDD,2BAAe,CAAChD,IAAD,EAAOwD,SAAP,EAAkBvD,GAAlB,KAA0B;QAClCwD,SAAS,GAAG,EAAlB;QACMC,SAAS,GAAG,EAAlB;EAEAjB,YAAY,CAACzC,IAAI,CAACsB,KAAN,CAAZ,CAAyBqC,OAAzB,CAAiCrC,KAAK,IAAI;QACrCsC,YAAY,GAAG,KAAnB;IAEAf,YAAY,CAACvB,KAAD,CAAZ,CAAoBqC,OAApB,CAA4B,CAACE,IAAD,EAAOC,KAAP,EAAcC,KAAd,KAAwB;UAC/CF,IAAI,IAAIG,QAAZ,EAAsB;QACrBJ,YAAY,GAAG,IAAf;QAEAI,QAAQ,CAACH,IAAD,CAAR,CAAeI,GAAf,CAAmBN,OAAnB,CAA2BO,WAAW,IAAI;gBACnC5D,KAAK,GAAGyD,KAAK,CAACI,KAAN,EAAd;UAEA7D,KAAK,CAAC8D,MAAN,CAAaN,KAAb,EAAoB,CAApB,EAAuBI,WAAvB;;cAEIT,SAAS,CAACjC,MAAV,IAAoB,CAAC,MAAMN,IAAN,CAAWuC,SAAS,CAACA,SAAS,CAACjC,MAAV,GAAmB,CAApB,CAApB,CAAzB,EAAsE;YACrEiC,SAAS,CAACF,IAAV,CAAe,GAAf;;;UAGDE,SAAS,CAACF,IAAV,CAAejD,KAAK,CAACkC,IAAN,CAAW,EAAX,CAAf;SATD;QAYAwB,QAAQ,CAACH,IAAD,CAAR,CAAeQ,GAAf,CAAmBV,OAAnB,CAA2BO,WAAW,IAAI;gBACnC5D,KAAK,GAAGyD,KAAK,CAACI,KAAN,EAAd;UAEA7D,KAAK,CAAC8D,MAAN,CAAaN,KAAb,EAAoB,CAApB,EAAuBI,WAAvB;;cAEIR,SAAS,CAAClC,MAAV,IAAoB,CAAC,MAAMN,IAAN,CAAWwC,SAAS,CAACA,SAAS,CAAClC,MAAV,GAAmB,CAApB,CAApB,CAAzB,EAAsE;YACrEkC,SAAS,CAACH,IAAV,CAAe,GAAf;;;UAGDG,SAAS,CAACH,IAAV,CAAejD,KAAK,CAACkC,IAAN,CAAW,EAAX,CAAf;SATD;;KAhBF;;QA8BI,CAACoB,YAAL,EAAmB;MAClBH,SAAS,CAACF,IAAV,CAAejC,KAAf;MACAoC,SAAS,CAACH,IAAV,CAAejC,KAAf;;GAnCF;QAuCMM,OAAO,GAAG5B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAEmC,SAAS,CAACjB,IAAV,CAAe,EAAf;GAApB,CAAhB;QACMX,OAAO,GAAG7B,IAAI,CAACM,KAAL,CAAW;IAAEgB,KAAK,EAAEoC,SAAS,CAAClB,IAAV,CAAe,EAAf;GAApB,CAAhB;SAEOiB,SAAS,CAACjC,MAAV,IAAoB,UAAUvB,GAA9B,GACJ2B,OADI,GAEL8B,SAAS,CAAClC,MAAV,IAAoB,UAAUvB,GAA9B,GACC4B,OADD,GAEAD,OAAO,CAACN,KAAR,KAAkBO,OAAO,CAACP,KAA1B,GACC,CACDG,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BE,OAA9B,CADC,EAEDH,SAAS,CAACzB,IAAD,EAAO,KAAP,CAAT,CAAuB0B,MAAvB,CAA8BG,OAA9B,CAFC,CADD,GAKA,IATF;CA9CD;AA0DA,MAAMmC,QAAQ,GAAG;kBACA;IAAEC,GAAG,EAAE,CAAC,YAAD,EAAe,eAAf,CAAP;IAAwCI,GAAG,EAAE,CAAC,YAAD,EAAe,eAAf;GAD7C;wBAEM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB,CAAP;IAAoDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB;GAF/D;sBAGI;IAAEJ,GAAG,EAAE,CAAC,eAAD,CAAP;IAA0BI,GAAG,EAAE,CAAC,eAAD;GAHnC;4BAIU;IAAEJ,GAAG,EAAE,CAAC,qBAAD,CAAP;IAAgCI,GAAG,EAAE,CAAC,qBAAD;GAJ/C;4BAKU;IAAEJ,GAAG,EAAE,CAAC,qBAAD,CAAP;IAAgCI,GAAG,EAAE,CAAC,qBAAD;GAL/C;4BAMU;IAAEJ,GAAG,EAAE,CAAC,qBAAD,CAAP;IAAgCI,GAAG,EAAE,CAAC,qBAAD;GAN/C;wBAOM;IAAEJ,GAAG,EAAE,CAAC,YAAD,CAAP;IAAuBI,GAAG,EAAE,CAAC,YAAD;GAPlC;8BAQY;IAAEJ,GAAG,EAAE,CAAC,kBAAD,CAAP;IAA6BI,GAAG,EAAE,CAAC,kBAAD;GAR9C;8BASY;IAAEJ,GAAG,EAAE,CAAC,kBAAD,CAAP;IAA6BI,GAAG,EAAE,CAAC,kBAAD;GAT9C;8BAUY;IAAEJ,GAAG,EAAE,CAAC,kBAAD,CAAP;IAA6BI,GAAG,EAAE,CAAC,kBAAD;GAV9C;wBAWM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB,CAAP;IAAoDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB;GAX/D;wBAYM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB,CAAP;IAAoDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,qBAArB;GAZ/D;gBAaF;IAAEJ,GAAG,EAAE,CAAC,eAAD,EAAkB,cAAlB,CAAP;IAA0CI,GAAG,EAAE,CAAC,eAAD,EAAkB,aAAlB;GAb7C;sBAcI;IAAEJ,GAAG,EAAE,CAAC,qBAAD,EAAwB,oBAAxB,CAAP;IAAsDI,GAAG,EAAE,CAAC,qBAAD,EAAwB,mBAAxB;GAd/D;sBAeI;IAAEJ,GAAG,EAAE,CAAC,qBAAD,EAAwB,oBAAxB,CAAP;IAAsDI,GAAG,EAAE,CAAC,qBAAD,EAAwB,mBAAxB;GAf/D;sBAgBI;IAAEJ,GAAG,EAAE,CAAC,qBAAD,EAAwB,oBAAxB,CAAP;IAAsDI,GAAG,EAAE,CAAC,qBAAD,EAAwB,mBAAxB;GAhB/D;mBAiBC;IAAEJ,GAAG,EAAE,CAAC,aAAD,EAAgB,cAAhB,CAAP;IAAwCI,GAAG,EAAE,CAAC,aAAD,EAAgB,cAAhB;GAjB9C;yBAkBO;IAAEJ,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB,CAAP;IAAoDI,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB;GAlBhE;uBAmBK;IAAEJ,GAAG,EAAE,CAAC,cAAD,CAAP;IAAyBI,GAAG,EAAE,CAAC,aAAD;GAnBnC;6BAoBW;IAAEJ,GAAG,EAAE,CAAC,oBAAD,CAAP;IAA+BI,GAAG,EAAE,CAAC,mBAAD;GApB/C;6BAqBW;IAAEJ,GAAG,EAAE,CAAC,oBAAD,CAAP;IAA+BI,GAAG,EAAE,CAAC,mBAAD;GArB/C;6BAsBW;IAAEJ,GAAG,EAAE,CAAC,oBAAD,CAAP;IAA+BI,GAAG,EAAE,CAAC,mBAAD;GAtB/C;yBAuBO;IAAEJ,GAAG,EAAE,CAAC,aAAD,CAAP;IAAwBI,GAAG,EAAE,CAAC,cAAD;GAvBpC;+BAwBa;IAAEJ,GAAG,EAAE,CAAC,mBAAD,CAAP;IAA8BI,GAAG,EAAE,CAAC,oBAAD;GAxBhD;+BAyBa;IAAEJ,GAAG,EAAE,CAAC,mBAAD,CAAP;IAA8BI,GAAG,EAAE,CAAC,oBAAD;GAzBhD;+BA0Ba;IAAEJ,GAAG,EAAE,CAAC,mBAAD,CAAP;IAA8BI,GAAG,EAAE,CAAC,oBAAD;GA1BhD;yBA2BO;IAAEJ,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB,CAAP;IAAoDI,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB;GA3BhE;yBA4BO;IAAEJ,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB,CAAP;IAAoDI,GAAG,EAAE,CAAC,mBAAD,EAAsB,oBAAtB;GA5BhE;kBA6BA;IAAEJ,GAAG,EAAE,CAAC,YAAD,EAAe,aAAf,CAAP;IAAsCI,GAAG,EAAE,CAAC,YAAD,EAAe,cAAf;GA7B3C;wBA8BM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,mBAArB,CAAP;IAAkDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,oBAArB;GA9B7D;wBA+BM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,mBAArB,CAAP;IAAkDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,oBAArB;GA/B7D;wBAgCM;IAAEJ,GAAG,EAAE,CAAC,kBAAD,EAAqB,mBAArB,CAAP;IAAkDI,GAAG,EAAE,CAAC,kBAAD,EAAqB,oBAArB;GAhC7D;gBAiCF;IAAEJ,GAAG,EAAE,CAAC,QAAD,CAAP;IAAmBI,GAAG,EAAE,CAAC,QAAD;GAjCtB;iBAkCD;IAAEJ,GAAG,EAAE,CAAC,OAAD,CAAP;IAAkBI,GAAG,EAAE,CAAC,OAAD;GAlCtB;WAmCP;IAAEJ,GAAG,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAAP;IAA2CI,GAAG,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B;GAnCzC;iBAoCD;IAAEJ,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,CAAP;IAA0BI,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR;GApC9B;uBAqCK;IAAEJ,GAAG,EAAE,CAAC,KAAD,CAAP;IAAgBI,GAAG,EAAE,CAAC,KAAD;GArC1B;qBAsCG;IAAEJ,GAAG,EAAE,CAAC,QAAD,CAAP;IAAmBI,GAAG,EAAE,CAAC,QAAD;GAtC3B;eAuCH;IAAEJ,GAAG,EAAE,CAAC,QAAD,EAAW,OAAX,CAAP;IAA4BI,GAAG,EAAE,CAAC,QAAD,EAAW,MAAX;GAvC9B;kBAwCA;IAAEJ,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CAAP;IAA0BI,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT;GAxC/B;wBAyCM;IAAEJ,GAAG,EAAE,CAAC,MAAD,CAAP;IAAiBI,GAAG,EAAE,CAAC,OAAD;GAzC5B;sBA0CI;IAAEJ,GAAG,EAAE,CAAC,OAAD,CAAP;IAAkBI,GAAG,EAAE,CAAC,MAAD;GA1C3B;iBA2CD;IAAEJ,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAAP;IAAwBI,GAAG,EAAE,CAAC,KAAD,EAAQ,OAAR;GA3C5B;kBA4CA;IAAEJ,GAAG,EAAE,CAAC,YAAD,EAAe,eAAf,CAAP;IAAwCI,GAAG,EAAE,CAAC,YAAD,EAAe,eAAf;GA5C7C;wBA6CM;IAAEJ,GAAG,EAAE,CAAC,YAAD,CAAP;IAAuBI,GAAG,EAAE,CAAC,YAAD;GA7ClC;sBA8CI;IAAEJ,GAAG,EAAE,CAAC,eAAD,CAAP;IAA0BI,GAAG,EAAE,CAAC,eAAD;GA9CnC;gBA+CF;IAAEJ,GAAG,EAAE,CAAC,eAAD,EAAkB,cAAlB,CAAP;IAA0CI,GAAG,EAAE,CAAC,eAAD,EAAkB,aAAlB;GA/C7C;mBAgDC;IAAEJ,GAAG,EAAE,CAAC,aAAD,EAAgB,cAAhB,CAAP;IAAwCI,GAAG,EAAE,CAAC,aAAD,EAAgB,cAAhB;GAhD9C;yBAiDO;IAAEJ,GAAG,EAAE,CAAC,aAAD,CAAP;IAAwBI,GAAG,EAAE,CAAC,cAAD;GAjDpC;uBAkDK;IAAEJ,GAAG,EAAE,CAAC,cAAD,CAAP;IAAyBI,GAAG,EAAE,CAAC,aAAD;GAlDnC;kBAmDA;IAAEJ,GAAG,EAAE,CAAC,YAAD,EAAe,aAAf,CAAP;IAAsCI,GAAG,EAAE,CAAC,YAAD,EAAe,cAAf;GAnD3C;mBAoDC;IAAEJ,GAAG,EAAE,CAAC,aAAD,EAAgB,gBAAhB,CAAP;IAA0CI,GAAG,EAAE,CAAC,aAAD,EAAgB,gBAAhB;GApDhD;yBAqDO;IAAEJ,GAAG,EAAE,CAAC,aAAD,CAAP;IAAwBI,GAAG,EAAE,CAAC,aAAD;GArDpC;uBAsDK;IAAEJ,GAAG,EAAE,CAAC,gBAAD,CAAP;IAA2BI,GAAG,EAAE,CAAC,gBAAD;GAtDrC;iBAuDD;IAAEJ,GAAG,EAAE,CAAC,gBAAD,EAAmB,eAAnB,CAAP;IAA4CI,GAAG,EAAE,CAAC,gBAAD,EAAmB,cAAnB;GAvDhD;oBAwDE;IAAEJ,GAAG,EAAE,CAAC,cAAD,EAAiB,eAAjB,CAAP;IAA0CI,GAAG,EAAE,CAAC,cAAD,EAAiB,eAAjB;GAxDjD;0BAyDQ;IAAEJ,GAAG,EAAE,CAAC,cAAD,CAAP;IAAyBI,GAAG,EAAE,CAAC,eAAD;GAzDtC;wBA0DM;IAAEJ,GAAG,EAAE,CAAC,eAAD,CAAP;IAA0BI,GAAG,EAAE,CAAC,cAAD;GA1DrC;mBA2DC;IAAEJ,GAAG,EAAE,CAAC,aAAD,EAAgB,cAAhB,CAAP;IAAwCI,GAAG,EAAE,CAAC,aAAD,EAAgB,eAAhB;;CA3D/D;;AC7DA,+BAAe,4IAAf;;ACAA;AACA;AAgBA,MAAMC,UAAU,GAAG;YACRC,eAAe,CAAC,QAAD,CADP;kBACmCA,eAAe,CAAC,QAAD,CADlD;kBAC8EA,eAAe,CAAC,QAAD,CAD7F;kBACyHA,eAAe,CAAC,QAAD,CADxI;kBAEFA,eAAe,CAAC,cAAD,CAFb;wBAEqDA,eAAe,CAAC,cAAD,CAFpE;wBAE4GA,eAAe,CAAC,cAAD,CAF3H;wBAEmKA,eAAe,CAAC,cAAD,CAFlL;wBAGIA,eAAe,CAAC,oBAAD,CAHnB;8BAGuEA,eAAe,CAAC,oBAAD,CAHtF;8BAG0IA,eAAe,CAAC,oBAAD,CAHzJ;8BAG6MA,eAAe,CAAC,oBAAD,CAH5N;sBAIEA,eAAe,CAAC,kBAAD,CAJjB;4BAIiEA,eAAe,CAAC,kBAAD,CAJhF;4BAIgIA,eAAe,CAAC,kBAAD,CAJ/I;4BAI+LA,eAAe,CAAC,kBAAD,CAJ9M;mBAKDA,eAAe,CAAC,eAAD,CALd;yBAKwDA,eAAe,CAAC,eAAD,CALvE;yBAKiHA,eAAe,CAAC,eAAD,CALhI;yBAK0KA,eAAe,CAAC,eAAD,CALzL;yBAMKA,eAAe,CAAC,qBAAD,CANpB;+BAM0EA,eAAe,CAAC,qBAAD,CANzF;+BAM+IA,eAAe,CAAC,qBAAD,CAN9J;+BAMoNA,eAAe,CAAC,qBAAD,CANnO;uBAOGA,eAAe,CAAC,mBAAD,CAPlB;6BAOoEA,eAAe,CAAC,mBAAD,CAPnF;6BAOqIA,eAAe,CAAC,mBAAD,CAPpJ;6BAOsMA,eAAe,CAAC,mBAAD,CAPrN;kBAQFA,eAAe,CAAC,cAAD,CARb;wBAQqDA,eAAe,CAAC,cAAD,CARpE;wBAQ4GA,eAAe,CAAC,cAAD,CAR3H;wBAQmKA,eAAe,CAAC,cAAD,CARlL;gBASJA,eAAe,CAAC,YAAD,CATX;sBAS+CA,eAAe,CAAC,YAAD,CAT9D;sBASkGA,eAAe,CAAC,YAAD,CATjH;sBASqJA,eAAe,CAAC,YAAD,CATpK;WAUTC,cAVS;WAWTC,cAXS;YAYRC,gBAZQ;aAaPA,gBAbO;WAcTC,aAAa,CAAC,OAAD,CAdJ;iBAeHA,aAAa,CAAC,aAAD,CAfV;eAgBLA,aAAa,CAAC,WAAD,CAhBR;YAiBRA,aAAa,CAAC,QAAD,CAjBL;kBAkBFA,aAAa,CAAC,cAAD,CAlBX;gBAmBJA,aAAa,CAAC,YAAD,CAnBT;WAoBTA,aAAa,CAAC,OAAD,CApBJ;SAqBXA,aAAa,CAAC,KAAD,CArBF;WAsBTH,cAtBS;YAuBRI,eAvBQ;UAwBVC,aAxBU;gBAyBJC,kBAzBI;gBA0BJC,mBA1BI;yBA2BKA;CA3BxB;;AA+BA,MAAMC,sBAAsB,GAAG,+DAA/B;;AAGA,YAAevE,OAAO,CAACwE,MAAR,CAAe,4BAAf,EAA6CC,IAAI,IAAI;QAC7DC,QAAQ,GAAGC,OAAO,CAACjF,MAAM,CAAC+E,IAAD,CAAN,CAAaC,QAAd,CAAxB;QACMlF,GAAG,GAAG,CAACkF,QAAD,IAAa,OAAOhF,MAAM,CAAC+E,IAAD,CAAN,CAAajF,GAApB,KAA4B,QAAzC,GACT,SAASiB,IAAT,CAAcgE,IAAI,CAACjF,GAAnB,IACC,KADD,GAEA,KAHS,GAIV,KAJF;SAMOoF,IAAI,IAAI;IACdA,IAAI,CAACC,SAAL,CAAetF,IAAI,IAAI;YAChBI,MAAM,GAAGJ,IAAI,CAACI,MAApB;YACMY,MAAM,GAAGgE,sBAAsB,CAAC9D,IAAvB,CAA4BlB,IAAI,CAACqB,IAAjC,IAAyCyB,YAAY,CAAC9C,IAAI,CAACsB,KAAN,EAAa,IAAb,CAArD,GAA0EuB,YAAY,CAAC7C,IAAI,CAACsB,KAAN,EAAa,IAAb,CAArG;YACMD,IAAI,GAAGrB,IAAI,CAACqB,IAAL,CAAUF,OAAV,CAAkBoE,wBAAlB,EAA4C,MAA5C,EAAoDC,WAApD,EAAb;;UAEInE,IAAI,IAAIiD,UAAZ,EAAwB;cACjBmB,QAAQ,GAAGnB,UAAU,CAACjD,IAAD,CAAV,CAAiBrB,IAAjB,EAAuBgB,MAAvB,EAA+Bf,GAA/B,CAAjB;;YAEIwF,QAAJ,EAAc;aACVC,MAAH,CAAUD,QAAV,EAAoB9B,OAApB,CAA4BO,WAAW,IAAI;gBACtCA,WAAW,CAAC7D,IAAZ,KAAqB,MAAzB,EAAiC;cAChCD,MAAM,CAACuF,MAAP,CAAczB,WAAd;aADD,MAEO;cACNlE,IAAI,CAAC2F,MAAL,CAAYzB,WAAZ;;WAJF;;cAQI,CAACiB,QAAL,EAAe;YACdnF,IAAI,CAAC4F,MAAL;;gBAEI,CAACxF,MAAM,CAACyF,KAAP,CAAarE,MAAlB,EAA0B;cACzBpB,MAAM,CAACwF,MAAP;;;;;KArBL;GADD;CARc,CAAf;;;;"}