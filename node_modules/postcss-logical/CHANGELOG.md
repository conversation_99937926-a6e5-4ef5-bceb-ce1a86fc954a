# Changes to PostCSS Logical Properties

### 3.0.0 (September 20, 2018)

- Added: Support for logical properties within `transition` and
  `transition-property`.
- Changed: Physical rule fallbacks are written as full selectors rather than
  as nesting selectors.

### 2.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

### 1.1.1 (March 21, 2017)

- Fix `dir` option to allow falsey value

### 1.1.0 (March 20, 2017)

- Add `preserve` option to preserve logical properties and values

### 1.0.2 (Aug 15, 2017)

- Improve flow-relative clear support

### 1.0.1 (Aug 13, 2017)

- Improve flow-relative border support

### 1.0.0 (Aug 8, 2017)

- Initial version
