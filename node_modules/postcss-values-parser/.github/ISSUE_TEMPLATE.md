<!--
  Please note: This template is *not* optional. Please fill in all fields and
  questions, otherwise *the issue may be closed*. Please provide actual technical
  information about errors, if an error has occurred.
-->

* Node Version:
* NPM Version:
* postcss-values-parser Version:

This issue is regarding a problem with:
- [ ] Standard CSS
- [ ] LESS
- [ ] SCSS
- [ ] SASS

If you have a large amount of code to share which demonstrates the problem you're experiencing, please provide a link to your
repository rather than pasting code. Otherwise, please paste relevant short snippets below.

```css
// offending or problematic css
```

```js
// any js you'd like to add. this section can be removed if none
```

```
// actual error output, if error was thrown
```

### Expected Behavior

### Actual Behavior

### How can we reproduce the behavior?
