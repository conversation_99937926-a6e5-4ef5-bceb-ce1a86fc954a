{"version": 3, "file": "index.cjs.js", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport valuesParser from 'postcss-values-parser';\n\nexport default postcss.plugin('postcss-color-functional-notation', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? Bo<PERSON>an(opts.preserve) : false;\n\n\treturn root => {\n\t\troot.walkDecls(decl => {\n\t\t\tconst { value: originalValue } = decl;\n\n\t\t\tif (colorAnyRegExp.test(originalValue)) {\n\t\t\t\tconst valueAST = valuesParser(originalValue).parse();\n\n\t\t\t\tvalueAST.walkType('func', node => {\n\t\t\t\t\tif (colorRegExp.test(node.value)) {\n\t\t\t\t\t\tconst children = node.nodes.slice(1, -1);\n\t\t\t\t\t\tconst isFunctionalHSL = matchFunctionalHSL(node, children);\n\t\t\t\t\t\tconst isFunctionalRGB1 = matchFunctionalRGB1(node, children);\n\t\t\t\t\t\tconst isFunctionalRGB2 = matchFunctionalRGB2(node, children);\n\n\t\t\t\t\t\tif (isFunctionalHSL || isFunctionalRGB1 || isFunctionalRGB2) {\n\t\t\t\t\t\t\tconst slashNode = children[3];\n\t\t\t\t\t\t\tconst alphaNode = children[4];\n\n\t\t\t\t\t\t\tif (alphaNode) {\n\t\t\t\t\t\t\t\tif (isPercentage(alphaNode) && !isCalc(alphaNode)) {\n\t\t\t\t\t\t\t\t\talphaNode.unit = '';\n\t\t\t\t\t\t\t\t\talphaNode.value = String(alphaNode.value / 100);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (isHslRgb(node)) {\n\t\t\t\t\t\t\t\t\tnode.value += 'a';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (isHslaRgba(node)) {\n\t\t\t\t\t\t\t\tnode.value = node.value.slice(0, -1);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (slashNode && isSlash(slashNode)) {\n\t\t\t\t\t\t\t\tslashNode.replaceWith( newComma() );\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (isFunctionalRGB2) {\n\t\t\t\t\t\t\t\tchildren[0].unit = children[1].unit = children[2].unit = '';\n\n\t\t\t\t\t\t\t\tchildren[0].value = String(Math.floor(children[0].value * 255 / 100));\n\t\t\t\t\t\t\t\tchildren[1].value = String(Math.floor(children[1].value * 255 / 100));\n\t\t\t\t\t\t\t\tchildren[2].value = String(Math.floor(children[2].value * 255 / 100));\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tnode.nodes.splice(3, 0, [ newComma() ]);\n\t\t\t\t\t\t\tnode.nodes.splice(2, 0, [ newComma() ]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tconst modifiedValue = String(valueAST);\n\n\t\t\t\tif (modifiedValue !== originalValue) {\n\t\t\t\t\tif (preserve) {\n\t\t\t\t\t\tdecl.cloneBefore({ value: modifiedValue });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdecl.value = modifiedValue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n\nconst alphaUnitMatch = /^%?$/i;\nconst calcFuncMatch = /^calc$/i;\nconst colorAnyRegExp = /(^|[^\\w-])(hsla?|rgba?)\\(/i;\nconst colorRegExp = /^(hsla?|rgba?)$/i;\nconst hslishRegExp = /^hsla?$/i;\nconst hslRgbFuncMatch = /^(hsl|rgb)$/i;\nconst hslaRgbaFuncMatch = /^(hsla|rgba)$/i;\nconst hueUnitMatch = /^(deg|grad|rad|turn)?$/i;\nconst rgbishRegExp = /^rgba?$/i;\nconst isAlphaValue = node => isCalc(node) || node.type === 'number' && alphaUnitMatch.test(node.unit);\nconst isCalc = node => node.type === 'func' && calcFuncMatch.test(node.value);\nconst isHue = node => isCalc(node) || node.type === 'number' && hueUnitMatch.test(node.unit);\nconst isNumber = node => isCalc(node) || node.type === 'number' && node.unit === '';\nconst isPercentage = node => isCalc(node) || node.type === 'number' && (node.unit === '%' || node.unit === '' && node.value === '0');\nconst isHslish = node => node.type === 'func' && hslishRegExp.test(node.value);\nconst isHslRgb = node => node.type === 'func' && hslRgbFuncMatch.test(node.value);\nconst isHslaRgba = node => node.type === 'func' && hslaRgbaFuncMatch.test(node.value);\nconst isRgbish = node => node.type === 'func' && rgbishRegExp.test(node.value);\nconst isSlash = node => node.type === 'operator' && node.value === '/';\nconst functionalHSLMatch = [isHue, isPercentage, isPercentage, isSlash, isAlphaValue];\nconst functionalRGB1Match = [isNumber, isNumber, isNumber, isSlash, isAlphaValue];\nconst functionalRGB2Match = [isPercentage, isPercentage, isPercentage, isSlash, isAlphaValue];\n\nconst matchFunctionalHSL = (node, children) => isHslish(node) && children.every(\n\t(child, index) => typeof functionalHSLMatch[index] === 'function' && functionalHSLMatch[index](child)\n);\nconst matchFunctionalRGB1 = (node, children) => isRgbish(node) && children.every(\n\t(child, index) => typeof functionalRGB1Match[index] === 'function' && functionalRGB1Match[index](child)\n);\nconst matchFunctionalRGB2 = (node, children) => isRgbish(node) && children.every(\n\t(child, index) => typeof functionalRGB2Match[index] === 'function' && functionalRGB2Match[index](child)\n);\n\nconst newComma = () => valuesParser.comma({ value: ',' })\n"], "names": ["postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "originalValue", "value", "colorAnyRegExp", "test", "valueAST", "values<PERSON><PERSON>er", "parse", "walkType", "node", "colorRegExp", "children", "nodes", "slice", "isFunctionalHSL", "matchFunctionalHSL", "isFunctionalRGB1", "matchFunctionalRGB1", "isFunctionalRGB2", "matchFunctionalRGB2", "slashNode", "alphaNode", "isPercentage", "isCalc", "unit", "String", "isHslRgb", "isHslaRgba", "isSlash", "replaceWith", "newComma", "Math", "floor", "splice", "modifiedValue", "cloneBefore", "alphaUnitMatch", "calcFuncMatch", "hslishRegExp", "hslRgbFuncMatch", "hslaRgbaFuncMatch", "hueUnitMatch", "rgbishRegExp", "isAlphaValue", "type", "is<PERSON>ue", "isNumber", "isHslish", "isRgbish", "functionalHSLMatch", "functionalRGB1Match", "functionalRGB2Match", "every", "child", "index", "comma"], "mappings": ";;;;;;;AAGA,YAAeA,OAAO,CAACC,MAAR,CAAe,mCAAf,EAAoDC,IAAI,IAAI;QACpEC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,KAAvE;SAEOG,IAAI,IAAI;IACdA,IAAI,CAACC,SAAL,CAAeC,IAAI,IAAI;YACPC,aADO,GACWD,IADX,CACdE,KADc;;UAGlBC,cAAc,CAACC,IAAf,CAAoBH,aAApB,CAAJ,EAAwC;cACjCI,QAAQ,GAAGC,YAAY,CAACL,aAAD,CAAZ,CAA4BM,KAA5B,EAAjB;QAEAF,QAAQ,CAACG,QAAT,CAAkB,MAAlB,EAA0BC,IAAI,IAAI;cAC7BC,WAAW,CAACN,IAAZ,CAAiBK,IAAI,CAACP,KAAtB,CAAJ,EAAkC;kBAC3BS,QAAQ,GAAGF,IAAI,CAACG,KAAL,CAAWC,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,CAAjB;kBACMC,eAAe,GAAGC,kBAAkB,CAACN,IAAD,EAAOE,QAAP,CAA1C;kBACMK,gBAAgB,GAAGC,mBAAmB,CAACR,IAAD,EAAOE,QAAP,CAA5C;kBACMO,gBAAgB,GAAGC,mBAAmB,CAACV,IAAD,EAAOE,QAAP,CAA5C;;gBAEIG,eAAe,IAAIE,gBAAnB,IAAuCE,gBAA3C,EAA6D;oBACtDE,SAAS,GAAGT,QAAQ,CAAC,CAAD,CAA1B;oBACMU,SAAS,GAAGV,QAAQ,CAAC,CAAD,CAA1B;;kBAEIU,SAAJ,EAAe;oBACVC,YAAY,CAACD,SAAD,CAAZ,IAA2B,CAACE,MAAM,CAACF,SAAD,CAAtC,EAAmD;kBAClDA,SAAS,CAACG,IAAV,GAAiB,EAAjB;kBACAH,SAAS,CAACnB,KAAV,GAAkBuB,MAAM,CAACJ,SAAS,CAACnB,KAAV,GAAkB,GAAnB,CAAxB;;;oBAGGwB,QAAQ,CAACjB,IAAD,CAAZ,EAAoB;kBACnBA,IAAI,CAACP,KAAL,IAAc,GAAd;;eAPF,MASO,IAAIyB,UAAU,CAAClB,IAAD,CAAd,EAAsB;gBAC5BA,IAAI,CAACP,KAAL,GAAaO,IAAI,CAACP,KAAL,CAAWW,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,CAAb;;;kBAGGO,SAAS,IAAIQ,OAAO,CAACR,SAAD,CAAxB,EAAqC;gBACpCA,SAAS,CAACS,WAAV,CAAuBC,QAAQ,EAA/B;;;kBAGGZ,gBAAJ,EAAsB;gBACrBP,QAAQ,CAAC,CAAD,CAAR,CAAYa,IAAZ,GAAmBb,QAAQ,CAAC,CAAD,CAAR,CAAYa,IAAZ,GAAmBb,QAAQ,CAAC,CAAD,CAAR,CAAYa,IAAZ,GAAmB,EAAzD;gBAEAb,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoBuB,MAAM,CAACM,IAAI,CAACC,KAAL,CAAWrB,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB,GAApB,GAA0B,GAArC,CAAD,CAA1B;gBACAS,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoBuB,MAAM,CAACM,IAAI,CAACC,KAAL,CAAWrB,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB,GAApB,GAA0B,GAArC,CAAD,CAA1B;gBACAS,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoBuB,MAAM,CAACM,IAAI,CAACC,KAAL,CAAWrB,QAAQ,CAAC,CAAD,CAAR,CAAYT,KAAZ,GAAoB,GAApB,GAA0B,GAArC,CAAD,CAA1B;;;cAGDO,IAAI,CAACG,KAAL,CAAWqB,MAAX,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAEH,QAAQ,EAAV,CAAxB;cACArB,IAAI,CAACG,KAAL,CAAWqB,MAAX,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAEH,QAAQ,EAAV,CAAxB;;;SArCH;cA0CMI,aAAa,GAAGT,MAAM,CAACpB,QAAD,CAA5B;;YAEI6B,aAAa,KAAKjC,aAAtB,EAAqC;cAChCN,QAAJ,EAAc;YACbK,IAAI,CAACmC,WAAL,CAAiB;cAAEjC,KAAK,EAAEgC;aAA1B;WADD,MAEO;YACNlC,IAAI,CAACE,KAAL,GAAagC,aAAb;;;;KAtDJ;GADD;CAHc,CAAf;AAkEA,MAAME,cAAc,GAAG,OAAvB;AACA,MAAMC,aAAa,GAAG,SAAtB;AACA,MAAMlC,cAAc,GAAG,4BAAvB;AACA,MAAMO,WAAW,GAAG,kBAApB;AACA,MAAM4B,YAAY,GAAG,UAArB;AACA,MAAMC,eAAe,GAAG,cAAxB;AACA,MAAMC,iBAAiB,GAAG,gBAA1B;AACA,MAAMC,YAAY,GAAG,yBAArB;AACA,MAAMC,YAAY,GAAG,UAArB;;AACA,MAAMC,YAAY,GAAGlC,IAAI,IAAIc,MAAM,CAACd,IAAD,CAAN,IAAgBA,IAAI,CAACmC,IAAL,KAAc,QAAd,IAA0BR,cAAc,CAAChC,IAAf,CAAoBK,IAAI,CAACe,IAAzB,CAAvE;;AACA,MAAMD,MAAM,GAAGd,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,MAAd,IAAwBP,aAAa,CAACjC,IAAd,CAAmBK,IAAI,CAACP,KAAxB,CAA/C;;AACA,MAAM2C,KAAK,GAAGpC,IAAI,IAAIc,MAAM,CAACd,IAAD,CAAN,IAAgBA,IAAI,CAACmC,IAAL,KAAc,QAAd,IAA0BH,YAAY,CAACrC,IAAb,CAAkBK,IAAI,CAACe,IAAvB,CAAhE;;AACA,MAAMsB,QAAQ,GAAGrC,IAAI,IAAIc,MAAM,CAACd,IAAD,CAAN,IAAgBA,IAAI,CAACmC,IAAL,KAAc,QAAd,IAA0BnC,IAAI,CAACe,IAAL,KAAc,EAAjF;;AACA,MAAMF,YAAY,GAAGb,IAAI,IAAIc,MAAM,CAACd,IAAD,CAAN,IAAgBA,IAAI,CAACmC,IAAL,KAAc,QAAd,KAA2BnC,IAAI,CAACe,IAAL,KAAc,GAAd,IAAqBf,IAAI,CAACe,IAAL,KAAc,EAAd,IAAoBf,IAAI,CAACP,KAAL,KAAe,GAAnF,CAA7C;;AACA,MAAM6C,QAAQ,GAAGtC,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,MAAd,IAAwBN,YAAY,CAAClC,IAAb,CAAkBK,IAAI,CAACP,KAAvB,CAAjD;;AACA,MAAMwB,QAAQ,GAAGjB,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,MAAd,IAAwBL,eAAe,CAACnC,IAAhB,CAAqBK,IAAI,CAACP,KAA1B,CAAjD;;AACA,MAAMyB,UAAU,GAAGlB,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,MAAd,IAAwBJ,iBAAiB,CAACpC,IAAlB,CAAuBK,IAAI,CAACP,KAA5B,CAAnD;;AACA,MAAM8C,QAAQ,GAAGvC,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,MAAd,IAAwBF,YAAY,CAACtC,IAAb,CAAkBK,IAAI,CAACP,KAAvB,CAAjD;;AACA,MAAM0B,OAAO,GAAGnB,IAAI,IAAIA,IAAI,CAACmC,IAAL,KAAc,UAAd,IAA4BnC,IAAI,CAACP,KAAL,KAAe,GAAnE;;AACA,MAAM+C,kBAAkB,GAAG,CAACJ,KAAD,EAAQvB,YAAR,EAAsBA,YAAtB,EAAoCM,OAApC,EAA6Ce,YAA7C,CAA3B;AACA,MAAMO,mBAAmB,GAAG,CAACJ,QAAD,EAAWA,QAAX,EAAqBA,QAArB,EAA+BlB,OAA/B,EAAwCe,YAAxC,CAA5B;AACA,MAAMQ,mBAAmB,GAAG,CAAC7B,YAAD,EAAeA,YAAf,EAA6BA,YAA7B,EAA2CM,OAA3C,EAAoDe,YAApD,CAA5B;;AAEA,MAAM5B,kBAAkB,GAAG,CAACN,IAAD,EAAOE,QAAP,KAAoBoC,QAAQ,CAACtC,IAAD,CAAR,IAAkBE,QAAQ,CAACyC,KAAT,CAChE,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOL,kBAAkB,CAACK,KAAD,CAAzB,KAAqC,UAArC,IAAmDL,kBAAkB,CAACK,KAAD,CAAlB,CAA0BD,KAA1B,CADL,CAAjE;;AAGA,MAAMpC,mBAAmB,GAAG,CAACR,IAAD,EAAOE,QAAP,KAAoBqC,QAAQ,CAACvC,IAAD,CAAR,IAAkBE,QAAQ,CAACyC,KAAT,CACjE,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOJ,mBAAmB,CAACI,KAAD,CAA1B,KAAsC,UAAtC,IAAoDJ,mBAAmB,CAACI,KAAD,CAAnB,CAA2BD,KAA3B,CADL,CAAlE;;AAGA,MAAMlC,mBAAmB,GAAG,CAACV,IAAD,EAAOE,QAAP,KAAoBqC,QAAQ,CAACvC,IAAD,CAAR,IAAkBE,QAAQ,CAACyC,KAAT,CACjE,CAACC,KAAD,EAAQC,KAAR,KAAkB,OAAOH,mBAAmB,CAACG,KAAD,CAA1B,KAAsC,UAAtC,IAAoDH,mBAAmB,CAACG,KAAD,CAAnB,CAA2BD,KAA3B,CADL,CAAlE;;AAIA,MAAMvB,QAAQ,GAAG,MAAMxB,YAAY,CAACiD,KAAb,CAAmB;EAAErD,KAAK,EAAE;CAA5B,CAAvB;;;;"}