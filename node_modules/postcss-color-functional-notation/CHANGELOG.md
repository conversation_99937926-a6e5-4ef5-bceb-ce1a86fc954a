# Changes to PostCSS Color Functional Notation

### 2.0.1 (September 18, 2018)

- Updated: PostCSS Values Parser 2 (patch for this project)

### 2.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node 6+

### 1.0.2 (July 13, 2018)

- Fixed: Poorly detected hsl() and rgb() now resolve correctly

### 1.0.1 (May 11, 2018)

- Fixed: A non-percentage 0 works alongside other percentages

### 1.0.0 (May 7, 2018)

- Initial version
