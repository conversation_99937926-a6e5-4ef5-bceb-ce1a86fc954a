import _typeof from "@babel/runtime/helpers/esm/typeof";
import * as React from 'react';
export default function useLazyKVMap(data, childrenColumnName, getRowKey) {
  var mapCacheRef = React.useRef({});
  function getRecordByKey(key) {
    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {
      var kvMap = new Map();
      /* eslint-disable no-inner-declarations */
      function dig(records) {
        records.forEach(function (record, index) {
          var rowKey = getRowKey(record, index);
          kvMap.set(rowKey, record);
          if (record && _typeof(record) === 'object' && childrenColumnName in record) {
            dig(record[childrenColumnName] || []);
          }
        });
      }
      /* eslint-enable */
      dig(data);
      mapCacheRef.current = {
        data: data,
        childrenColumnName: childrenColumnName,
        kvMap: kvMap,
        getRowKey: getRowKey
      };
    }
    return mapCacheRef.current.kvMap.get(key);
  }
  return [getRecordByKey];
}