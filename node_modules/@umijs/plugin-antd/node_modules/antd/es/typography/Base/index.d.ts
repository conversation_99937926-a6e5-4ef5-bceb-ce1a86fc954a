import type { AutoSizeType } from 'rc-textarea/lib/ResizableTextArea';
import * as React from 'react';
import type { TooltipProps } from '../../tooltip';
import type { TypographyProps } from '../Typography';
export type BaseType = 'secondary' | 'success' | 'warning' | 'danger';
interface CopyConfig {
    text?: string;
    onCopy?: (event?: React.MouseEvent<HTMLDivElement>) => void;
    icon?: React.ReactNode;
    tooltips?: boolean | React.ReactNode;
    format?: 'text/plain' | 'text/html';
}
interface EditConfig {
    text?: string;
    editing?: boolean;
    icon?: React.ReactNode;
    tooltip?: boolean | React.ReactNode;
    onStart?: () => void;
    onChange?: (value: string) => void;
    onCancel?: () => void;
    onEnd?: () => void;
    maxLength?: number;
    autoSize?: boolean | AutoSizeType;
    triggerType?: ('icon' | 'text')[];
    enterIcon?: React.ReactNode;
}
export interface EllipsisConfig {
    rows?: number;
    expandable?: boolean;
    suffix?: string;
    symbol?: React.ReactNode;
    onExpand?: React.MouseEventHandler<HTMLElement>;
    onEllipsis?: (ellipsis: boolean) => void;
    tooltip?: React.ReactNode | TooltipProps;
}
export interface BlockProps<C extends keyof JSX.IntrinsicElements = keyof JSX.IntrinsicElements> extends TypographyProps<C> {
    title?: string;
    editable?: boolean | EditConfig;
    copyable?: boolean | CopyConfig;
    type?: BaseType;
    disabled?: boolean;
    ellipsis?: boolean | EllipsisConfig;
    code?: boolean;
    mark?: boolean;
    underline?: boolean;
    delete?: boolean;
    strong?: boolean;
    keyboard?: boolean;
    italic?: boolean;
}
declare const Base: React.ForwardRefExoticComponent<BlockProps<keyof JSX.IntrinsicElements> & React.RefAttributes<HTMLElement>>;
export default Base;
