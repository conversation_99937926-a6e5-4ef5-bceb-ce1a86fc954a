.@{ant-prefix}-anchor {
  &-rtl {
    direction: rtl;
  }

  &-wrapper {
    .@{ant-prefix}-anchor-rtl& {
      margin-right: -4px;
      margin-left: 0;
      padding-right: 4px;
      padding-left: 0;
    }
  }

  &-ink {
    .@{ant-prefix}-anchor-rtl & {
      right: 0;
      left: auto;
    }

    &-ball {
      .@{ant-prefix}-anchor-rtl & {
        right: 50%;
        left: 0;
        transform: translateX(50%);
      }
    }
  }

  &-link {
    .@{ant-prefix}-anchor-rtl & {
      padding: @anchor-link-top @anchor-link-left @anchor-link-top 0;
    }
  }
}
