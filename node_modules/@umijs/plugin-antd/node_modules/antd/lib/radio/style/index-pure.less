@import '../../style/themes/index';
@import '../../style/mixins/index';

@radio-prefix-cls: ~'@{ant-prefix}-radio';
@radio-group-prefix-cls: ~'@{radio-prefix-cls}-group';
@radio-inner-prefix-cls: ~'@{radio-prefix-cls}-inner';
@radio-duration: 0.3s;
@radio-focus-shadow: 0 0 0 3px @slider-handle-color-focus-shadow;
@radio-button-focus-shadow: @radio-focus-shadow;

.@{radio-group-prefix-cls} {
  .reset-component();

  display: inline-block;
  font-size: 0;

  .@{ant-prefix}-badge-count {
    z-index: 1;
  }

  > .@{ant-prefix}-badge:not(:first-child) > .@{radio-prefix-cls}-button-wrapper {
    border-left: none;
  }
}

// 一般状态
.@{radio-prefix-cls}-wrapper {
  .reset-component();
  position: relative;
  display: inline-flex;
  align-items: baseline;
  margin-right: @radio-wrapper-margin-right;
  cursor: pointer;

  &-disabled {
    cursor: not-allowed;
  }

  &::after {
    display: inline-block;
    width: 0;
    overflow: hidden;
    content: '\a0';
  }

  &&-in-form-item {
    input[type='radio'] {
      width: 14px;
      height: 14px;
    }
  }
}

.@{radio-prefix-cls} {
  .reset-component();

  position: relative;
  top: @radio-top;
  display: inline-block;
  outline: none;
  cursor: pointer;

  .@{radio-prefix-cls}-wrapper:hover &,
  &:hover .@{radio-inner-prefix-cls},
  &-input:focus + .@{radio-inner-prefix-cls} {
    border-color: @radio-dot-color;
  }

  &-input:focus + .@{radio-inner-prefix-cls} {
    box-shadow: @radio-focus-shadow;
  }

  &-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid @radio-dot-color;
    border-radius: 50%;
    visibility: hidden;
    animation: antRadioEffect 0.36s ease-in-out;
    animation-fill-mode: both;
    content: '';
  }

  &:hover::after,
  .@{radio-prefix-cls}-wrapper:hover &::after {
    visibility: visible;
  }

  &-inner {
    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      display: block;
      width: @radio-size;
      height: @radio-size;
      margin-top: -(@radio-size / 2);
      margin-left: -(@radio-size / 2);
      background-color: @radio-dot-color;
      border-top: 0;
      border-left: 0;
      border-radius: @radio-size;
      transform: scale(0);
      opacity: 0;
      transition: all @radio-duration @ease-in-out-circ;
      content: ' ';
    }

    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: @radio-size;
    height: @radio-size;
    background-color: @radio-button-bg;
    border-color: @border-color-base;
    border-style: solid;
    border-width: @radio-border-width;
    border-radius: 50%;
    transition: all @radio-duration;
  }

  &-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    cursor: pointer;
    opacity: 0;
  }

  &&-disabled {
    .@{radio-inner-prefix-cls} {
      border-color: @border-color-base;
    }
  }
}

// 选中状态
.@{radio-prefix-cls}-checked {
  .@{radio-inner-prefix-cls} {
    border-color: @radio-dot-color;

    &::after {
      transform: scale((unit(@radio-dot-size) / unit(@radio-size)));
      opacity: 1;
      transition: all @radio-duration @ease-in-out-circ;
    }
  }
}

.@{radio-prefix-cls}-disabled {
  cursor: not-allowed;

  .@{radio-inner-prefix-cls} {
    background-color: @input-disabled-bg;
    cursor: not-allowed;

    &::after {
      background-color: @radio-dot-disabled-color;
    }
  }

  .@{radio-prefix-cls}-input {
    cursor: not-allowed;
  }

  & + span {
    color: @disabled-color;
    cursor: not-allowed;
  }
}

span.@{radio-prefix-cls} + * {
  padding-right: 8px;
  padding-left: 8px;
}

.@{radio-prefix-cls}-button-wrapper {
  position: relative;
  display: inline-block;
  height: @btn-height-base;
  margin: 0;
  padding: 0 @radio-button-padding-horizontal;
  color: @radio-button-color;
  font-size: @font-size-base;
  line-height: @btn-height-base - 2px;
  background: @radio-button-bg;
  border: @border-width-base @border-style-base @border-color-base;
  // strange align fix for chrome but works
  // https://gw.alipayobjects.com/zos/rmsportal/VFTfKXJuogBAXcvfAUWJ.gif
  border-top-width: @border-width-base + 0.02px;
  border-left-width: 0;
  cursor: pointer;
  transition: color 0.3s, background 0.3s, border-color 0.3s, box-shadow 0.3s;

  a {
    color: @radio-button-color;
  }

  > .@{radio-prefix-cls}-button {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
  }

  .@{radio-group-prefix-cls}-large & {
    height: @input-height-lg;
    font-size: @font-size-lg;
    line-height: @input-height-lg - 2px;
  }

  .@{radio-group-prefix-cls}-small & {
    height: @input-height-sm;
    padding: 0 @control-padding-horizontal-sm - 1px;
    line-height: @input-height-sm - 2px;
  }

  &:not(:first-child) {
    &::before {
      position: absolute;
      top: @border-width-base * -1;
      left: -1px;
      display: block;
      box-sizing: content-box;
      width: 1px;
      height: 100%;
      padding: @border-width-base 0;
      background-color: @border-color-base;
      transition: background-color 0.3s;
      content: '';
    }
  }

  &:first-child {
    border-left: @border-width-base @border-style-base @border-color-base;
    border-radius: @border-radius-base 0 0 @border-radius-base;
  }

  &:last-child {
    border-radius: 0 @border-radius-base @border-radius-base 0;
  }

  &:first-child:last-child {
    border-radius: @border-radius-base;
  }

  &:hover {
    position: relative;
    color: @radio-dot-color;
  }

  &:focus-within {
    box-shadow: @radio-button-focus-shadow;
  }

  .@{radio-prefix-cls}-inner,
  input[type='checkbox'],
  input[type='radio'] {
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
  }

  &-checked:not(&-disabled) {
    z-index: 1;
    color: @radio-dot-color;
    background: @radio-button-checked-bg;
    border-color: @radio-dot-color;

    &::before {
      background-color: @radio-dot-color;
    }

    &:first-child {
      border-color: @radio-dot-color;
    }

    &:hover {
      color: @radio-button-hover-color;
      border-color: @radio-button-hover-color;

      &::before {
        background-color: @radio-button-hover-color;
      }
    }

    &:active {
      color: @radio-button-active-color;
      border-color: @radio-button-active-color;

      &::before {
        background-color: @radio-button-active-color;
      }
    }

    &:focus-within {
      box-shadow: @radio-button-focus-shadow;
    }
  }

  .@{radio-group-prefix-cls}-solid &-checked:not(&-disabled) {
    color: @radio-solid-checked-color;
    background: @radio-dot-color;
    border-color: @radio-dot-color;

    &:hover {
      color: @radio-solid-checked-color;
      background: @radio-button-hover-color;
      border-color: @radio-button-hover-color;
    }

    &:active {
      color: @radio-solid-checked-color;
      background: @radio-button-active-color;
      border-color: @radio-button-active-color;
    }

    &:focus-within {
      box-shadow: @radio-button-focus-shadow;
    }
  }

  &-disabled {
    color: @disabled-color;
    background-color: @input-disabled-bg;
    border-color: @border-color-base;
    cursor: not-allowed;

    &:first-child,
    &:hover {
      color: @disabled-color;
      background-color: @input-disabled-bg;
      border-color: @border-color-base;
    }

    &:first-child {
      border-left-color: @border-color-base;
    }
  }

  &-disabled&-checked {
    color: @radio-disabled-button-checked-color;
    background-color: @radio-disabled-button-checked-bg;
    border-color: @border-color-base;
    box-shadow: none;
  }
}

@keyframes antRadioEffect {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }

  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

@import './rtl';
