// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
var _ReconciliationTwoTone = /*#__PURE__*/ _interop_require_default(require("@ant-design/icons-svg/lib/asn/ReconciliationTwoTone"));
var _AntdIcon = /*#__PURE__*/ _interop_require_default(require("../components/AntdIcon"));
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
var ReconciliationTwoTone = function(props, ref) {
    return /*#__PURE__*/ _react.createElement(_AntdIcon.default, _object_spread_props(_object_spread({}, props), {
        ref: ref,
        icon: _ReconciliationTwoTone.default
    }));
};
/**![reconciliation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0MCAzNDRINDA0VjI0MEgzMDR2MTYwaDE3NmMxNy43IDAgMzIgMTQuMyAzMiAzMnYzNjBoMzI4VjI0MEg3NDB2MTA0ek01ODQgNDQ4YzAtNC40IDMuNi04IDgtOGg0OGM0LjQgMCA4IDMuNiA4IDh2NTZjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di01NnptOTIgMzAxYy01MC44IDAtOTItNDEuMi05Mi05MnM0MS4yLTkyIDkyLTkyIDkyIDQxLjIgOTIgOTItNDEuMiA5Mi05MiA5MnptOTItMzQxdjk2YzAgNC40LTMuNiA4LTggOGgtNDhjLTQuNCAwLTgtMy42LTgtOHYtOTZjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTY0MiA2NTdhMzQgMzQgMCAxMDY4IDAgMzQgMzQgMCAxMC02OCAweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTkyIDUxMmg0OGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDh6bTExMi0xMDR2OTZjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtOTZjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNODgwIDE2OEg2NjhjMC0zMC45LTI1LjEtNTYtNTYtNTZoLTgwYy0zMC45IDAtNTYgMjUuMS01NiA1NkgyNjRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjIwMGgtODhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjQ0OGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzMzZjMTcuNyAwIDMyLTE0LjMgMzItMzJ2LTE2aDM2OGMxNy43IDAgMzItMTQuMyAzMi0zMlYyMDBjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MTIgNjRoNzJ2LTU2aDY0djU2aDcydjQ4SDQ2OHYtNDh6bS0yMCA2MTZIMTc2VjYxNmgyNzJ2MjMyem0wLTI5NkgxNzZ2LTg4aDI3MnY4OHptMzkyIDI0MEg1MTJWNDMyYzAtMTcuNy0xNC4zLTMyLTMyLTMySDMwNFYyNDBoMTAwdjEwNGgzMzZWMjQwaDEwMHY1NTJ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik02NzYgNTY1Yy01MC44IDAtOTIgNDEuMi05MiA5MnM0MS4yIDkyIDkyIDkyIDkyLTQxLjIgOTItOTItNDEuMi05Mi05Mi05MnptMCAxMjZjLTE4LjggMC0zNC0xNS4yLTM0LTM0czE1LjItMzQgMzQtMzQgMzQgMTUuMiAzNCAzNC0xNS4yIDM0LTM0IDM0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */ var RefIcon = /*#__PURE__*/ _react.forwardRef(ReconciliationTwoTone);
if (process.env.NODE_ENV !== 'production') {
    RefIcon.displayName = 'ReconciliationTwoTone';
}
var _default = RefIcon;
