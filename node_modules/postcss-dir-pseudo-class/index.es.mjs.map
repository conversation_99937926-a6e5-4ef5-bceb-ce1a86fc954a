{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport selectorParser from 'postcss-selector-parser';\n\nexport default postcss.plugin('postcss-dir-pseudo-class', opts => {\n\tconst dir = Object(opts).dir;\n\tconst preserve = Boolean(Object(opts).preserve);\n\n\treturn root => {\n\t\t// walk rules using the :dir pseudo-class\n\t\troot.walkRules(/:dir\\([^\\)]*\\)/, rule => {\n\t\t\tlet currentRule = rule\n\n\t\t\t// conditionally preserve the original rule\n\t\t\tif (preserve) {\n\t\t\t\tcurrentRule = rule.cloneBefore();\n\t\t\t}\n\n\t\t\t// update the rule selector\n\t\t\tcurrentRule.selector = selectorParser(selectors => {\n\t\t\t\t// for each (comma separated) selector\n\t\t\t\tselectors.nodes.forEach(selector => {\n\t\t\t\t\t// walk all selector nodes that are :dir pseudo-classes\n\t\t\t\t\tselector.walk(node => {\n\t\t\t\t\t\tif ('pseudo' === node.type && ':dir' === node.value) {\n\t\t\t\t\t\t\t// previous and next selector nodes\n\t\t\t\t\t\t\tconst prev = node.prev();\n\t\t\t\t\t\t\tconst next = node.next();\n\n\t\t\t\t\t\t\tconst prevIsSpaceCombinator = prev && prev.type && 'combinator' === prev.type && ' ' === prev.value;\n\t\t\t\t\t\t\tconst nextIsSpaceCombinator = next && next.type && 'combinator' === next.type && ' ' === next.value;\n\n\t\t\t\t\t\t\t// preserve the selector tree\n\t\t\t\t\t\t\tif (prevIsSpaceCombinator && (nextIsSpaceCombinator || !next)) {\n\t\t\t\t\t\t\t\tnode.replaceWith(\n\t\t\t\t\t\t\t\t\tselectorParser.universal()\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tnode.remove();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// conditionally prepend a combinator before inserting the [dir] attribute\n\t\t\t\t\t\t\tconst first = selector.nodes[0];\n\t\t\t\t\t\t\tconst firstIsSpaceCombinator = first && 'combinator' === first.type && ' ' === first.value;\n\t\t\t\t\t\t\tconst firstIsHtml = first && 'tag' === first.type && 'html' === first.value;\n\t\t\t\t\t\t\tconst firstIsRoot = first && 'pseudo' === first.type && ':root' === first.value;\n\n\t\t\t\t\t\t\tif (first && !firstIsHtml && !firstIsRoot && !firstIsSpaceCombinator) {\n\t\t\t\t\t\t\t\tselector.prepend(\n\t\t\t\t\t\t\t\t\tselectorParser.combinator({\n\t\t\t\t\t\t\t\t\t\tvalue: ' '\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// value of the :dir pseudo-class\n\t\t\t\t\t\t\tconst value = node.nodes.toString();\n\n\t\t\t\t\t\t\t// whether :dir matches the presumed direction\n\t\t\t\t\t\t\tconst isdir = dir === value;\n\n\t\t\t\t\t\t\t// [dir] attribute\n\t\t\t\t\t\t\tconst dirAttr = selectorParser.attribute({\n\t\t\t\t\t\t\t\tattribute: 'dir',\n\t\t\t\t\t\t\t\toperator:  '=',\n\t\t\t\t\t\t\t\tquoteMark: '\"',\n\t\t\t\t\t\t\t\tvalue:     `\"${ value }\"`\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t// not[dir] attribute\n\t\t\t\t\t\t\tconst notDirAttr = selectorParser.pseudo({\n\t\t\t\t\t\t\t\tvalue: `${firstIsHtml || firstIsRoot ? '' : 'html'}:not`\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tnotDirAttr.append(\n\t\t\t\t\t\t\t\tselectorParser.attribute({\n\t\t\t\t\t\t\t\t\tattribute: 'dir',\n\t\t\t\t\t\t\t\t\toperator:  '=',\n\t\t\t\t\t\t\t\t\tquoteMark: '\"',\n\t\t\t\t\t\t\t\t\tvalue:     `\"${ 'ltr' === value ? 'rtl' : 'ltr' }\"`\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tif (isdir) {\n\t\t\t\t\t\t\t\t// if the direction is presumed\n\t\t\t\t\t\t\t\tif (firstIsHtml) {\n\t\t\t\t\t\t\t\t\t// insert :root after html tag\n\t\t\t\t\t\t\t\t\tselector.insertAfter(first, notDirAttr);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t// prepend :root\n\t\t\t\t\t\t\t\t\tselector.prepend(notDirAttr);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (firstIsHtml) {\n\t\t\t\t\t\t\t\t// otherwise, insert dir attribute after html tag\n\t\t\t\t\t\t\t\tselector.insertAfter(first, dirAttr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// otherwise, prepend the dir attribute\n\t\t\t\t\t\t\t\tselector.prepend(dirAttr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}).processSync(currentRule.selector);\n\t\t});\n\t};\n});\n"], "names": ["postcss", "plugin", "opts", "dir", "Object", "preserve", "Boolean", "root", "walkRules", "rule", "currentRule", "cloneBefore", "selector", "selector<PERSON><PERSON><PERSON>", "selectors", "nodes", "for<PERSON>ach", "walk", "node", "type", "value", "prev", "next", "prevIsSpaceCombinator", "nextIsSpaceCombinator", "replaceWith", "universal", "remove", "first", "firstIsSpaceCombinator", "firstIsHtml", "firstIsRoot", "prepend", "combinator", "toString", "isdir", "dir<PERSON><PERSON><PERSON>", "attribute", "operator", "quoteMark", "notDirAttr", "pseudo", "append", "insertAfter", "processSync"], "mappings": ";;;AAGA,YAAeA,OAAO,CAACC,MAAR,CAAe,0BAAf,EAA2CC,IAAI,IAAI;QAC3DC,GAAG,GAAGC,MAAM,CAACF,IAAD,CAAN,CAAaC,GAAzB;QACME,QAAQ,GAAGC,OAAO,CAACF,MAAM,CAACF,IAAD,CAAN,CAAaG,QAAd,CAAxB;SAEOE,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAe,gBAAf,EAAiCC,IAAI,IAAI;UACpCC,WAAW,GAAGD,IAAlB,CADwC;;UAIpCJ,QAAJ,EAAc;QACbK,WAAW,GAAGD,IAAI,CAACE,WAAL,EAAd;OALuC;;;MASxCD,WAAW,CAACE,QAAZ,GAAuBC,cAAc,CAACC,SAAS,IAAI;;QAElDA,SAAS,CAACC,KAAV,CAAgBC,OAAhB,CAAwBJ,QAAQ,IAAI;;UAEnCA,QAAQ,CAACK,IAAT,CAAcC,IAAI,IAAI;gBACjB,aAAaA,IAAI,CAACC,IAAlB,IAA0B,WAAWD,IAAI,CAACE,KAA9C,EAAqD;;oBAE9CC,IAAI,GAAGH,IAAI,CAACG,IAAL,EAAb;oBACMC,IAAI,GAAGJ,IAAI,CAACI,IAAL,EAAb;oBAEMC,qBAAqB,GAAGF,IAAI,IAAIA,IAAI,CAACF,IAAb,IAAqB,iBAAiBE,IAAI,CAACF,IAA3C,IAAmD,QAAQE,IAAI,CAACD,KAA9F;oBACMI,qBAAqB,GAAGF,IAAI,IAAIA,IAAI,CAACH,IAAb,IAAqB,iBAAiBG,IAAI,CAACH,IAA3C,IAAmD,QAAQG,IAAI,CAACF,KAA9F,CANoD;;kBAShDG,qBAAqB,KAAKC,qBAAqB,IAAI,CAACF,IAA/B,CAAzB,EAA+D;gBAC9DJ,IAAI,CAACO,WAAL,CACCZ,cAAc,CAACa,SAAf,EADD;eADD,MAIO;gBACNR,IAAI,CAACS,MAAL;eAdmD;;;oBAkB9CC,KAAK,GAAGhB,QAAQ,CAACG,KAAT,CAAe,CAAf,CAAd;oBACMc,sBAAsB,GAAGD,KAAK,IAAI,iBAAiBA,KAAK,CAACT,IAAhC,IAAwC,QAAQS,KAAK,CAACR,KAArF;oBACMU,WAAW,GAAGF,KAAK,IAAI,UAAUA,KAAK,CAACT,IAAzB,IAAiC,WAAWS,KAAK,CAACR,KAAtE;oBACMW,WAAW,GAAGH,KAAK,IAAI,aAAaA,KAAK,CAACT,IAA5B,IAAoC,YAAYS,KAAK,CAACR,KAA1E;;kBAEIQ,KAAK,IAAI,CAACE,WAAV,IAAyB,CAACC,WAA1B,IAAyC,CAACF,sBAA9C,EAAsE;gBACrEjB,QAAQ,CAACoB,OAAT,CACCnB,cAAc,CAACoB,UAAf,CAA0B;kBACzBb,KAAK,EAAE;iBADR,CADD;eAxBmD;;;oBAgC9CA,KAAK,GAAGF,IAAI,CAACH,KAAL,CAAWmB,QAAX,EAAd,CAhCoD;;oBAmC9CC,KAAK,GAAGhC,GAAG,KAAKiB,KAAtB,CAnCoD;;oBAsC9CgB,OAAO,GAAGvB,cAAc,CAACwB,SAAf,CAAyB;gBACxCA,SAAS,EAAE,KAD6B;gBAExCC,QAAQ,EAAG,GAF6B;gBAGxCC,SAAS,EAAE,GAH6B;gBAIxCnB,KAAK,EAAO,IAAIA,KAAO;eAJR,CAAhB,CAtCoD;;oBA8C9CoB,UAAU,GAAG3B,cAAc,CAAC4B,MAAf,CAAsB;gBACxCrB,KAAK,EAAG,GAAEU,WAAW,IAAIC,WAAf,GAA6B,EAA7B,GAAkC,MAAO;eADjC,CAAnB;cAIAS,UAAU,CAACE,MAAX,CACC7B,cAAc,CAACwB,SAAf,CAAyB;gBACxBA,SAAS,EAAE,KADa;gBAExBC,QAAQ,EAAG,GAFa;gBAGxBC,SAAS,EAAE,GAHa;gBAIxBnB,KAAK,EAAO,IAAI,UAAUA,KAAV,GAAkB,KAAlB,GAA0B,KAAO;eAJlD,CADD;;kBASIe,KAAJ,EAAW;;oBAENL,WAAJ,EAAiB;;kBAEhBlB,QAAQ,CAAC+B,WAAT,CAAqBf,KAArB,EAA4BY,UAA5B;iBAFD,MAGO;;kBAEN5B,QAAQ,CAACoB,OAAT,CAAiBQ,UAAjB;;eAPF,MASO,IAAIV,WAAJ,EAAiB;;gBAEvBlB,QAAQ,CAAC+B,WAAT,CAAqBf,KAArB,EAA4BQ,OAA5B;eAFM,MAGA;;gBAENxB,QAAQ,CAACoB,OAAT,CAAiBI,OAAjB;;;WA1EH;SAFD;OAFoC,CAAd,CAmFpBQ,WAnFoB,CAmFRlC,WAAW,CAACE,QAnFJ,CAAvB;KATD;GAFD;CAJc,CAAf;;;;"}