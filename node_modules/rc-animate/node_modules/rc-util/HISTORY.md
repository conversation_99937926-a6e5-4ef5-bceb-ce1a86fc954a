# History

---

## 4.16.1 / 2019-12-09

- <PERSON><PERSON><PERSON><PERSON><PERSON> add `switchScrollingEffect` prop.
- Add `setStyle`.
- Improve `switchScrollingEffect` when closed

## 4.15.0 / 2019-11-09

- Add `unsafeLifecyclesPolyfill`. ant-design/ant-design#9792

## 4.14.0 / 2019-10-29

- add `supportRef` check

## 4.13.0 / 2019-10-10

- add `domHooks` for test

## 4.12.0 / 2019-10-08

- add `ref` util

## 4.11.0 / 2019-08-23

- warning support `noteOnce`

## 4.8.0 / 2019-07-09

- add findDOMNode to check if a DOM first and then use native findDOMNode

## 4.7.0 / 2019-07-08

- add Portal<PERSON>rapper to support body scroll control

## 4.6.0 / 2016-10-15

- addDOMEventListener support option

## 3.4.0 / 2016-08-16

- add getScrollBarSize

## 3.3.0 / 2016-07-18

- add getContainerRenderMixin
