{"version": 3, "file": "index.js", "sources": ["src/patch/postcss-system-ui-font-family.js", "src/lib/plugins-by-id.js", "src/lib/get-transformed-insertions.js", "src/lib/get-unsupported-browsers-by-feature.js", "src/lib/ids-by-execution-order.js", "src/lib/write-to-exports.js", "src/postcss.js"], "sourcesContent": ["import postcss from 'postcss';\n\nexport default postcss.plugin('postcss-system-ui-font', () => root => {\n\troot.walkDecls(propertyRegExp, decl => {\n\t\tdecl.value = decl.value.replace(systemUiMatch, systemUiReplace);\n\t});\n});\n\nconst propertyRegExp = /(?:^(?:-|\\\\002d){2})|(?:^font(?:-family)?$)/i;\nconst whitespace = '[\\\\f\\\\n\\\\r\\\\x09\\\\x20]';\nconst systemUiFamily = [\n\t'system-ui',\n\t/* macOS 10.11-10.12 */ '-apple-system',\n\t/* Windows 6+ */ 'Segoe UI',\n\t/* Android 4+ */ 'Roboto',\n\t/* Ubuntu 10.10+ */ 'Ubuntu',\n\t/* Gnome 3+ */ 'Cantarell',\n\t/* KDE Plasma 5+ */ 'Noto Sans',\n\t/* fallback */ 'sans-serif'\n];\nconst systemUiMatch = new RegExp(`(^|,|${whitespace}+)(?:system-ui${whitespace}*)(?:,${whitespace}*(?:${systemUiFamily.join('|')})${whitespace}*)?(,|$)`, 'i');\nconst systemUiReplace = `$1${systemUiFamily.join(', ')}$2`;\n", "import postcssAttributeCaseInsensitive from 'postcss-attribute-case-insensitive';\nimport postcssBlankPseudo from 'css-blank-pseudo/postcss';\nimport postcssColorFunctionalNotation from 'postcss-color-functional-notation';\nimport postcssColor<PERSON>ray from 'postcss-color-gray';\nimport postcssColor<PERSON><PERSON><PERSON>l<PERSON> from 'postcss-color-hex-alpha';\nimport postcssColorModFunction from 'postcss-color-mod-function';\nimport postcssColorRebeccapurple from 'postcss-color-rebeccapurple';\nimport postcssCustomMedia from 'postcss-custom-media';\nimport postcssCustomProperties from 'postcss-custom-properties';\nimport postcssCustomSelectors from 'postcss-custom-selectors';\nimport postcssDirPseudoClass from 'postcss-dir-pseudo-class';\nimport postcssDoublePositionGradients from 'postcss-double-position-gradients';\nimport postcssEnvFunction from 'postcss-env-function';\nimport postcssFocusVisible from 'postcss-focus-visible';\nimport postcssFocusWithin from 'postcss-focus-within';\nimport postcssFontVariant from 'postcss-font-variant';\nimport postcssFontFamilySystemUi from '../patch/postcss-system-ui-font-family';\nimport postcssGapProperties from 'postcss-gap-properties';\nimport postcssHasPseudo from 'css-has-pseudo/postcss';\nimport postcssImageSetPolyfill from 'postcss-image-set-function';\nimport postcssInitial from 'postcss-initial';\nimport postcssLabFunction from 'postcss-lab-function';\nimport postcssLogical from 'postcss-logical';\nimport postcssMediaMinmax from 'postcss-media-minmax';\nimport postcssNesting from 'postcss-nesting';\nimport postcssOverflowShorthand from 'postcss-overflow-shorthand';\nimport postcssPageBreak from 'postcss-page-break';\nimport postcssPlace from 'postcss-place';\nimport postcssPrefersColorScheme from 'css-prefers-color-scheme/postcss';\nimport postcssPseudoClassAnyLink from 'postcss-pseudo-class-any-link';\nimport postcssReplaceOverflowWrap from 'postcss-replace-overflow-wrap';\nimport postcssSelectorMatches from 'postcss-selector-matches';\nimport postcssSelectorNot from 'postcss-selector-not';\n\n// postcss plugins ordered by id\nexport default {\n\t'all-property': postcssInitial,\n\t'any-link-pseudo-class': postcssPseudoClassAnyLink,\n\t'blank-pseudo-class': postcssBlankPseudo,\n\t'break-properties': postcssPageBreak,\n\t'case-insensitive-attributes': postcssAttributeCaseInsensitive,\n\t'color-functional-notation': postcssColorFunctionalNotation,\n\t'color-mod-function': postcssColorModFunction,\n\t'custom-media-queries': postcssCustomMedia,\n\t'custom-properties': postcssCustomProperties,\n\t'custom-selectors': postcssCustomSelectors,\n\t'dir-pseudo-class': postcssDirPseudoClass,\n\t'double-position-gradients': postcssDoublePositionGradients,\n\t'environment-variables': postcssEnvFunction,\n\t'focus-visible-pseudo-class': postcssFocusVisible,\n\t'focus-within-pseudo-class': postcssFocusWithin,\n\t'font-variant-property': postcssFontVariant,\n\t'gap-properties': postcssGapProperties,\n\t'gray-function': postcssColorGray,\n\t'has-pseudo-class': postcssHasPseudo,\n\t'hexadecimal-alpha-notation': postcssColorHexAlpha,\n\t'image-set-function': postcssImageSetPolyfill,\n\t'lab-function': postcssLabFunction,\n\t'logical-properties-and-values': postcssLogical,\n\t'matches-pseudo-class': postcssSelectorMatches,\n\t'media-query-ranges': postcssMediaMinmax,\n\t'nesting-rules': postcssNesting,\n\t'not-pseudo-class': postcssSelectorNot,\n\t'overflow-property': postcssOverflowShorthand,\n\t'overflow-wrap-property': postcssReplaceOverflowWrap,\n\t'place-properties': postcssPlace,\n\t'prefers-color-scheme-query': postcssPrefersColorScheme,\n\t'rebeccapurple-color': postcssColorRebeccapurple,\n\t'system-ui-font-family': postcssFontFamilySystemUi\n};\n", "// return a list of features to be inserted before or after cssdb features\nexport default function getTransformedInsertions(insertions, placement) {\n\treturn Object.keys(insertions).map(\n\t\tid => [].concat(insertions[id]).map(\n\t\t\tplugin => ({\n\t\t\t\t[placement]: true,\n\t\t\t\tplugin,\n\t\t\t\tid\n\t\t\t})\n\t\t)\n\t).reduce(\n\t\t(array, feature) => array.concat(feature), []\n\t);\n}\n", "import * as caniuse from 'caniuse-lite';\n\n// return a list of browsers that do not support the feature\nexport default function getUnsupportedBrowsersByFeature(feature) {\n\tconst caniuseFeature = caniuse.features[feature];\n\n\t// if feature support can be determined\n\tif (caniuseFeature) {\n\t\tconst stats = caniuse.feature(caniuseFeature).stats;\n\n\t\t// return an array of browsers and versions that do not support the feature\n\t\tconst results = Object.keys(stats).reduce(\n\t\t\t(browsers, browser) => browsers.concat(\n\t\t\t\tObject.keys(stats[browser]).filter(\n\t\t\t\t\tversion => stats[browser][version].indexOf('y') !== 0\n\t\t\t\t).map(\n\t\t\t\t\tversion => `${browser} ${version}`\n\t\t\t\t)\n\t\t\t),\n\t\t\t[]\n\t\t);\n\n\t\treturn results;\n\t} else {\n\t\t// otherwise, return that the feature does not work in any browser\n\t\treturn [ '> 0%' ];\n\t}\n}\n", "// ids ordered by required execution, then alphabetically\nexport default [\n\t'custom-media-queries',\n\t'custom-properties',\n\t'environment-variables', // run environment-variables here to access transpiled custom media params and properties\n\t'image-set-function', // run images-set-function before nesting-rules so that it may fix nested media\n\t'media-query-ranges', // run media-query-range and\n\t'prefers-color-scheme-query', // run prefers-color-scheme-query here to prevent duplicate transpilation after nesting-rules\n\t'nesting-rules',\n\t'custom-selectors', // run custom-selectors after nesting-rules to correctly transpile &:--custom-selector\n\t'any-link-pseudo-class',\n\t'case-insensitive-attributes',\n\t'focus-visible-pseudo-class',\n\t'focus-within-pseudo-class',\n\t'matches-pseudo-class', // run matches-pseudo-class and\n\t'not-pseudo-class', // run not-pseudo-class after other selectors have been transpiled\n\t'logical-properties-and-values', // run logical-properties-and-values before dir-pseudo-class\n\t'dir-pseudo-class',\n\t'all-property', // run all-property before other property polyfills\n\t'color-functional-notation',\n\t'double-position-gradients',\n\t'gray-function',\n\t'hexadecimal-alpha-notation',\n\t'lab-function',\n\t'rebeccapurple-color',\n\t'color-mod-function', // run color-mod after other color modifications have finished\n\t'blank-pseudo-class',\n\t'break-properties',\n\t'font-variant-property',\n\t'has-pseudo-class',\n\t'gap-properties',\n\t'overflow-property',\n\t'overflow-wrap-property',\n\t'place-properties',\n\t'system-ui-font-family'\n];\n", "/* eslint max-params: [\"error\", 4] */\n\nimport fs from 'fs';\nimport path from 'path';\n\n/* Write Exports to CSS File\n/* ========================================================================== */\n\nfunction getCustomMediaAsCss(customMedia) {\n\tconst cssContent = Object.keys(customMedia).reduce((cssLines, name) => {\n\t\tcssLines.push(`@custom-media ${name} ${customMedia[name]};`);\n\n\t\treturn cssLines;\n\t}, []).join('\\n');\n\tconst css = `${cssContent}\\n`;\n\n\treturn css;\n}\n\nfunction getCustomPropertiesAsCss(customProperties) {\n\tconst cssContent = Object.keys(customProperties).reduce((cssLines, name) => {\n\t\tcssLines.push(`\\t${name}: ${customProperties[name]};`);\n\n\t\treturn cssLines;\n\t}, []).join('\\n');\n\tconst css = `:root {\\n${cssContent}\\n}\\n`;\n\n\treturn css;\n}\n\nfunction getCustomSelectorsAsCss(customSelectors) {\n\tconst cssContent = Object.keys(customSelectors).reduce((cssLines, name) => {\n\t\tcssLines.push(`@custom-selector ${name} ${customSelectors[name]};`);\n\n\t\treturn cssLines;\n\t}, []).join('\\n');\n\tconst css = `${cssContent}\\n`;\n\n\treturn css;\n}\n\nasync function writeExportsToCssFile(to, customMedia, customProperties, customSelectors) {\n\tconst customPropertiesAsCss = getCustomPropertiesAsCss(customProperties);\n\tconst customMediaAsCss = getCustomMediaAsCss(customMedia);\n\tconst customSelectorsAsCss = getCustomSelectorsAsCss(customSelectors);\n\tconst css = `${customMediaAsCss}\\n${customSelectorsAsCss}\\n${customPropertiesAsCss}`;\n\n\tawait writeFile(to, css);\n}\n\n/* Write Exports to JSON file\n/* ========================================================================== */\n\nasync function writeExportsToJsonFile(to, customMedia, customProperties, customSelectors) {\n\tconst jsonContent = JSON.stringify({\n\t\t'custom-media': customMedia,\n\t\t'custom-properties': customProperties,\n\t\t'custom-selectors': customSelectors\n\t}, null, '  ');\n\tconst json = `${jsonContent}\\n`;\n\n\tawait writeFile(to, json);\n}\n\n/* Write Exports to Common JS file\n/* ========================================================================== */\n\nfunction getObjectWithKeyAsCjs(key, object) {\n\tconst jsContents = Object.keys(object).reduce((jsLines, name) => {\n\t\tjsLines.push(`\\t\\t'${escapeForJS(name)}': '${escapeForJS(object[name])}'`);\n\n\t\treturn jsLines;\n\t}, []).join(',\\n');\n\tconst cjs = `\\n\\t${key}: {\\n${jsContents}\\n\\t}`;\n\n\treturn cjs;\n}\n\nasync function writeExportsToCjsFile(to, customMedia, customProperties, customSelectors) {\n\tconst customMediaAsCjs = getObjectWithKeyAsCjs('customMedia', customMedia);\n\tconst customPropertiesAsCjs = getObjectWithKeyAsCjs('customProperties', customProperties);\n\tconst customSelectorsAsCjs = getObjectWithKeyAsCjs('customSelectors', customSelectors);\n\tconst cjs = `module.exports = {${customMediaAsCjs},${customPropertiesAsCjs},${customSelectorsAsCjs}\\n};\\n`;\n\n\tawait writeFile(to, cjs);\n}\n\n/* Write Exports to Module JS file\n/* ========================================================================== */\n\nfunction getObjectWithKeyAsMjs(key, object) {\n\tconst mjsContents = Object.keys(object).reduce((mjsLines, name) => {\n\t\tmjsLines.push(`\\t'${escapeForJS(name)}': '${escapeForJS(object[name])}'`);\n\n\t\treturn mjsLines;\n\t}, []).join(',\\n');\n\tconst mjs = `export const ${key} = {\\n${mjsContents}\\n};\\n`;\n\n\treturn mjs;\n}\n\nasync function writeExportsToMjsFile(to, customMedia, customProperties, customSelectors) {\n\tconst customMediaAsMjs = getObjectWithKeyAsMjs('customMedia', customMedia);\n\tconst customPropertiesAsMjs = getObjectWithKeyAsMjs('customProperties', customProperties);\n\tconst customSelectorsAsMjs = getObjectWithKeyAsMjs('customSelectors', customSelectors);\n\tconst mjs = `${customMediaAsMjs}\\n${customPropertiesAsMjs}\\n${customSelectorsAsMjs}`;\n\n\tawait writeFile(to, mjs);\n}\n\n/* Write Exports to Exports\n/* ========================================================================== */\n\nexport default function writeToExports(customExports, destinations) {\n\treturn Promise.all([].concat(destinations).map(async destination => {\n\t\tif (destination instanceof Function) {\n\t\t\tawait destination({\n\t\t\t\tcustomMedia: getObjectWithStringifiedKeys(customExports.customMedia),\n\t\t\t\tcustomProperties: getObjectWithStringifiedKeys(customExports.customProperties),\n\t\t\t\tcustomSelectors: getObjectWithStringifiedKeys(customExports.customSelectors)\n\t\t\t});\n\t\t} else {\n\t\t\t// read the destination as an object\n\t\t\tconst opts = destination === Object(destination) ? destination : { to: String(destination) };\n\n\t\t\t// transformer for Exports into a JSON-compatible object\n\t\t\tconst toJSON = opts.toJSON || getObjectWithStringifiedKeys;\n\n\t\t\tif ('customMedia' in opts || 'customProperties' in opts || 'customSelectors' in opts) {\n\t\t\t\t// write directly to an object as customProperties\n\t\t\t\topts.customMedia = toJSON(customExports.customMedia);\n\t\t\t\topts.customProperties = toJSON(customExports.customProperties);\n\t\t\t\topts.customSelectors = toJSON(customExports.customSelectors);\n\t\t\t} else if ('custom-media' in opts || 'custom-properties' in opts || 'custom-selectors' in opts) {\n\t\t\t\t// write directly to an object as custom-properties\n\t\t\t\topts['custom-media'] = toJSON(customExports.customMedia);\n\t\t\t\topts['custom-properties'] = toJSON(customExports.customProperties);\n\t\t\t\topts['custom-selectors'] = toJSON(customExports.customSelectors);\n\t\t\t} else {\n\t\t\t\t// destination pathname\n\t\t\t\tconst to = String(opts.to || '');\n\n\t\t\t\t// type of file being written to\n\t\t\t\tconst type = (opts.type || path.extname(opts.to).slice(1)).toLowerCase();\n\n\t\t\t\t// transformed Exports\n\t\t\t\tconst customMediaJSON = toJSON(customExports.customMedia);\n\t\t\t\tconst customPropertiesJSON = toJSON(customExports.customProperties);\n\t\t\t\tconst customSelectorsJSON = toJSON(customExports.customSelectors);\n\n\t\t\t\tif (type === 'css') {\n\t\t\t\t\tawait writeExportsToCssFile(to, customMediaJSON, customPropertiesJSON, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'js') {\n\t\t\t\t\tawait writeExportsToCjsFile(to, customMediaJSON, customPropertiesJSON, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'json') {\n\t\t\t\t\tawait writeExportsToJsonFile(to, customMediaJSON, customPropertiesJSON, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'mjs') {\n\t\t\t\t\tawait writeExportsToMjsFile(to, customMediaJSON, customPropertiesJSON, customSelectorsJSON);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}));\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nfunction getObjectWithStringifiedKeys(object) {\n\treturn Object.keys(object).reduce((objectJSON, key) => {\n\t\tobjectJSON[key] = String(object[key]);\n\n\t\treturn objectJSON;\n\t}, {});\n}\n\nfunction writeFile(to, text) {\n\treturn new Promise((resolve, reject) => {\n\t\tfs.writeFile(to, text, error => {\n\t\t\tif (error) {\n\t\t\t\treject(error);\n\t\t\t} else {\n\t\t\t\tresolve();\n\t\t\t}\n\t\t});\n\t});\n}\n\nfunction escapeForJS(string) {\n\treturn string.replace(/\\\\([\\s\\S])|(')/g, '\\\\$1$2').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n}\n", "import autoprefixer from 'autoprefixer'\nimport browserslist from 'browserslist';\nimport cssdb from 'cssdb';\nimport postcss from 'postcss';\nimport plugins from './lib/plugins-by-id';\nimport getTransformedInsertions from './lib/get-transformed-insertions';\nimport getUnsupportedBrowsersByFeature from './lib/get-unsupported-browsers-by-feature';\nimport idsByExecutionOrder from './lib/ids-by-execution-order';\nimport writeToExports from './lib/write-to-exports';\n\nexport default postcss.plugin('postcss-preset-env', opts => {\n\t// initialize options\n\tconst features = Object(Object(opts).features);\n\tconst insertBefore = Object(Object(opts).insertBefore);\n\tconst insertAfter = Object(Object(opts).insertAfter);\n\tconst browsers = Object(opts).browsers;\n\tconst stage = 'stage' in Object(opts)\n\t\t? opts.stage === false\n\t\t\t? 5\n\t\t: parseInt(opts.stage) || 0\n\t: 2;\n\tconst autoprefixerOptions = Object(opts).autoprefixer;\n\tconst sharedOpts = initializeSharedOpts(Object(opts));\n\tconst stagedAutoprefixer = autoprefixerOptions === false\n\t\t? () => {}\n\t: autoprefixer(Object.assign({ overrideBrowserslist: browsers }, autoprefixerOptions));\n\n\t// polyfillable features (those with an available postcss plugin)\n\tconst polyfillableFeatures = cssdb.concat(\n\t\t// additional features to be inserted before cssdb features\n\t\tgetTransformedInsertions(insertBefore, 'insertBefore'),\n\t\t// additional features to be inserted after cssdb features\n\t\tgetTransformedInsertions(insertAfter, 'insertAfter')\n\t).filter(\n\t\t// inserted features or features with an available postcss plugin\n\t\tfeature => feature.insertBefore || feature.id in plugins\n\t).sort(\n\t\t// features sorted by execution order and then insertion order\n\t\t(a, b) => idsByExecutionOrder.indexOf(a.id) - idsByExecutionOrder.indexOf(b.id) || (a.insertBefore ? -1 : b.insertBefore ? 1 : 0) || (a.insertAfter ? 1 : b.insertAfter ? -1 : 0)\n\t).map(\n\t\t// polyfillable features as an object\n\t\tfeature => {\n\t\t\t// target browsers for the polyfill\n\t\t\tconst unsupportedBrowsers = getUnsupportedBrowsersByFeature(feature.caniuse);\n\n\t\t\treturn feature.insertBefore || feature.insertAfter ? {\n\t\t\t\tbrowsers: unsupportedBrowsers,\n\t\t\t\tplugin:   feature.plugin,\n\t\t\t\tid:       `${feature.insertBefore ? 'before' : 'after'}-${feature.id}`,\n\t\t\t\tstage:    6\n\t\t\t} : {\n\t\t\t\tbrowsers: unsupportedBrowsers,\n\t\t\t\tplugin:   plugins[feature.id],\n\t\t\t\tid:       feature.id,\n\t\t\t\tstage:    feature.stage\n\t\t\t};\n\t\t}\n\t);\n\n\t// staged features (those at or above the selected stage)\n\tconst stagedFeatures = polyfillableFeatures.filter(\n\t\tfeature => feature.id in features\n\t\t\t? features[feature.id]\n\t\t: feature.stage >= stage\n\t).map(\n\t\tfeature => ({\n\t\t\tbrowsers: feature.browsers,\n\t\t\tplugin: typeof feature.plugin.process === 'function'\n\t\t\t\t? features[feature.id] === true\n\t\t\t\t\t? sharedOpts\n\t\t\t\t\t\t// if the plugin is enabled and has shared options\n\t\t\t\t\t\t? feature.plugin(Object.assign({}, sharedOpts))\n\t\t\t\t\t// otherwise, if the plugin is enabled\n\t\t\t\t\t: feature.plugin()\n\t\t\t\t: sharedOpts\n\t\t\t\t\t// if the plugin has shared options and individual options\n\t\t\t\t\t? feature.plugin(Object.assign({}, sharedOpts, features[feature.id]))\n\t\t\t\t// if the plugin has individual options\n\t\t\t\t: feature.plugin(Object.assign({}, features[feature.id]))\n\t\t\t// if the plugin is already initialized\n\t\t\t: feature.plugin,\n\t\t\tid: feature.id\n\t\t})\n\t);\n\n\t// browsers supported by the configuration\n\tconst supportedBrowsers = browserslist(browsers, { ignoreUnknownVersions: true });\n\n\t// features supported by the stage and browsers\n\tconst supportedFeatures = stagedFeatures.filter(\n\t\tfeature => supportedBrowsers.some(\n\t\t\tsupportedBrowser => browserslist(feature.browsers, {\n\t\t\t\tignoreUnknownVersions: true\n\t\t\t}).some(\n\t\t\t\tpolyfillBrowser => polyfillBrowser === supportedBrowser\n\t\t\t)\n\t\t)\n\t);\n\n\treturn (root, result) => {\n\t\t// polyfills run in execution order\n\t\tconst polyfills = supportedFeatures.reduce(\n\t\t\t(promise, feature) => promise.then(\n\t\t\t\t() => feature.plugin(result.root, result)\n\t\t\t),\n\t\t\tPromise.resolve()\n\t\t).then(\n\t\t\t() => stagedAutoprefixer(result.root, result)\n\t\t).then(\n\t\t\t() => {\n\t\t\t\tif (Object(opts).exportTo) {\n\t\t\t\t\twriteToExports(sharedOpts.exportTo, opts.exportTo);\n\t\t\t\t}\n\t\t\t}\n\t\t)\n\n\t\treturn polyfills;\n\t};\n});\n\nconst initializeSharedOpts = opts => {\n\tif ('importFrom' in opts || 'exportTo' in opts || 'preserve' in opts) {\n\t\tconst sharedOpts = {};\n\n\t\tif ('importFrom' in opts) {\n\t\t\tsharedOpts.importFrom = opts.importFrom;\n\t\t}\n\n\t\tif ('exportTo' in opts) {\n\t\t\tsharedOpts.exportTo = {\n\t\t\t\tcustomMedia: {},\n\t\t\t\tcustomProperties: {},\n\t\t\t\tcustomSelectors: {},\n\t\t\t};\n\t\t}\n\n\t\tif ('preserve' in opts) {\n\t\t\tsharedOpts.preserve = opts.preserve;\n\t\t}\n\n\t\treturn sharedOpts;\n\t}\n\n\treturn false;\n};\n"], "names": ["postcss", "plugin", "root", "walkDecls", "propertyRegExp", "decl", "value", "replace", "systemUiMatch", "systemUiReplace", "whitespace", "systemUiFamily", "RegExp", "join", "postcssInitial", "postcssPseudoClassAnyLink", "postcssBlankPseudo", "postcssPageBreak", "postcssAttributeCaseInsensitive", "postcssColorFunctionalNotation", "postcssColorModFunction", "postcssCustomMedia", "postcssCustomProperties", "postcssCustomSelectors", "postcssDirPseudoClass", "postcssDoublePositionGradients", "postcssEnvFunction", "postcssFocusVisible", "postcssFocusWithin", "postcssFontVariant", "postcssGapProperties", "postcssColorGray", "postcssHasPseudo", "postcssColorHexAlpha", "postcssImageSetPolyfill", "postcssLabFunction", "postcssLogical", "postcssSelectorMatches", "postcssMediaMinmax", "postcssNesting", "postcssSelectorNot", "postcssOverflowShorthand", "postcssReplaceOverflowWrap", "postcss<PERSON>lace", "postcssPrefersColorScheme", "postcssColorRebeccapurple", "postcssFontFamilySystemUi", "getTransformedInsertions", "insertions", "placement", "Object", "keys", "map", "id", "concat", "reduce", "array", "feature", "getUnsupportedBrowsersByFeature", "caniuseFeature", "caniuse", "stats", "results", "browsers", "browser", "filter", "version", "indexOf", "getCustomMediaAsCss", "customMedia", "cssContent", "cssLines", "name", "push", "css", "getCustomPropertiesAsCss", "customProperties", "getCustomSelectorsAsCss", "customSelectors", "writeExportsToCssFile", "to", "customPropertiesAsCss", "customMediaAsCss", "customSelectorsAsCss", "writeFile", "writeExportsToJsonFile", "json<PERSON><PERSON><PERSON>", "JSON", "stringify", "json", "getObjectWithKeyAsCjs", "key", "object", "js<PERSON><PERSON>nts", "jsLines", "escapeForJS", "cjs", "writeExportsToCjsFile", "customMediaAsCjs", "customPropertiesAsCjs", "customSelectorsAsCjs", "getObjectWithKeyAsMjs", "mjs<PERSON><PERSON><PERSON>", "mjsLines", "mjs", "writeExportsToMjsFile", "customMediaAsMjs", "customPropertiesAsMjs", "customSelectorsAsMjs", "writeToExports", "customExports", "destinations", "Promise", "all", "destination", "Function", "getObjectWithStringifiedKeys", "opts", "String", "toJSON", "type", "path", "extname", "slice", "toLowerCase", "customMediaJSON", "customPropertiesJSON", "customSelectorsJSON", "objectJSON", "text", "resolve", "reject", "fs", "error", "string", "features", "insertBefore", "insertAfter", "stage", "parseInt", "autoprefixerOptions", "autoprefixer", "sharedOpts", "initializeSharedOpts", "stagedAutoprefixer", "assign", "overrideBrowserslist", "polyfillableFeatures", "cssdb", "plugins", "sort", "a", "b", "idsByExecutionOrder", "unsupportedBrowsers", "stagedFeatures", "process", "supportedBrowsers", "browserslist", "ignoreUnknownVersions", "supportedFeatures", "some", "supportedBrowser", "polyfill<PERSON>rowser", "result", "polyfills", "promise", "then", "exportTo", "importFrom", "preserve"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,gCAAeA,SAAO,CAACC,MAAR,CAAe,wBAAf,EAAyC,MAAMC,IAAI,IAAI;EACrEA,IAAI,CAACC,SAAL,CAAeC,cAAf,EAA+BC,IAAI,IAAI;IACtCA,IAAI,CAACC,KAAL,GAAaD,IAAI,CAACC,KAAL,CAAWC,OAAX,CAAmBC,aAAnB,EAAkCC,eAAlC,CAAb;GADD;CADc,CAAf;AAMA,MAAML,cAAc,GAAG,8CAAvB;AACA,MAAMM,UAAU,GAAG,uBAAnB;AACA,MAAMC,cAAc,GAAG,CACtB,WADsB;;AAEE,eAFF;;AAGL,UAHK;;AAIL,QAJK;;AAKF,QALE;;AAMP,WANO;;AAOF,WAPE;;AAQP,YARO,CAAvB;AAUA,MAAMH,aAAa,GAAG,IAAII,MAAJ,CAAY,QAAOF,UAAW,iBAAgBA,UAAW,SAAQA,UAAW,OAAMC,cAAc,CAACE,IAAf,CAAoB,GAApB,CAAyB,IAAGH,UAAW,UAAzH,EAAoI,GAApI,CAAtB;AACA,MAAMD,eAAe,GAAI,KAAIE,cAAc,CAACE,IAAf,CAAoB,IAApB,CAA0B,IAAvD;;ACcA,cAAe;kBACEC,cADF;2BAEWC,yBAFX;wBAGQC,kBAHR;sBAIMC,gBAJN;iCAKiBC,+BALjB;+BAMeC,8BANf;wBAOQC,uBAPR;0BAQUC,kBARV;uBASOC,uBATP;sBAUMC,sBAVN;sBAWMC,qBAXN;+BAYeC,8BAZf;2BAaWC,kBAbX;gCAcgBC,mBAdhB;+BAeeC,kBAff;2BAgBWC,kBAhBX;oBAiBIC,oBAjBJ;mBAkBGC,gBAlBH;sBAmBMC,gBAnBN;gCAoBgBC,oBApBhB;wBAqBQC,uBArBR;kBAsBEC,kBAtBF;mCAuBmBC,cAvBnB;0BAwBUC,sBAxBV;wBAyBQC,kBAzBR;mBA0BGC,cA1BH;sBA2BMC,kBA3BN;uBA4BOC,wBA5BP;4BA6BYC,0BA7BZ;sBA8BMC,YA9BN;gCA+BgBC,yBA/BhB;yBAgCSC,yBAhCT;2BAiCWC;CAjC1B;;ACnCA;AACA,AAAe,SAASC,wBAAT,CAAkCC,UAAlC,EAA8CC,SAA9C,EAAyD;SAChEC,MAAM,CAACC,IAAP,CAAYH,UAAZ,EAAwBI,GAAxB,CACNC,EAAE,IAAI,GAAGC,MAAH,CAAUN,UAAU,CAACK,EAAD,CAApB,EAA0BD,GAA1B,CACLnD,MAAM,KAAK;KACTgD,SAAD,GAAa,IADH;IAEVhD,MAFU;IAGVoD;GAHK,CADD,CADA,EAQLE,MARK,CASN,CAACC,KAAD,EAAQC,OAAR,KAAoBD,KAAK,CAACF,MAAN,CAAaG,OAAb,CATd,EASqC,EATrC,CAAP;;;ACCc,SAASC,+BAAT,CAAyCD,OAAzC,EAAkD;QAC1DE,cAAc,GAAGC,gBAAA,CAAiBH,OAAjB,CAAvB,CADgE;;MAI5DE,cAAJ,EAAoB;UACbE,KAAK,GAAGD,eAAA,CAAgBD,cAAhB,EAAgCE,KAA9C,CADmB;;UAIbC,OAAO,GAAGZ,MAAM,CAACC,IAAP,CAAYU,KAAZ,EAAmBN,MAAnB,CACf,CAACQ,QAAD,EAAWC,OAAX,KAAuBD,QAAQ,CAACT,MAAT,CACtBJ,MAAM,CAACC,IAAP,CAAYU,KAAK,CAACG,OAAD,CAAjB,EAA4BC,MAA5B,CACCC,OAAO,IAAIL,KAAK,CAACG,OAAD,CAAL,CAAeE,OAAf,EAAwBC,OAAxB,CAAgC,GAAhC,MAAyC,CADrD,EAEEf,GAFF,CAGCc,OAAO,IAAK,GAAEF,OAAQ,IAAGE,OAAQ,EAHlC,CADsB,CADR,EAQf,EARe,CAAhB;WAWOJ,OAAP;GAfD,MAgBO;;WAEC,CAAE,MAAF,CAAP;;;;ACzBF;AACA,0BAAe,CACd,sBADc,EAEd,mBAFc,EAGd,uBAHc;AAId,oBAJc;AAKd,oBALc;AAMd,4BANc;AAOd,eAPc,EAQd,kBARc;AASd,uBATc,EAUd,6BAVc,EAWd,4BAXc,EAYd,2BAZc,EAad,sBAbc;AAcd,kBAdc;AAed,+BAfc;AAgBd,kBAhBc,EAiBd,cAjBc;AAkBd,2BAlBc,EAmBd,2BAnBc,EAoBd,eApBc,EAqBd,4BArBc,EAsBd,cAtBc,EAuBd,qBAvBc,EAwBd,oBAxBc;AAyBd,oBAzBc,EA0Bd,kBA1Bc,EA2Bd,uBA3Bc,EA4Bd,kBA5Bc,EA6Bd,gBA7Bc,EA8Bd,mBA9Bc,EA+Bd,wBA/Bc,EAgCd,kBAhCc,EAiCd,uBAjCc,CAAf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACIA;;;AAGA,SAASM,mBAAT,CAA6BC,WAA7B,EAA0C;QACnCC,UAAU,GAAGpB,MAAM,CAACC,IAAP,CAAYkB,WAAZ,EAAyBd,MAAzB,CAAgC,CAACgB,QAAD,EAAWC,IAAX,KAAoB;IACtED,QAAQ,CAACE,IAAT,CAAe,iBAAgBD,IAAK,IAAGH,WAAW,CAACG,IAAD,CAAO,GAAzD;WAEOD,QAAP;GAHkB,EAIhB,EAJgB,EAIZ1D,IAJY,CAIP,IAJO,CAAnB;QAKM6D,GAAG,GAAI,GAAEJ,UAAW,IAA1B;SAEOI,GAAP;;;AAGD,SAASC,wBAAT,CAAkCC,gBAAlC,EAAoD;QAC7CN,UAAU,GAAGpB,MAAM,CAACC,IAAP,CAAYyB,gBAAZ,EAA8BrB,MAA9B,CAAqC,CAACgB,QAAD,EAAWC,IAAX,KAAoB;IAC3ED,QAAQ,CAACE,IAAT,CAAe,KAAID,IAAK,KAAII,gBAAgB,CAACJ,IAAD,CAAO,GAAnD;WAEOD,QAAP;GAHkB,EAIhB,EAJgB,EAIZ1D,IAJY,CAIP,IAJO,CAAnB;QAKM6D,GAAG,GAAI,YAAWJ,UAAW,OAAnC;SAEOI,GAAP;;;AAGD,SAASG,uBAAT,CAAiCC,eAAjC,EAAkD;QAC3CR,UAAU,GAAGpB,MAAM,CAACC,IAAP,CAAY2B,eAAZ,EAA6BvB,MAA7B,CAAoC,CAACgB,QAAD,EAAWC,IAAX,KAAoB;IAC1ED,QAAQ,CAACE,IAAT,CAAe,oBAAmBD,IAAK,IAAGM,eAAe,CAACN,IAAD,CAAO,GAAhE;WAEOD,QAAP;GAHkB,EAIhB,EAJgB,EAIZ1D,IAJY,CAIP,IAJO,CAAnB;QAKM6D,GAAG,GAAI,GAAEJ,UAAW,IAA1B;SAEOI,GAAP;;;SAGcK;;;;;;;;6CAAf,WAAqCC,EAArC,EAAyCX,WAAzC,EAAsDO,gBAAtD,EAAwEE,eAAxE,EAAyF;UAClFG,qBAAqB,GAAGN,wBAAwB,CAACC,gBAAD,CAAtD;UACMM,gBAAgB,GAAGd,mBAAmB,CAACC,WAAD,CAA5C;UACMc,oBAAoB,GAAGN,uBAAuB,CAACC,eAAD,CAApD;UACMJ,GAAG,GAAI,GAAEQ,gBAAiB,KAAIC,oBAAqB,KAAIF,qBAAsB,EAAnF;UAEMG,SAAS,CAACJ,EAAD,EAAKN,GAAL,CAAf;;;;;SAMcW;;;;;;;;8CAAf,WAAsCL,EAAtC,EAA0CX,WAA1C,EAAuDO,gBAAvD,EAAyEE,eAAzE,EAA0F;UACnFQ,WAAW,GAAGC,IAAI,CAACC,SAAL,CAAe;sBAClBnB,WADkB;2BAEbO,gBAFa;0BAGdE;KAHD,EAIjB,IAJiB,EAIX,IAJW,CAApB;UAKMW,IAAI,GAAI,GAAEH,WAAY,IAA5B;UAEMF,SAAS,CAACJ,EAAD,EAAKS,IAAL,CAAf;;;;;AAMD,SAASC,qBAAT,CAA+BC,GAA/B,EAAoCC,MAApC,EAA4C;QACrCC,UAAU,GAAG3C,MAAM,CAACC,IAAP,CAAYyC,MAAZ,EAAoBrC,MAApB,CAA2B,CAACuC,OAAD,EAAUtB,IAAV,KAAmB;IAChEsB,OAAO,CAACrB,IAAR,CAAc,QAAOsB,WAAW,CAACvB,IAAD,CAAO,OAAMuB,WAAW,CAACH,MAAM,CAACpB,IAAD,CAAP,CAAe,GAAvE;WAEOsB,OAAP;GAHkB,EAIhB,EAJgB,EAIZjF,IAJY,CAIP,KAJO,CAAnB;QAKMmF,GAAG,GAAI,OAAML,GAAI,QAAOE,UAAW,OAAzC;SAEOG,GAAP;;;SAGcC;;;;;;;;6CAAf,WAAqCjB,EAArC,EAAyCX,WAAzC,EAAsDO,gBAAtD,EAAwEE,eAAxE,EAAyF;UAClFoB,gBAAgB,GAAGR,qBAAqB,CAAC,aAAD,EAAgBrB,WAAhB,CAA9C;UACM8B,qBAAqB,GAAGT,qBAAqB,CAAC,kBAAD,EAAqBd,gBAArB,CAAnD;UACMwB,oBAAoB,GAAGV,qBAAqB,CAAC,iBAAD,EAAoBZ,eAApB,CAAlD;UACMkB,GAAG,GAAI,qBAAoBE,gBAAiB,IAAGC,qBAAsB,IAAGC,oBAAqB,QAAnG;UAEMhB,SAAS,CAACJ,EAAD,EAAKgB,GAAL,CAAf;;;;;AAMD,SAASK,qBAAT,CAA+BV,GAA/B,EAAoCC,MAApC,EAA4C;QACrCU,WAAW,GAAGpD,MAAM,CAACC,IAAP,CAAYyC,MAAZ,EAAoBrC,MAApB,CAA2B,CAACgD,QAAD,EAAW/B,IAAX,KAAoB;IAClE+B,QAAQ,CAAC9B,IAAT,CAAe,MAAKsB,WAAW,CAACvB,IAAD,CAAO,OAAMuB,WAAW,CAACH,MAAM,CAACpB,IAAD,CAAP,CAAe,GAAtE;WAEO+B,QAAP;GAHmB,EAIjB,EAJiB,EAIb1F,IAJa,CAIR,KAJQ,CAApB;QAKM2F,GAAG,GAAI,gBAAeb,GAAI,SAAQW,WAAY,QAApD;SAEOE,GAAP;;;SAGcC;;;;;;;;6CAAf,WAAqCzB,EAArC,EAAyCX,WAAzC,EAAsDO,gBAAtD,EAAwEE,eAAxE,EAAyF;UAClF4B,gBAAgB,GAAGL,qBAAqB,CAAC,aAAD,EAAgBhC,WAAhB,CAA9C;UACMsC,qBAAqB,GAAGN,qBAAqB,CAAC,kBAAD,EAAqBzB,gBAArB,CAAnD;UACMgC,oBAAoB,GAAGP,qBAAqB,CAAC,iBAAD,EAAoBvB,eAApB,CAAlD;UACM0B,GAAG,GAAI,GAAEE,gBAAiB,KAAIC,qBAAsB,KAAIC,oBAAqB,EAAnF;UAEMxB,SAAS,CAACJ,EAAD,EAAKwB,GAAL,CAAf;;;;;AAMD,AAAe,SAASK,cAAT,CAAwBC,aAAxB,EAAuCC,YAAvC,EAAqD;SAC5DC,OAAO,CAACC,GAAR,CAAY,GAAG3D,MAAH,CAAUyD,YAAV,EAAwB3D,GAAxB;;;iCAA4B,WAAM8D,WAAN,EAAqB;UAC/DA,WAAW,YAAYC,QAA3B,EAAqC;cAC9BD,WAAW,CAAC;UACjB7C,WAAW,EAAE+C,4BAA4B,CAACN,aAAa,CAACzC,WAAf,CADxB;UAEjBO,gBAAgB,EAAEwC,4BAA4B,CAACN,aAAa,CAAClC,gBAAf,CAF7B;UAGjBE,eAAe,EAAEsC,4BAA4B,CAACN,aAAa,CAAChC,eAAf;SAH7B,CAAjB;OADD,MAMO;;cAEAuC,IAAI,GAAGH,WAAW,KAAKhE,MAAM,CAACgE,WAAD,CAAtB,GAAsCA,WAAtC,GAAoD;UAAElC,EAAE,EAAEsC,MAAM,CAACJ,WAAD;SAA7E,CAFM;;cAKAK,MAAM,GAAGF,IAAI,CAACE,MAAL,IAAeH,4BAA9B;;YAEI,iBAAiBC,IAAjB,IAAyB,sBAAsBA,IAA/C,IAAuD,qBAAqBA,IAAhF,EAAsF;;UAErFA,IAAI,CAAChD,WAAL,GAAmBkD,MAAM,CAACT,aAAa,CAACzC,WAAf,CAAzB;UACAgD,IAAI,CAACzC,gBAAL,GAAwB2C,MAAM,CAACT,aAAa,CAAClC,gBAAf,CAA9B;UACAyC,IAAI,CAACvC,eAAL,GAAuByC,MAAM,CAACT,aAAa,CAAChC,eAAf,CAA7B;SAJD,MAKO,IAAI,kBAAkBuC,IAAlB,IAA0B,uBAAuBA,IAAjD,IAAyD,sBAAsBA,IAAnF,EAAyF;;UAE/FA,IAAI,CAAC,cAAD,CAAJ,GAAuBE,MAAM,CAACT,aAAa,CAACzC,WAAf,CAA7B;UACAgD,IAAI,CAAC,mBAAD,CAAJ,GAA4BE,MAAM,CAACT,aAAa,CAAClC,gBAAf,CAAlC;UACAyC,IAAI,CAAC,kBAAD,CAAJ,GAA2BE,MAAM,CAACT,aAAa,CAAChC,eAAf,CAAjC;SAJM,MAKA;;gBAEAE,EAAE,GAAGsC,MAAM,CAACD,IAAI,CAACrC,EAAL,IAAW,EAAZ,CAAjB,CAFM;;gBAKAwC,IAAI,GAAG,CAACH,IAAI,CAACG,IAAL,IAAaC,IAAI,CAACC,OAAL,CAAaL,IAAI,CAACrC,EAAlB,EAAsB2C,KAAtB,CAA4B,CAA5B,CAAd,EAA8CC,WAA9C,EAAb,CALM;;gBAQAC,eAAe,GAAGN,MAAM,CAACT,aAAa,CAACzC,WAAf,CAA9B;gBACMyD,oBAAoB,GAAGP,MAAM,CAACT,aAAa,CAAClC,gBAAf,CAAnC;gBACMmD,mBAAmB,GAAGR,MAAM,CAACT,aAAa,CAAChC,eAAf,CAAlC;;cAEI0C,IAAI,KAAK,KAAb,EAAoB;kBACbzC,qBAAqB,CAACC,EAAD,EAAK6C,eAAL,EAAsBC,oBAAtB,EAA4CC,mBAA5C,CAA3B;;;cAGGP,IAAI,KAAK,IAAb,EAAmB;kBACZvB,qBAAqB,CAACjB,EAAD,EAAK6C,eAAL,EAAsBC,oBAAtB,EAA4CC,mBAA5C,CAA3B;;;cAGGP,IAAI,KAAK,MAAb,EAAqB;kBACdnC,sBAAsB,CAACL,EAAD,EAAK6C,eAAL,EAAsBC,oBAAtB,EAA4CC,mBAA5C,CAA5B;;;cAGGP,IAAI,KAAK,KAAb,EAAoB;kBACbf,qBAAqB,CAACzB,EAAD,EAAK6C,eAAL,EAAsBC,oBAAtB,EAA4CC,mBAA5C,CAA3B;;;;KAjDe;;;;;MAAZ,CAAP;;;;;AA2DD,SAASX,4BAAT,CAAsCxB,MAAtC,EAA8C;SACtC1C,MAAM,CAACC,IAAP,CAAYyC,MAAZ,EAAoBrC,MAApB,CAA2B,CAACyE,UAAD,EAAarC,GAAb,KAAqB;IACtDqC,UAAU,CAACrC,GAAD,CAAV,GAAkB2B,MAAM,CAAC1B,MAAM,CAACD,GAAD,CAAP,CAAxB;WAEOqC,UAAP;GAHM,EAIJ,EAJI,CAAP;;;AAOD,SAAS5C,SAAT,CAAmBJ,EAAnB,EAAuBiD,IAAvB,EAA6B;SACrB,IAAIjB,OAAJ,CAAY,CAACkB,OAAD,EAAUC,MAAV,KAAqB;IACvCC,EAAE,CAAChD,SAAH,CAAaJ,EAAb,EAAiBiD,IAAjB,EAAuBI,KAAK,IAAI;UAC3BA,KAAJ,EAAW;QACVF,MAAM,CAACE,KAAD,CAAN;OADD,MAEO;QACNH,OAAO;;KAJT;GADM,CAAP;;;AAWD,SAASnC,WAAT,CAAqBuC,MAArB,EAA6B;SACrBA,MAAM,CAAC/H,OAAP,CAAe,iBAAf,EAAkC,QAAlC,EAA4CA,OAA5C,CAAoD,KAApD,EAA2D,KAA3D,EAAkEA,OAAlE,CAA0E,KAA1E,EAAiF,KAAjF,CAAP;;;ACxLD,cAAeP,SAAO,CAACC,MAAR,CAAe,oBAAf,EAAqCoH,IAAI,IAAI;;QAErDkB,QAAQ,GAAGrF,MAAM,CAACA,MAAM,CAACmE,IAAD,CAAN,CAAakB,QAAd,CAAvB;QACMC,YAAY,GAAGtF,MAAM,CAACA,MAAM,CAACmE,IAAD,CAAN,CAAamB,YAAd,CAA3B;QACMC,WAAW,GAAGvF,MAAM,CAACA,MAAM,CAACmE,IAAD,CAAN,CAAaoB,WAAd,CAA1B;QACM1E,QAAQ,GAAGb,MAAM,CAACmE,IAAD,CAAN,CAAatD,QAA9B;QACM2E,KAAK,GAAG,WAAWxF,MAAM,CAACmE,IAAD,CAAjB,GACXA,IAAI,CAACqB,KAAL,KAAe,KAAf,GACC,CADD,GAEAC,QAAQ,CAACtB,IAAI,CAACqB,KAAN,CAAR,IAAwB,CAHb,GAIZ,CAJF;QAKME,mBAAmB,GAAG1F,MAAM,CAACmE,IAAD,CAAN,CAAawB,YAAzC;QACMC,UAAU,GAAGC,oBAAoB,CAAC7F,MAAM,CAACmE,IAAD,CAAP,CAAvC;QACM2B,kBAAkB,GAAGJ,mBAAmB,KAAK,KAAxB,GACxB,MAAM,EADkB,GAEzBC,YAAY,CAAC3F,MAAM,CAAC+F,MAAP,CAAc;IAAEC,oBAAoB,EAAEnF;GAAtC,EAAkD6E,mBAAlD,CAAD,CAFd,CAb2D;;QAkBrDO,oBAAoB,GAAGC,KAAK,CAAC9F,MAAN;EAE5BP,wBAAwB,CAACyF,YAAD,EAAe,cAAf,CAFI;EAI5BzF,wBAAwB,CAAC0F,WAAD,EAAc,aAAd,CAJI,EAK3BxE,MAL2B;EAO5BR,OAAO,IAAIA,OAAO,CAAC+E,YAAR,IAAwB/E,OAAO,CAACJ,EAAR,IAAcgG,OAPrB,EAQ3BC,IAR2B;GAU3BC,CAAD,EAAIC,CAAJ,KAAUC,mBAAmB,CAACtF,OAApB,CAA4BoF,CAAC,CAAClG,EAA9B,IAAoCoG,mBAAmB,CAACtF,OAApB,CAA4BqF,CAAC,CAACnG,EAA9B,CAApC,KAA0EkG,CAAC,CAACf,YAAF,GAAiB,CAAC,CAAlB,GAAsBgB,CAAC,CAAChB,YAAF,GAAiB,CAAjB,GAAqB,CAArH,MAA4He,CAAC,CAACd,WAAF,GAAgB,CAAhB,GAAoBe,CAAC,CAACf,WAAF,GAAgB,CAAC,CAAjB,GAAqB,CAArK,CAVkB,EAW3BrF,GAX2B;EAa5BK,OAAO,IAAI;;UAEJiG,mBAAmB,GAAGhG,+BAA+B,CAACD,OAAO,CAACG,OAAT,CAA3D;WAEOH,OAAO,CAAC+E,YAAR,IAAwB/E,OAAO,CAACgF,WAAhC,GAA8C;MACpD1E,QAAQ,EAAE2F,mBAD0C;MAEpDzJ,MAAM,EAAIwD,OAAO,CAACxD,MAFkC;MAGpDoD,EAAE,EAAS,GAAEI,OAAO,CAAC+E,YAAR,GAAuB,QAAvB,GAAkC,OAAQ,IAAG/E,OAAO,CAACJ,EAAG,EAHjB;MAIpDqF,KAAK,EAAK;KAJJ,GAKH;MACH3E,QAAQ,EAAE2F,mBADP;MAEHzJ,MAAM,EAAIoJ,OAAO,CAAC5F,OAAO,CAACJ,EAAT,CAFd;MAGHA,EAAE,EAAQI,OAAO,CAACJ,EAHf;MAIHqF,KAAK,EAAKjF,OAAO,CAACiF;KATnB;GAjB2B,CAA7B,CAlB2D;;QAkDrDiB,cAAc,GAAGR,oBAAoB,CAAClF,MAArB,CACtBR,OAAO,IAAIA,OAAO,CAACJ,EAAR,IAAckF,QAAd,GACRA,QAAQ,CAAC9E,OAAO,CAACJ,EAAT,CADA,GAETI,OAAO,CAACiF,KAAR,IAAiBA,KAHG,EAIrBtF,GAJqB,CAKtBK,OAAO,KAAK;IACXM,QAAQ,EAAEN,OAAO,CAACM,QADP;IAEX9D,MAAM,EAAE,OAAOwD,OAAO,CAACxD,MAAR,CAAe2J,OAAtB,KAAkC,UAAlC,GACLrB,QAAQ,CAAC9E,OAAO,CAACJ,EAAT,CAAR,KAAyB,IAAzB,GACCyF,UAAU;MAETrF,OAAO,CAACxD,MAAR,CAAeiD,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBH,UAAlB,CAAf,CAFS;MAIVrF,OAAO,CAACxD,MAAR,EALD,GAMA6I,UAAU;MAETrF,OAAO,CAACxD,MAAR,CAAeiD,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBH,UAAlB,EAA8BP,QAAQ,CAAC9E,OAAO,CAACJ,EAAT,CAAtC,CAAf,CAFS;MAIVI,OAAO,CAACxD,MAAR,CAAeiD,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBV,QAAQ,CAAC9E,OAAO,CAACJ,EAAT,CAA1B,CAAf,CAXK;MAaNI,OAAO,CAACxD,MAfC;IAgBXoD,EAAE,EAAEI,OAAO,CAACJ;GAhBN,CALe,CAAvB,CAlD2D;;QA4ErDwG,iBAAiB,GAAGC,YAAY,CAAC/F,QAAD,EAAW;IAAEgG,qBAAqB,EAAE;GAApC,CAAtC,CA5E2D;;QA+ErDC,iBAAiB,GAAGL,cAAc,CAAC1F,MAAf,CACzBR,OAAO,IAAIoG,iBAAiB,CAACI,IAAlB,CACVC,gBAAgB,IAAIJ,YAAY,CAACrG,OAAO,CAACM,QAAT,EAAmB;IAClDgG,qBAAqB,EAAE;GADQ,CAAZ,CAEjBE,IAFiB,CAGnBE,eAAe,IAAIA,eAAe,KAAKD,gBAHpB,CADV,CADc,CAA1B;SAUO,CAAChK,IAAD,EAAOkK,MAAP,KAAkB;;UAElBC,SAAS,GAAGL,iBAAiB,CAACzG,MAAlB,CACjB,CAAC+G,OAAD,EAAU7G,OAAV,KAAsB6G,OAAO,CAACC,IAAR,CACrB,MAAM9G,OAAO,CAACxD,MAAR,CAAemK,MAAM,CAAClK,IAAtB,EAA4BkK,MAA5B,CADe,CADL,EAIjBpD,OAAO,CAACkB,OAAR,EAJiB,EAKhBqC,IALgB,CAMjB,MAAMvB,kBAAkB,CAACoB,MAAM,CAAClK,IAAR,EAAckK,MAAd,CANP,EAOhBG,IAPgB,CAQjB,MAAM;UACDrH,MAAM,CAACmE,IAAD,CAAN,CAAamD,QAAjB,EAA2B;QAC1B3D,cAAc,CAACiC,UAAU,CAAC0B,QAAZ,EAAsBnD,IAAI,CAACmD,QAA3B,CAAd;;KAVe,CAAlB;WAeOH,SAAP;GAjBD;CAzFc,CAAf;;AA8GA,MAAMtB,oBAAoB,GAAG1B,IAAI,IAAI;MAChC,gBAAgBA,IAAhB,IAAwB,cAAcA,IAAtC,IAA8C,cAAcA,IAAhE,EAAsE;UAC/DyB,UAAU,GAAG,EAAnB;;QAEI,gBAAgBzB,IAApB,EAA0B;MACzByB,UAAU,CAAC2B,UAAX,GAAwBpD,IAAI,CAACoD,UAA7B;;;QAGG,cAAcpD,IAAlB,EAAwB;MACvByB,UAAU,CAAC0B,QAAX,GAAsB;QACrBnG,WAAW,EAAE,EADQ;QAErBO,gBAAgB,EAAE,EAFG;QAGrBE,eAAe,EAAE;OAHlB;;;QAOG,cAAcuC,IAAlB,EAAwB;MACvByB,UAAU,CAAC4B,QAAX,GAAsBrD,IAAI,CAACqD,QAA3B;;;WAGM5B,UAAP;;;SAGM,KAAP;CAvBD;;;;"}