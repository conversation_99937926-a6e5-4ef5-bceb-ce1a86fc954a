{"version": 3, "file": "index.cjs.js", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport parser from 'postcss-values-parser';\n\nconst placeMatch = /^place-(content|items|self)/;\n\nexport default postcss.plugin('postcss-place', opts => {\n\t// prepare options\n\tconst preserve = 'preserve' in Object(opts) ? Boolean(opts.prefix) : true;\n\n\treturn root => {\n\t\t// walk each matching declaration\n\t\troot.walkDecls(placeMatch, decl => {\n\t\t\t// alignment\n\t\t\tconst alignment = decl.prop.match(placeMatch)[1];\n\n\t\t\t// value ast and child nodes\n\t\t\tconst value = parser(decl.value).parse();\n\t\t\tconst children = value.nodes[0].nodes;\n\n\t\t\t// new justify-[alignment] and align-[alignment] declarations\n\t\t\tconst alignValue = children.length === 1 ? decl.value : String(children.slice(0, 1)).trim();\n\t\t\tconst justifyValue = children.length === 1 ? decl.value : String(children.slice(1)).trim();\n\n\t\t\tdecl.cloneBefore({\n\t\t\t\tprop: `align-${alignment}`,\n\t\t\t\tvalue: alignValue\n\t\t\t});\n\n\t\t\tdecl.cloneBefore({\n\t\t\t\tprop: `justify-${alignment}`,\n\t\t\t\tvalue: justifyValue\n\t\t\t});\n\n\t\t\t// conditionally remove place-[alignment]\n\t\t\tif (!preserve) {\n\t\t\t\tdecl.remove();\n\t\t\t}\n\t\t});\n\t};\n});\n"], "names": ["placeMatch", "postcss", "plugin", "opts", "preserve", "Object", "Boolean", "prefix", "root", "walkDecls", "decl", "alignment", "prop", "match", "value", "parser", "parse", "children", "nodes", "alignValue", "length", "String", "slice", "trim", "justifyValue", "cloneBefore", "remove"], "mappings": ";;;;;;;AAGA,MAAMA,UAAU,GAAG,6BAAnB;AAEA,YAAeC,OAAO,CAACC,MAAR,CAAe,eAAf,EAAgCC,IAAI,IAAI;;QAEhDC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACI,MAAN,CAApC,GAAoD,IAArE;SAEOC,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeT,UAAf,EAA2BU,IAAI,IAAI;;YAE5BC,SAAS,GAAGD,IAAI,CAACE,IAAL,CAAUC,KAAV,CAAgBb,UAAhB,EAA4B,CAA5B,CAAlB,CAFkC;;YAK5Bc,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACI,KAAN,CAAN,CAAmBE,KAAnB,EAAd;YACMC,QAAQ,GAAGH,KAAK,CAACI,KAAN,CAAY,CAAZ,EAAeA,KAAhC,CANkC;;YAS5BC,UAAU,GAAGF,QAAQ,CAACG,MAAT,KAAoB,CAApB,GAAwBV,IAAI,CAACI,KAA7B,GAAqCO,MAAM,CAACJ,QAAQ,CAACK,KAAT,CAAe,CAAf,EAAkB,CAAlB,CAAD,CAAN,CAA6BC,IAA7B,EAAxD;YACMC,YAAY,GAAGP,QAAQ,CAACG,MAAT,KAAoB,CAApB,GAAwBV,IAAI,CAACI,KAA7B,GAAqCO,MAAM,CAACJ,QAAQ,CAACK,KAAT,CAAe,CAAf,CAAD,CAAN,CAA0BC,IAA1B,EAA1D;MAEAb,IAAI,CAACe,WAAL,CAAiB;QAChBb,IAAI,EAAG,SAAQD,SAAU,EADT;QAEhBG,KAAK,EAAEK;OAFR;MAKAT,IAAI,CAACe,WAAL,CAAiB;QAChBb,IAAI,EAAG,WAAUD,SAAU,EADX;QAEhBG,KAAK,EAAEU;OAFR,EAjBkC;;UAuB9B,CAACpB,QAAL,EAAe;QACdM,IAAI,CAACgB,MAAL;;KAxBF;GAFD;CAJc,CAAf;;;;"}