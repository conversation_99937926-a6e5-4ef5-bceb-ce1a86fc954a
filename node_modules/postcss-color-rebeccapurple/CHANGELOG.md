# 4.0.1 - 2018-09-18

- Updated: PostCSS Values Parser v2+

# 4.0.0 - 2018-09-17

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

# 3.1.0 - 2018-05-01

- Improve `rebeccapurple` pre-parse word detection
- Switched from `postcss-value-parser` to `postcss-values-parser`
- Bump `postcss` from `^6.0.1` to `^6.0.22`

# 3.0.0 - 2017-05-10

- Added: compatibility with postcss v6.x

# 2.0.1 - 2016-11-28

- Bump `color` dependency version
([postcss-cssnext/#327](https://github.com/MoOx/postcss-cssnext/issues/327) - @wtgtybhertgeghgtwtg).

# 2.0.0 - 2015-09-08

- Added: compatibility with postcss v5.x
- Removed: compatiblity with postcss v4.x

# 1.2.0 - 2015-08-13

- Added: compatibility with postcss v4.1.x
([#4](https://github.com/postcss/postcss-color-rebeccapurple/pull/4))

# 1.1.0 - 2014-11-25

- Enhanced exceptions

# 1.0.0 - 2014-10-04

Initial release from [postcss-color](https://github.com/postcss/postcss-color)
