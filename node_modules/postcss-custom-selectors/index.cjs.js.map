{"version": 3, "file": "index.cjs.js", "sources": ["lib/selectors-ast-from-selectors-string.js", "lib/custom-selectors-from-root.js", "lib/transform-selectors-by-custom-selectors.js", "lib/transform-rules.js", "lib/import-from.js", "lib/export-to.js", "index.js"], "sourcesContent": ["import parser from 'postcss-selector-parser';\n\n/* Return a Selectors AST from a Selectors String\n/* ========================================================================== */\n\nexport default selectorString => {\n\tlet selectorAST;\n\n\tparser(selectors => {\n\t\tselectorAST = selectors\n\t}).processSync(selectorString);\n\n\treturn selectorAST;\n};\n", "import getASTFromSelectors from './selectors-ast-from-selectors-string';\n\n// return custom selectors from the css root, conditionally removing them\nexport default (root, opts) => {\n\t// initialize custom selectors\n\tconst customSelectors = {};\n\n\t// for each custom selector atrule that is a child of the css root\n\troot.nodes.slice().forEach(node => {\n\t\tif (isCustomSelector(node)) {\n\t\t\t// extract the name and selectors from the params of the custom selector\n\t\t\tconst [, name, selectors] = node.params.match(customSelectorParamsRegExp);\n\n\t\t\t// write the parsed selectors to the custom selector\n\t\t\tcustomSelectors[name] = getASTFromSelectors(selectors);\n\n\t\t\t// conditionally remove the custom selector atrule\n\t\t\tif (!Object(opts).preserve) {\n\t\t\t\tnode.remove();\n\t\t\t}\n\t\t}\n\t});\n\n\treturn customSelectors;\n};\n\n// match the custom selector name\nconst customSelectorNameRegExp = /^custom-selector$/i;\n\n// match the custom selector params\nconst customSelectorParamsRegExp = /^(:--[A-z][\\w-]*)\\s+([\\W\\w]+)\\s*$/;\n\n// whether the atrule is a custom selector\nconst isCustomSelector = node => node.type === 'atrule' && customSelectorNameRegExp.test(node.name) && customSelectorParamsRegExp.test(node.params);\n", "// return transformed selectors, replacing custom pseudo selectors with custom selectors\nexport default function transformSelectorList(selectorList, customSelectors) {\n\tlet index = selectorList.nodes.length - 1;\n\n\twhile (index >= 0) {\n\t\tconst transformedSelectors = transformSelector(selectorList.nodes[index], customSelectors);\n\n\t\tif (transformedSelectors.length) {\n\t\t\tselectorList.nodes.splice(index, 1, ...transformedSelectors);\n\t\t}\n\n\t\t--index;\n\t}\n\n\treturn selectorList;\n}\n\n// return custom pseudo selectors replaced with custom selectors\nfunction transformSelector(selector, customSelectors) {\n\tconst transpiledSelectors = [];\n\n\tfor (const index in selector.nodes) {\n\t\tconst { value, nodes } = selector.nodes[index];\n\n\t\tif (value in customSelectors) {\n\t\t\tfor (const replacementSelector of customSelectors[value].nodes) {\n\t\t\t\tconst selectorClone = selector.clone();\n\n\t\t\t\tselectorClone.nodes.splice(index, 1, ...replacementSelector.clone().nodes.map(node => {\n\t\t\t\t\t// use spacing from the current usage\n\t\t\t\t\tnode.spaces = { ...selector.nodes[index].spaces };\n\n\t\t\t\t\treturn node;\n\t\t\t\t}));\n\n\t\t\t\tconst retranspiledSelectors = transformSelector(selectorClone, customSelectors);\n\n\t\t\t\tadjustNodesBySelectorEnds(selectorClone.nodes, Number(index));\n\n\t\t\t\tif (retranspiledSelectors.length) {\n\t\t\t\t\ttranspiledSelectors.push(...retranspiledSelectors);\n\t\t\t\t} else {\n\t\t\t\t\ttranspiledSelectors.push(selectorClone);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn transpiledSelectors;\n\t\t} else if (nodes && nodes.length) {\n\t\t\ttransformSelectorList(selector.nodes[index], customSelectors);\n\t\t}\n\t}\n\n\treturn transpiledSelectors;\n}\n\n// match selectors by difficult-to-separate ends\nconst withoutSelectorStartMatch = /^(tag|universal)$/;\nconst withoutSelectorEndMatch = /^(class|id|pseudo|tag|universal)$/;\n\nconst isWithoutSelectorStart = node => withoutSelectorStartMatch.test(Object(node).type);\nconst isWithoutSelectorEnd = node => withoutSelectorEndMatch.test(Object(node).type);\n\n// adjust nodes by selector ends (so that .class:--h1 becomes h1.class rather than .classh1)\nconst adjustNodesBySelectorEnds = (nodes, index) => {\n\tif (index && isWithoutSelectorStart(nodes[index]) && isWithoutSelectorEnd(nodes[index - 1])) {\n\t\tlet safeIndex = index - 1;\n\n\t\twhile (safeIndex && isWithoutSelectorEnd(nodes[safeIndex])) {\n\t\t\t--safeIndex;\n\t\t}\n\n\t\tif (safeIndex < index) {\n\t\t\tconst node = nodes.splice(index, 1)[0];\n\n\t\t\tnodes.splice(safeIndex, 0, node);\n\n\t\t\tnodes[safeIndex].spaces.before = nodes[safeIndex + 1].spaces.before;\n\t\t\tnodes[safeIndex + 1].spaces.before = '';\n\n\t\t\tif (nodes[index]) {\n\t\t\t\tnodes[index].spaces.after = nodes[safeIndex].spaces.after;\n\t\t\t\tnodes[safeIndex].spaces.after = '';\n\t\t\t}\n\t\t}\n\t}\n};\n\n", "import parser from 'postcss-selector-parser';\nimport transformSelectorsByCustomSelectors from './transform-selectors-by-custom-selectors';\n\n// transform custom pseudo selectors with custom selectors\nexport default (root, customSelectors, opts) => {\n\troot.walkRules(customPseudoRegExp, rule => {\n\t\tconst selector = parser(selectors => {\n\t\t\ttransformSelectorsByCustomSelectors(selectors, customSelectors, opts)\n\t\t}).processSync(rule.selector);\n\n\t\tif (opts.preserve) {\n\t\t\trule.cloneBefore({ selector });\n\t\t} else {\n\t\t\trule.selector = selector;\n\t\t}\n\t});\n};\n\nconst customPseudoRegExp = /:--[A-z][\\w-]*/;\n", "import fs from 'fs';\nimport path from 'path';\nimport postcss from 'postcss';\nimport getSelectorsAstFromSelectorsString from './selectors-ast-from-selectors-string';\nimport getCustomSelectors from './custom-selectors-from-root';\n\n/* Import Custom Selectors from CSS AST\n/* ========================================================================== */\n\nfunction importCustomSelectorsFromCSSAST(root) {\n\treturn getCustomSelectors(root);\n}\n\n/* Import Custom Selectors from CSS File\n/* ========================================================================== */\n\nasync function importCustomSelectorsFromCSSFile(from) {\n\tconst css = await readFile(path.resolve(from));\n\tconst root = postcss.parse(css, { from: path.resolve(from) });\n\n\treturn importCustomSelectorsFromCSSAST(root);\n}\n\n/* Import Custom Selectors from Object\n/* ========================================================================== */\n\nfunction importCustomSelectorsFromObject(object) {\n\tconst customSelectors = Object.assign(\n\t\t{},\n\t\tObject(object).customSelectors || Object(object)['custom-selectors']\n\t);\n\n\tfor (const key in customSelectors) {\n\t\tcustomSelectors[key] = getSelectorsAstFromSelectorsString(customSelectors[key]);\n\t}\n\n\treturn customSelectors;\n}\n\n/* Import Custom Selectors from JSON file\n/* ========================================================================== */\n\nasync function importCustomSelectorsFromJSONFile(from) {\n\tconst object = await readJSON(path.resolve(from));\n\n\treturn importCustomSelectorsFromObject(object);\n}\n\n/* Import Custom Selectors from JS file\n/* ========================================================================== */\n\nasync function importCustomSelectorsFromJSFile(from) {\n\tconst object = await import(path.resolve(from));\n\n\treturn importCustomSelectorsFromObject(object);\n}\n\n/* Import Custom Selectors from Sources\n/* ========================================================================== */\n\nexport default function importCustomSelectorsFromSources(sources) {\n\treturn sources.map(source => {\n\t\tif (source instanceof Promise) {\n\t\t\treturn source;\n\t\t} else if (source instanceof Function) {\n\t\t\treturn source();\n\t\t}\n\n\t\t// read the source as an object\n\t\tconst opts = source === Object(source) ? source : { from: String(source) };\n\n\t\t// skip objects with custom selectors\n\t\tif (Object(opts).customSelectors || Object(opts)['custom-selectors']) {\n\t\t\treturn opts\n\t\t}\n\n\t\t// source pathname\n\t\tconst from = String(opts.from || '');\n\n\t\t// type of file being read from\n\t\tconst type = (opts.type || path.extname(from).slice(1)).toLowerCase();\n\n\t\treturn { type, from };\n\t}).reduce(async (customSelectors, source) => {\n\t\tconst { type, from } = await source;\n\n\t\tif (type === 'ast') {\n\t\t\treturn Object.assign(customSelectors, importCustomSelectorsFromCSSAST(from));\n\t\t}\n\n\t\tif (type === 'css') {\n\t\t\treturn Object.assign(customSelectors, await importCustomSelectorsFromCSSFile(from));\n\t\t}\n\n\t\tif (type === 'js') {\n\t\t\treturn Object.assign(customSelectors, await importCustomSelectorsFromJSFile(from));\n\t\t}\n\n\t\tif (type === 'json') {\n\t\t\treturn Object.assign(customSelectors, await importCustomSelectorsFromJSONFile(from));\n\t\t}\n\n\t\treturn Object.assign(customSelectors, importCustomSelectorsFromObject(await source));\n\t}, {});\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst readFile = from => new Promise((resolve, reject) => {\n\tfs.readFile(from, 'utf8', (error, result) => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve(result);\n\t\t}\n\t});\n});\n\nconst readJSON = async from => JSON.parse(await readFile(from));\n", "import fs from 'fs';\nimport path from 'path';\n\n/* Import Custom Selectors from CSS File\n/* ========================================================================== */\n\nasync function exportCustomSelectorsToCssFile(to, customSelectors) {\n\tconst cssContent = Object.keys(customSelectors).reduce((cssLines, name) => {\n\t\tcssLines.push(`@custom-selector ${name} ${customSelectors[name]};`);\n\n\t\treturn cssLines;\n\t}, []).join('\\n');\n\tconst css = `${cssContent}\\n`;\n\n\tawait writeFile(to, css);\n}\n\n/* Import Custom Selectors from JSON file\n/* ========================================================================== */\n\nasync function exportCustomSelectorsToJsonFile(to, customSelectors) {\n\tconst jsonContent = JSON.stringify({\n\t\t'custom-selectors': customSelectors\n\t}, null, '  ');\n\tconst json = `${jsonContent}\\n`;\n\n\tawait writeFile(to, json);\n}\n\n/* Import Custom Selectors from Common JS file\n/* ========================================================================== */\n\nasync function exportCustomSelectorsToCjsFile(to, customSelectors) {\n\tconst jsContents = Object.keys(customSelectors).reduce((jsLines, name) => {\n\t\tjsLines.push(`\\t\\t'${escapeForJS(name)}': '${escapeForJS(customSelectors[name])}'`);\n\n\t\treturn jsLines;\n\t}, []).join(',\\n');\n\tconst js = `module.exports = {\\n\\tcustomSelectors: {\\n${jsContents}\\n\\t}\\n};\\n`;\n\n\tawait writeFile(to, js);\n}\n\n/* Import Custom Selectors from Module JS file\n/* ========================================================================== */\n\nasync function exportCustomSelectorsToMjsFile(to, customSelectors) {\n\tconst mjsContents = Object.keys(customSelectors).reduce((mjsLines, name) => {\n\t\tmjsLines.push(`\\t'${escapeForJS(name)}': '${escapeForJS(customSelectors[name])}'`);\n\n\t\treturn mjsLines;\n\t}, []).join(',\\n');\n\tconst mjs = `export const customSelectors = {\\n${mjsContents}\\n};\\n`;\n\n\tawait writeFile(to, mjs);\n}\n\n/* Export Custom Selectors to Destinations\n/* ========================================================================== */\n\nexport default function exportCustomSelectorsToDestinations(customSelectors, destinations) {\n\treturn Promise.all(destinations.map(async destination => {\n\t\tif (destination instanceof Function) {\n\t\t\tawait destination(defaultCustomSelectorsToJSON(customSelectors));\n\t\t} else {\n\t\t\t// read the destination as an object\n\t\t\tconst opts = destination === Object(destination) ? destination : { to: String(destination) };\n\n\t\t\t// transformer for custom selectors into a JSON-compatible object\n\t\t\tconst toJSON = opts.toJSON || defaultCustomSelectorsToJSON;\n\n\t\t\tif ('customSelectors' in opts) {\n\t\t\t\t// write directly to an object as customSelectors\n\t\t\t\topts.customSelectors = toJSON(customSelectors);\n\t\t\t} else if ('custom-selectors' in opts) {\n\t\t\t\t// write directly to an object as custom-selectors\n\t\t\t\topts['custom-selectors'] = toJSON(customSelectors);\n\t\t\t} else {\n\t\t\t\t// destination pathname\n\t\t\t\tconst to = String(opts.to || '');\n\n\t\t\t\t// type of file being written to\n\t\t\t\tconst type = (opts.type || path.extname(opts.to).slice(1)).toLowerCase();\n\n\t\t\t\t// transformed custom selectors\n\t\t\t\tconst customSelectorsJSON = toJSON(customSelectors);\n\n\t\t\t\tif (type === 'css') {\n\t\t\t\t\tawait exportCustomSelectorsToCssFile(to, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'js') {\n\t\t\t\t\tawait exportCustomSelectorsToCjsFile(to, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'json') {\n\t\t\t\t\tawait exportCustomSelectorsToJsonFile(to, customSelectorsJSON);\n\t\t\t\t}\n\n\t\t\t\tif (type === 'mjs') {\n\t\t\t\t\tawait exportCustomSelectorsToMjsFile(to, customSelectorsJSON);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}));\n}\n\n/* Helper utilities\n/* ========================================================================== */\n\nconst defaultCustomSelectorsToJSON = customSelectors => {\n\treturn Object.keys(customSelectors).reduce((customSelectorsJSON, key) => {\n\t\tcustomSelectorsJSON[key] = String(customSelectors[key]);\n\n\t\treturn customSelectorsJSON;\n\t}, {});\n};\n\nconst writeFile = (to, text) => new Promise((resolve, reject) => {\n\tfs.writeFile(to, text, error => {\n\t\tif (error) {\n\t\t\treject(error);\n\t\t} else {\n\t\t\tresolve();\n\t\t}\n\t});\n});\n\nconst escapeForJS = string => string.replace(/\\\\([\\s\\S])|(')/g, '\\\\$1$2').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n", "import postcss from 'postcss';\nimport getCustomSelectors from './lib/custom-selectors-from-root';\nimport transformRules from './lib/transform-rules';\nimport importCustomSelectorsFromSources from './lib/import-from';\nimport exportCustomSelectorsToDestinations from './lib/export-to';\n\nexport default postcss.plugin('postcss-custom-selectors', opts => {\n\t// whether to preserve custom selectors and rules using them\n\tconst preserve = Boolean(Object(opts).preserve);\n\n\t// sources to import custom selectors from\n\tconst importFrom = [].concat(Object(opts).importFrom || []);\n\n\t// destinations to export custom selectors to\n\tconst exportTo = [].concat(Object(opts).exportTo || []);\n\n\t// promise any custom selectors are imported\n\tconst customSelectorsPromise = importCustomSelectorsFromSources(importFrom);\n\n\treturn async root => {\n\t\tconst customProperties = Object.assign(\n\t\t\tawait customSelectorsPromise,\n\t\t\tgetCustomSelectors(root, { preserve })\n\t\t);\n\n\t\tawait exportCustomSelectorsToDestinations(customProperties, exportTo);\n\n\t\ttransformRules(root, customProperties, { preserve });\n\t};\n});\n"], "names": ["selectorString", "selectorAST", "parser", "selectors", "processSync", "root", "opts", "customSelectors", "nodes", "slice", "for<PERSON>ach", "node", "isCustomSelector", "params", "match", "customSelectorParamsRegExp", "name", "getASTFromSelectors", "Object", "preserve", "remove", "customSelectorNameRegExp", "type", "test", "transformSelectorList", "selectorList", "index", "length", "transformedSelectors", "transformSelector", "splice", "selector", "transpiledSelectors", "value", "replacementSelector", "selectorClone", "clone", "map", "spaces", "retranspiledSelectors", "adjustNodesBySelectorEnds", "Number", "push", "withoutSelectorStartMatch", "withoutSelectorEndMatch", "isWithoutSelectorStart", "isWithoutSelectorEnd", "safeIndex", "before", "after", "walkRules", "customPseudoRegExp", "rule", "transformSelectorsByCustomSelectors", "cloneBefore", "importCustomSelectorsFromCSSAST", "getCustomSelectors", "importCustomSelectorsFromCSSFile", "from", "css", "readFile", "path", "resolve", "postcss", "parse", "importCustomSelectorsFromObject", "object", "assign", "key", "getSelectorsAstFromSelectorsString", "importCustomSelectorsFromJSONFile", "readJSON", "importCustomSelectorsFromJSFile", "importCustomSelectorsFromSources", "sources", "source", "Promise", "Function", "String", "extname", "toLowerCase", "reduce", "reject", "fs", "error", "result", "JSON", "exportCustomSelectorsToCssFile", "to", "cssContent", "keys", "cssLines", "join", "writeFile", "exportCustomSelectorsToJsonFile", "json<PERSON><PERSON><PERSON>", "stringify", "json", "exportCustomSelectorsToCjsFile", "js<PERSON><PERSON>nts", "jsLines", "escapeForJS", "js", "exportCustomSelectorsToMjsFile", "mjs<PERSON><PERSON><PERSON>", "mjsLines", "mjs", "exportCustomSelectorsToDestinations", "destinations", "all", "destination", "defaultCustomSelectorsToJSON", "toJSON", "customSelectorsJSON", "text", "string", "replace", "plugin", "Boolean", "importFrom", "concat", "exportTo", "customSelectorsPromise", "customProperties", "transformRules"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;AAGA,0CAAeA,cAAc,IAAI;MAC5BC,WAAJ;EAEAC,MAAM,CAACC,SAAS,IAAI;IACnBF,WAAW,GAAGE,SAAd;GADK,CAAN,CAEGC,WAFH,CAEeJ,cAFf;SAIOC,WAAP;CAPD;;ACFA,0BAAe,CAACI,IAAD,EAAOC,IAAP,KAAgB;;QAExBC,eAAe,GAAG,EAAxB,CAF8B;;EAK9BF,IAAI,CAACG,KAAL,CAAWC,KAAX,GAAmBC,OAAnB,CAA2BC,IAAI,IAAI;QAC9BC,gBAAgB,CAACD,IAAD,CAApB,EAA4B;;iCAECA,IAAI,CAACE,MAAL,CAAYC,KAAZ,CAAkBC,0BAAlB,CAFD;;YAElBC,IAFkB;YAEZb,SAFY;;;MAK3BI,eAAe,CAACS,IAAD,CAAf,GAAwBC,kCAAmB,CAACd,SAAD,CAA3C,CAL2B;;UAQvB,CAACe,MAAM,CAACZ,IAAD,CAAN,CAAaa,QAAlB,EAA4B;QAC3BR,IAAI,CAACS,MAAL;;;GAVH;SAeOb,eAAP;CApBD;;AAwBA,MAAMc,wBAAwB,GAAG,oBAAjC;;AAGA,MAAMN,0BAA0B,GAAG,mCAAnC;;AAGA,MAAMH,gBAAgB,GAAGD,IAAI,IAAIA,IAAI,CAACW,IAAL,KAAc,QAAd,IAA0BD,wBAAwB,CAACE,IAAzB,CAA8BZ,IAAI,CAACK,IAAnC,CAA1B,IAAsED,0BAA0B,CAACQ,IAA3B,CAAgCZ,IAAI,CAACE,MAArC,CAAvG;;ACjCA;AACA,AAAe,SAASW,qBAAT,CAA+BC,YAA/B,EAA6ClB,eAA7C,EAA8D;MACxEmB,KAAK,GAAGD,YAAY,CAACjB,KAAb,CAAmBmB,MAAnB,GAA4B,CAAxC;;SAEOD,KAAK,IAAI,CAAhB,EAAmB;UACZE,oBAAoB,GAAGC,iBAAiB,CAACJ,YAAY,CAACjB,KAAb,CAAmBkB,KAAnB,CAAD,EAA4BnB,eAA5B,CAA9C;;QAEIqB,oBAAoB,CAACD,MAAzB,EAAiC;MAChCF,YAAY,CAACjB,KAAb,CAAmBsB,MAAnB,CAA0BJ,KAA1B,EAAiC,CAAjC,EAAoC,GAAGE,oBAAvC;;;MAGCF,KAAF;;;SAGMD,YAAP;;;AAID,SAASI,iBAAT,CAA2BE,QAA3B,EAAqCxB,eAArC,EAAsD;QAC/CyB,mBAAmB,GAAG,EAA5B;;OAEK,MAAMN,KAAX,IAAoBK,QAAQ,CAACvB,KAA7B,EAAoC;kCACVuB,QAAQ,CAACvB,KAAT,CAAekB,KAAf,CADU;UAC3BO,KAD2B,yBAC3BA,KAD2B;UACpBzB,KADoB,yBACpBA,KADoB;;QAG/ByB,KAAK,IAAI1B,eAAb,EAA8B;;;;;;6BACKA,eAAe,CAAC0B,KAAD,CAAf,CAAuBzB,KAAzD,8HAAgE;gBAArD0B,mBAAqD;gBACzDC,aAAa,GAAGJ,QAAQ,CAACK,KAAT,EAAtB;UAEAD,aAAa,CAAC3B,KAAd,CAAoBsB,MAApB,CAA2BJ,KAA3B,EAAkC,CAAlC,EAAqC,GAAGQ,mBAAmB,CAACE,KAApB,GAA4B5B,KAA5B,CAAkC6B,GAAlC,CAAsC1B,IAAI,IAAI;;YAErFA,IAAI,CAAC2B,MAAL,qBAAmBP,QAAQ,CAACvB,KAAT,CAAekB,KAAf,EAAsBY,MAAzC;mBAEO3B,IAAP;WAJuC,CAAxC;gBAOM4B,qBAAqB,GAAGV,iBAAiB,CAACM,aAAD,EAAgB5B,eAAhB,CAA/C;UAEAiC,yBAAyB,CAACL,aAAa,CAAC3B,KAAf,EAAsBiC,MAAM,CAACf,KAAD,CAA5B,CAAzB;;cAEIa,qBAAqB,CAACZ,MAA1B,EAAkC;YACjCK,mBAAmB,CAACU,IAApB,CAAyB,GAAGH,qBAA5B;WADD,MAEO;YACNP,mBAAmB,CAACU,IAApB,CAAyBP,aAAzB;;;;;;;;;;;;;;;;;;aAIKH,mBAAP;KAtBD,MAuBO,IAAIxB,KAAK,IAAIA,KAAK,CAACmB,MAAnB,EAA2B;MACjCH,qBAAqB,CAACO,QAAQ,CAACvB,KAAT,CAAekB,KAAf,CAAD,EAAwBnB,eAAxB,CAArB;;;;SAIKyB,mBAAP;;;;AAID,MAAMW,yBAAyB,GAAG,mBAAlC;AACA,MAAMC,uBAAuB,GAAG,mCAAhC;;AAEA,MAAMC,sBAAsB,GAAGlC,IAAI,IAAIgC,yBAAyB,CAACpB,IAA1B,CAA+BL,MAAM,CAACP,IAAD,CAAN,CAAaW,IAA5C,CAAvC;;AACA,MAAMwB,oBAAoB,GAAGnC,IAAI,IAAIiC,uBAAuB,CAACrB,IAAxB,CAA6BL,MAAM,CAACP,IAAD,CAAN,CAAaW,IAA1C,CAArC;;;AAGA,MAAMkB,yBAAyB,GAAG,CAAChC,KAAD,EAAQkB,KAAR,KAAkB;MAC/CA,KAAK,IAAImB,sBAAsB,CAACrC,KAAK,CAACkB,KAAD,CAAN,CAA/B,IAAiDoB,oBAAoB,CAACtC,KAAK,CAACkB,KAAK,GAAG,CAAT,CAAN,CAAzE,EAA6F;QACxFqB,SAAS,GAAGrB,KAAK,GAAG,CAAxB;;WAEOqB,SAAS,IAAID,oBAAoB,CAACtC,KAAK,CAACuC,SAAD,CAAN,CAAxC,EAA4D;QACzDA,SAAF;;;QAGGA,SAAS,GAAGrB,KAAhB,EAAuB;YAChBf,IAAI,GAAGH,KAAK,CAACsB,MAAN,CAAaJ,KAAb,EAAoB,CAApB,EAAuB,CAAvB,CAAb;MAEAlB,KAAK,CAACsB,MAAN,CAAaiB,SAAb,EAAwB,CAAxB,EAA2BpC,IAA3B;MAEAH,KAAK,CAACuC,SAAD,CAAL,CAAiBT,MAAjB,CAAwBU,MAAxB,GAAiCxC,KAAK,CAACuC,SAAS,GAAG,CAAb,CAAL,CAAqBT,MAArB,CAA4BU,MAA7D;MACAxC,KAAK,CAACuC,SAAS,GAAG,CAAb,CAAL,CAAqBT,MAArB,CAA4BU,MAA5B,GAAqC,EAArC;;UAEIxC,KAAK,CAACkB,KAAD,CAAT,EAAkB;QACjBlB,KAAK,CAACkB,KAAD,CAAL,CAAaY,MAAb,CAAoBW,KAApB,GAA4BzC,KAAK,CAACuC,SAAD,CAAL,CAAiBT,MAAjB,CAAwBW,KAApD;QACAzC,KAAK,CAACuC,SAAD,CAAL,CAAiBT,MAAjB,CAAwBW,KAAxB,GAAgC,EAAhC;;;;CAlBJ;;AC3DA,sBAAe,CAAC5C,IAAD,EAAOE,eAAP,EAAwBD,IAAxB,KAAiC;EAC/CD,IAAI,CAAC6C,SAAL,CAAeC,kBAAf,EAAmCC,IAAI,IAAI;UACpCrB,QAAQ,GAAG7B,MAAM,CAACC,SAAS,IAAI;MACpCkD,qBAAmC,CAAClD,SAAD,EAAYI,eAAZ,EAA6BD,IAA7B,CAAnC;KADsB,CAAN,CAEdF,WAFc,CAEFgD,IAAI,CAACrB,QAFH,CAAjB;;QAIIzB,IAAI,CAACa,QAAT,EAAmB;MAClBiC,IAAI,CAACE,WAAL,CAAiB;QAAEvB;OAAnB;KADD,MAEO;MACNqB,IAAI,CAACrB,QAAL,GAAgBA,QAAhB;;GARF;CADD;AAcA,MAAMoB,kBAAkB,GAAG,gBAA3B;;ACZA;;;AAGA,SAASI,+BAAT,CAAyClD,IAAzC,EAA+C;SACvCmD,kBAAkB,CAACnD,IAAD,CAAzB;;;;;;SAMcoD;;;;;;;;wDAAf,WAAgDC,IAAhD,EAAsD;UAC/CC,GAAG,SAASC,QAAQ,CAACC,IAAI,CAACC,OAAL,CAAaJ,IAAb,CAAD,CAA1B;UACMrD,IAAI,GAAG0D,OAAO,CAACC,KAAR,CAAcL,GAAd,EAAmB;MAAED,IAAI,EAAEG,IAAI,CAACC,OAAL,CAAaJ,IAAb;KAA3B,CAAb;WAEOH,+BAA+B,CAAClD,IAAD,CAAtC;;;;;AAMD,SAAS4D,+BAAT,CAAyCC,MAAzC,EAAiD;QAC1C3D,eAAe,GAAGW,MAAM,CAACiD,MAAP,CACvB,EADuB,EAEvBjD,MAAM,CAACgD,MAAD,CAAN,CAAe3D,eAAf,IAAkCW,MAAM,CAACgD,MAAD,CAAN,CAAe,kBAAf,CAFX,CAAxB;;OAKK,MAAME,GAAX,IAAkB7D,eAAlB,EAAmC;IAClCA,eAAe,CAAC6D,GAAD,CAAf,GAAuBC,kCAAkC,CAAC9D,eAAe,CAAC6D,GAAD,CAAhB,CAAzD;;;SAGM7D,eAAP;;;;;;SAMc+D;;;;;;;;yDAAf,WAAiDZ,IAAjD,EAAuD;UAChDQ,MAAM,SAASK,QAAQ,CAACV,IAAI,CAACC,OAAL,CAAaJ,IAAb,CAAD,CAA7B;WAEOO,+BAA+B,CAACC,MAAD,CAAtC;;;;;SAMcM;;;;;;;;uDAAf,WAA+Cd,IAA/C,EAAqD;UAC9CQ,MAAM,SAAS,wBAAOL,IAAI,CAACC,OAAL,CAAaJ,IAAb,CAAP,EAArB;WAEOO,+BAA+B,CAACC,MAAD,CAAtC;;;;;AAMD,AAAe,SAASO,gCAAT,CAA0CC,OAA1C,EAAmD;SAC1DA,OAAO,CAACrC,GAAR,CAAYsC,MAAM,IAAI;QACxBA,MAAM,YAAYC,OAAtB,EAA+B;aACvBD,MAAP;KADD,MAEO,IAAIA,MAAM,YAAYE,QAAtB,EAAgC;aAC/BF,MAAM,EAAb;KAJ2B;;;UAQtBrE,IAAI,GAAGqE,MAAM,KAAKzD,MAAM,CAACyD,MAAD,CAAjB,GAA4BA,MAA5B,GAAqC;MAAEjB,IAAI,EAAEoB,MAAM,CAACH,MAAD;KAAhE,CAR4B;;QAWxBzD,MAAM,CAACZ,IAAD,CAAN,CAAaC,eAAb,IAAgCW,MAAM,CAACZ,IAAD,CAAN,CAAa,kBAAb,CAApC,EAAsE;aAC9DA,IAAP;KAZ2B;;;UAgBtBoD,IAAI,GAAGoB,MAAM,CAACxE,IAAI,CAACoD,IAAL,IAAa,EAAd,CAAnB,CAhB4B;;UAmBtBpC,IAAI,GAAG,CAAChB,IAAI,CAACgB,IAAL,IAAauC,IAAI,CAACkB,OAAL,CAAarB,IAAb,EAAmBjD,KAAnB,CAAyB,CAAzB,CAAd,EAA2CuE,WAA3C,EAAb;WAEO;MAAE1D,IAAF;MAAQoC;KAAf;GArBM,EAsBJuB,MAtBI;;;iCAsBG,WAAO1E,eAAP,EAAwBoE,MAAxB,EAAmC;0BACfA,MADe;YACpCrD,IADoC,SACpCA,IADoC;YAC9BoC,IAD8B,SAC9BA,IAD8B;;UAGxCpC,IAAI,KAAK,KAAb,EAAoB;eACZJ,MAAM,CAACiD,MAAP,CAAc5D,eAAd,EAA+BgD,+BAA+B,CAACG,IAAD,CAA9D,CAAP;;;UAGGpC,IAAI,KAAK,KAAb,EAAoB;eACZJ,MAAM,CAACiD,MAAP,CAAc5D,eAAd,SAAqCkD,gCAAgC,CAACC,IAAD,CAArE,EAAP;;;UAGGpC,IAAI,KAAK,IAAb,EAAmB;eACXJ,MAAM,CAACiD,MAAP,CAAc5D,eAAd,SAAqCiE,+BAA+B,CAACd,IAAD,CAApE,EAAP;;;UAGGpC,IAAI,KAAK,MAAb,EAAqB;eACbJ,MAAM,CAACiD,MAAP,CAAc5D,eAAd,SAAqC+D,iCAAiC,CAACZ,IAAD,CAAtE,EAAP;;;aAGMxC,MAAM,CAACiD,MAAP,CAAc5D,eAAd,EAA+B0D,+BAA+B,QAAOU,MAAP,EAA9D,CAAP;KAzCM;;;;;OA0CJ,EA1CI,CAAP;;;;;AAgDD,MAAMf,QAAQ,GAAGF,IAAI,IAAI,IAAIkB,OAAJ,CAAY,CAACd,OAAD,EAAUoB,MAAV,KAAqB;EACzDC,EAAE,CAACvB,QAAH,CAAYF,IAAZ,EAAkB,MAAlB,EAA0B,CAAC0B,KAAD,EAAQC,MAAR,KAAmB;QACxCD,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNtB,OAAO,CAACuB,MAAD,CAAP;;GAJF;CADwB,CAAzB;;AAUA,MAAMd,QAAQ;;AAAA;gCAAG,WAAMb,IAAN;WAAc4B,IAAI,CAACtB,KAAL,QAAiBJ,QAAQ,CAACF,IAAD,CAAzB,EAAd;GAAH;;kBAARa,QAAQ;;;GAAd;;ACpHA;;;SAGegB;;;;;;;;sDAAf,WAA8CC,EAA9C,EAAkDjF,eAAlD,EAAmE;UAC5DkF,UAAU,GAAGvE,MAAM,CAACwE,IAAP,CAAYnF,eAAZ,EAA6B0E,MAA7B,CAAoC,CAACU,QAAD,EAAW3E,IAAX,KAAoB;MAC1E2E,QAAQ,CAACjD,IAAT,CAAe,oBAAmB1B,IAAK,IAAGT,eAAe,CAACS,IAAD,CAAO,GAAhE;aAEO2E,QAAP;KAHkB,EAIhB,EAJgB,EAIZC,IAJY,CAIP,IAJO,CAAnB;UAKMjC,GAAG,GAAI,GAAE8B,UAAW,IAA1B;UAEMI,SAAS,CAACL,EAAD,EAAK7B,GAAL,CAAf;;;;;SAMcmC;;;;;;;;uDAAf,WAA+CN,EAA/C,EAAmDjF,eAAnD,EAAoE;UAC7DwF,WAAW,GAAGT,IAAI,CAACU,SAAL,CAAe;0BACdzF;KADD,EAEjB,IAFiB,EAEX,IAFW,CAApB;UAGM0F,IAAI,GAAI,GAAEF,WAAY,IAA5B;UAEMF,SAAS,CAACL,EAAD,EAAKS,IAAL,CAAf;;;;;SAMcC;;;;;;;;sDAAf,WAA8CV,EAA9C,EAAkDjF,eAAlD,EAAmE;UAC5D4F,UAAU,GAAGjF,MAAM,CAACwE,IAAP,CAAYnF,eAAZ,EAA6B0E,MAA7B,CAAoC,CAACmB,OAAD,EAAUpF,IAAV,KAAmB;MACzEoF,OAAO,CAAC1D,IAAR,CAAc,QAAO2D,WAAW,CAACrF,IAAD,CAAO,OAAMqF,WAAW,CAAC9F,eAAe,CAACS,IAAD,CAAhB,CAAwB,GAAhF;aAEOoF,OAAP;KAHkB,EAIhB,EAJgB,EAIZR,IAJY,CAIP,KAJO,CAAnB;UAKMU,EAAE,GAAI,6CAA4CH,UAAW,aAAnE;UAEMN,SAAS,CAACL,EAAD,EAAKc,EAAL,CAAf;;;;;SAMcC;;;;;;;;sDAAf,WAA8Cf,EAA9C,EAAkDjF,eAAlD,EAAmE;UAC5DiG,WAAW,GAAGtF,MAAM,CAACwE,IAAP,CAAYnF,eAAZ,EAA6B0E,MAA7B,CAAoC,CAACwB,QAAD,EAAWzF,IAAX,KAAoB;MAC3EyF,QAAQ,CAAC/D,IAAT,CAAe,MAAK2D,WAAW,CAACrF,IAAD,CAAO,OAAMqF,WAAW,CAAC9F,eAAe,CAACS,IAAD,CAAhB,CAAwB,GAA/E;aAEOyF,QAAP;KAHmB,EAIjB,EAJiB,EAIbb,IAJa,CAIR,KAJQ,CAApB;UAKMc,GAAG,GAAI,qCAAoCF,WAAY,QAA7D;UAEMX,SAAS,CAACL,EAAD,EAAKkB,GAAL,CAAf;;;;;AAMD,AAAe,SAASC,mCAAT,CAA6CpG,eAA7C,EAA8DqG,YAA9D,EAA4E;SACnFhC,OAAO,CAACiC,GAAR,CAAYD,YAAY,CAACvE,GAAb;;;iCAAiB,WAAMyE,WAAN,EAAqB;UACpDA,WAAW,YAAYjC,QAA3B,EAAqC;cAC9BiC,WAAW,CAACC,4BAA4B,CAACxG,eAAD,CAA7B,CAAjB;OADD,MAEO;;cAEAD,IAAI,GAAGwG,WAAW,KAAK5F,MAAM,CAAC4F,WAAD,CAAtB,GAAsCA,WAAtC,GAAoD;UAAEtB,EAAE,EAAEV,MAAM,CAACgC,WAAD;SAA7E,CAFM;;cAKAE,MAAM,GAAG1G,IAAI,CAAC0G,MAAL,IAAeD,4BAA9B;;YAEI,qBAAqBzG,IAAzB,EAA+B;;UAE9BA,IAAI,CAACC,eAAL,GAAuByG,MAAM,CAACzG,eAAD,CAA7B;SAFD,MAGO,IAAI,sBAAsBD,IAA1B,EAAgC;;UAEtCA,IAAI,CAAC,kBAAD,CAAJ,GAA2B0G,MAAM,CAACzG,eAAD,CAAjC;SAFM,MAGA;;gBAEAiF,EAAE,GAAGV,MAAM,CAACxE,IAAI,CAACkF,EAAL,IAAW,EAAZ,CAAjB,CAFM;;gBAKAlE,IAAI,GAAG,CAAChB,IAAI,CAACgB,IAAL,IAAauC,IAAI,CAACkB,OAAL,CAAazE,IAAI,CAACkF,EAAlB,EAAsB/E,KAAtB,CAA4B,CAA5B,CAAd,EAA8CuE,WAA9C,EAAb,CALM;;gBAQAiC,mBAAmB,GAAGD,MAAM,CAACzG,eAAD,CAAlC;;cAEIe,IAAI,KAAK,KAAb,EAAoB;kBACbiE,8BAA8B,CAACC,EAAD,EAAKyB,mBAAL,CAApC;;;cAGG3F,IAAI,KAAK,IAAb,EAAmB;kBACZ4E,8BAA8B,CAACV,EAAD,EAAKyB,mBAAL,CAApC;;;cAGG3F,IAAI,KAAK,MAAb,EAAqB;kBACdwE,+BAA+B,CAACN,EAAD,EAAKyB,mBAAL,CAArC;;;cAGG3F,IAAI,KAAK,KAAb,EAAoB;kBACbiF,8BAA8B,CAACf,EAAD,EAAKyB,mBAAL,CAApC;;;;KAvCe;;;;;MAAZ,CAAP;;;;;AAiDD,MAAMF,4BAA4B,GAAGxG,eAAe,IAAI;SAChDW,MAAM,CAACwE,IAAP,CAAYnF,eAAZ,EAA6B0E,MAA7B,CAAoC,CAACgC,mBAAD,EAAsB7C,GAAtB,KAA8B;IACxE6C,mBAAmB,CAAC7C,GAAD,CAAnB,GAA2BU,MAAM,CAACvE,eAAe,CAAC6D,GAAD,CAAhB,CAAjC;WAEO6C,mBAAP;GAHM,EAIJ,EAJI,CAAP;CADD;;AAQA,MAAMpB,SAAS,GAAG,CAACL,EAAD,EAAK0B,IAAL,KAAc,IAAItC,OAAJ,CAAY,CAACd,OAAD,EAAUoB,MAAV,KAAqB;EAChEC,EAAE,CAACU,SAAH,CAAaL,EAAb,EAAiB0B,IAAjB,EAAuB9B,KAAK,IAAI;QAC3BA,KAAJ,EAAW;MACVF,MAAM,CAACE,KAAD,CAAN;KADD,MAEO;MACNtB,OAAO;;GAJT;CAD+B,CAAhC;;AAUA,MAAMuC,WAAW,GAAGc,MAAM,IAAIA,MAAM,CAACC,OAAP,CAAe,iBAAf,EAAkC,QAAlC,EAA4CA,OAA5C,CAAoD,KAApD,EAA2D,KAA3D,EAAkEA,OAAlE,CAA0E,KAA1E,EAAiF,KAAjF,CAA9B;;AC1HA,YAAerD,OAAO,CAACsD,MAAR,CAAe,0BAAf,EAA2C/G,IAAI,IAAI;;QAE3Da,QAAQ,GAAGmG,OAAO,CAACpG,MAAM,CAACZ,IAAD,CAAN,CAAaa,QAAd,CAAxB,CAFiE;;QAK3DoG,UAAU,GAAG,GAAGC,MAAH,CAAUtG,MAAM,CAACZ,IAAD,CAAN,CAAaiH,UAAb,IAA2B,EAArC,CAAnB,CALiE;;QAQ3DE,QAAQ,GAAG,GAAGD,MAAH,CAAUtG,MAAM,CAACZ,IAAD,CAAN,CAAamH,QAAb,IAAyB,EAAnC,CAAjB,CARiE;;QAW3DC,sBAAsB,GAAGjD,gCAAgC,CAAC8C,UAAD,CAA/D;;;;mCAEO,WAAMlH,IAAN,EAAc;cACdsH,gBAAgB,GAAGzG,MAAM,CAACiD,MAAP,QAClBuD,sBADkB,GAExBlE,kBAAkB,CAACnD,IAAD,EAAO;UAAEc;SAAT,CAFM,CAAzB;cAKMwF,mCAAmC,CAACgB,gBAAD,EAAmBF,QAAnB,CAAzC;QAEAG,cAAc,CAACvH,IAAD,EAAOsH,gBAAP,EAAyB;UAAExG;SAA3B,CAAd;OARD;;;;;;;CAbc,CAAf;;;;"}