# Change Log
This project adheres to [Semantic Versioning](http://semver.org/).

## 4.0.2
* Fix parsing `:;`.

## 4.0.1
* Remove development file from npm package.

## 4.0
* Remove Node.js 9 and Node.js 4 support.
* Remove IE and “dead” browsers from Babel.
* Use PostCSS 7.0.

## 3.0.1
* Fix parsing IE filter with missed semicolon.

## 3.0
* Use PostCSS 6.0.
* Use `babel-preset-env`.

## 2.0.1
* Do not fall on colon instead of semicolon in the declaration end.

## 2.0
* Use PostCSS 5.2 core tokenizer.

## 1.0.7
* Parse new lines according W3C CSS syntax specification.

## 1.0.6
* Fix package dependencies.

## 1.0.5
* Fix CSS syntax error position on unclosed quotes.

## 1.0.4
* Fix wrong `main` in `package.json`.

## 1.0.3
* Remove Babel from dependencies (by <PERSON><PERSON>).

## 1.0.2
* Clean code for PostCSS 5.0.13 internal changes.
* Use Babel 6.

## 1.0.1
* Fix `url()` parsing.

## 1.0
* Initial release from PostCSS sources.
